import{r as B,w as H,x as b,o as m,p as v,l,i as e,D as u,j as o,k as M,t as C,c as F,q as x,F as q,K as w,O as J,S as Q,s as X,a$ as Y}from"./index-CX4J5aM5.js";import{q as _}from"./gen-BRdzRWqq.js";const h={key:0},c={style:{float:"left"}},ee={style:{float:"right"}},le={style:{float:"left"}},te={style:{float:"right"}},ne={style:{float:"left"}},ae={style:{float:"right"}},ue={style:{float:"left"}},oe={style:{float:"right"}},de=X({name:"genInfoForm"}),ie=Object.assign(de,{props:{info:{type:Object,default:null},tables:{type:Array,default:null},columns:{type:Array,default:[]}},setup(a){const A=B([]),R=B([]),U=a,j=B({tplCategory:[{required:!0,message:"请选择生成模板",trigger:"blur"}],moduleName:[{required:!0,message:"请输入生成模块名",trigger:"blur",pattern:/^[A-Za-z]+$/}],businessName:[{required:!0,message:"请输入生成业务名",trigger:"blur",pattern:/^[A-Za-z]+$/}],functionName:[{required:!0,message:"请输入生成功能名",trigger:"blur"}],permissionPrefix:{required:!0,message:"请输入权限前缀",trigger:"blur"},genType:[{required:!0,message:"请选择代码生成方式",trigger:"blur"}],treeCode:[{required:!0,message:"请选择树编码字段",trigger:"blur"}],treeParentCode:[{required:!0,message:"请选择树父编码字段",trigger:"blur"}],subTableName:[{required:!0,message:"请选择关联的子表表名",trigger:"blur"}],subTableFkName:[{required:!0,message:"请选择子表关联的外键名",trigger:"blur"}]});function z(g){U.info.subTableFkName=""}function L(g){g!=="sub"&&(U.info.subTableName="",U.info.subTableFkName="")}function O(g){g==null&&(U.info.parentMenuId=0)}function Z(g){if(!(g==null||g==null||g==""))for(var t in U.tables){const p=U.tables[t];if(g===p.tableName){_(p.tableId).then(N=>{N.code==200&&(A.value=N.data.columns)});break}}}function $(){Y({menuTypeIds:"M,C"}).then(g=>{R.value=g.data})}function D(g){console.log(g)}return H(()=>U.info.subTableName,g=>{Z(g)}),$(),(g,t)=>{const p=b("el-option"),N=b("el-select"),f=b("el-form-item"),d=b("el-col"),i=b("question-filled"),r=b("el-icon"),s=b("el-tooltip"),S=b("el-input"),E=b("el-cascader"),V=b("el-radio"),I=b("el-radio-group"),K=b("el-switch"),k=b("el-tag"),T=b("el-checkbox"),W=b("el-checkbox-group"),P=b("el-row"),G=b("el-form");return m(),v(G,{ref:"genInfoForm",model:a.info,rules:M(j),"label-width":"150px"},{default:l(()=>[e(P,null,{default:l(()=>[e(d,{lg:12},{default:l(()=>[e(f,{prop:"tplCategory"},{label:l(()=>t[30]||(t[30]=[u("生成模板")])),default:l(()=>[e(N,{modelValue:a.info.tplCategory,"onUpdate:modelValue":t[0]||(t[0]=n=>a.info.tplCategory=n),onChange:L},{default:l(()=>[e(p,{label:"单表（增删改查）",value:"crud"}),e(p,{label:"树表（增删改查）",value:"tree"}),e(p,{label:"主子表（增删改查）",value:"subNavMore"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"frontTpl"},{label:l(()=>t[31]||(t[31]=[u("前端模板")])),default:l(()=>[e(N,{modelValue:a.info.frontTpl,"onUpdate:modelValue":t[1]||(t[1]=n=>a.info.frontTpl=n)},{default:l(()=>[e(p,{label:"Vue2 element ui",value:1}),e(p,{label:"Vue3 element plus",value:2}),e(p,{label:"Ant design",value:3})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"baseNameSpace"},{label:l(()=>[t[32]||(t[32]=u(" 生成命名空间前缀 ")),o("span",null,[e(s,{content:"比如 ZR.",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.baseNameSpace,"onUpdate:modelValue":t[2]||(t[2]=n=>a.info.baseNameSpace=n)},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"moduleName"},{label:l(()=>[t[33]||(t[33]=u(" 生成模块名 ")),o("span",null,[e(s,{content:"可理解为子系统名，例如 system、user、tool（一般文件夹归类）",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.moduleName,"onUpdate:modelValue":t[3]||(t[3]=n=>a.info.moduleName=n),"auto-complete":""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"businessName"},{label:l(()=>[t[34]||(t[34]=u(" 生成业务名 ")),o("span",null,[e(s,{content:"可理解为功能英文名，例如 user",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.businessName,"onUpdate:modelValue":t[4]||(t[4]=n=>a.info.businessName=n)},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"functionName"},{label:l(()=>[t[35]||(t[35]=u(" 生成功能名 ")),o("span",null,[e(s,{content:"用作类描述，例如 用户,代码生成,文章系统",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.functionName,"onUpdate:modelValue":t[5]||(t[5]=n=>a.info.functionName=n)},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,null,{label:l(()=>[t[36]||(t[36]=u(" 上级菜单 ")),o("span",null,[e(s,{content:"分配到指定菜单下，例如 系统管理",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(E,{class:"w100",options:M(R),props:{checkStrictly:!0,value:"menuId",label:"menuName",emitPath:!1},placeholder:"请选择上级菜单",clearable:"",onChange:t[6]||(t[6]=n=>O(n)),modelValue:a.info.parentMenuId,"onUpdate:modelValue":t[7]||(t[7]=n=>a.info.parentMenuId=n)},{default:l(({node:n,data:y})=>[o("span",null,C(y.menuName),1),n.isLeaf?x("",!0):(m(),F("span",h," ("+C(y.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1})]),_:1}),e(d,{lg:24},{default:l(()=>[e(f,{label:"默认查询排序字段"},{default:l(()=>[e(N,{modelValue:a.info.sortField,"onUpdate:modelValue":t[8]||(t[8]=n=>a.info.sortField=n),placeholder:"请选择字段",class:"mr10",clearable:""},{default:l(()=>[(m(!0),F(q,null,w(a.columns,n=>(m(),v(p,{key:n.columnId,label:n.csharpField,value:n.csharpField},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e(V,{modelValue:a.info.sortType,"onUpdate:modelValue":t[9]||(t[9]=n=>a.info.sortType=n),value:"asc"},{default:l(()=>t[37]||(t[37]=[u("正序")])),_:1},8,["modelValue"]),e(V,{modelValue:a.info.sortType,"onUpdate:modelValue":t[10]||(t[10]=n=>a.info.sortType=n),value:"desc"},{default:l(()=>t[38]||(t[38]=[u("倒序")])),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"useSnowflakeId"},{label:l(()=>[t[39]||(t[39]=u(" 是否使用雪花id ")),o("span",null,[e(s,{content:"设置成主键的字段将自动设置为雪花id字段",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(I,{disabled:a.info.tplCategory!="crud",modelValue:a.info.useSnowflakeId,"onUpdate:modelValue":t[11]||(t[11]=n=>a.info.useSnowflakeId=n)},{default:l(()=>[e(V,{value:!0},{default:l(()=>t[40]||(t[40]=[u("是")])),_:1}),e(V,{value:!1},{default:l(()=>t[41]||(t[41]=[u("否")])),_:1})]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"permissionPrefix"},{label:l(()=>[t[42]||(t[42]=u(" 权限前缀 ")),o("span",null,[e(s,{content:"eg：system:user:add中的'system:user'",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.permissionPrefix,"onUpdate:modelValue":t[12]||(t[12]=n=>a.info.permissionPrefix=n),placeholder:"请输入权限前缀"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"genType"},{label:l(()=>[t[43]||(t[43]=u(" 生成代码方式 ")),o("span",null,[e(s,{content:"默认为zip压缩包下载",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(V,{modelValue:a.info.genType,"onUpdate:modelValue":t[13]||(t[13]=n=>a.info.genType=n),value:"0"},{default:l(()=>t[44]||(t[44]=[u("zip压缩包")])),_:1},8,["modelValue"]),e(V,{modelValue:a.info.genType,"onUpdate:modelValue":t[14]||(t[14]=n=>a.info.genType=n),value:"1"},{default:l(()=>t[45]||(t[45]=[u("自定义路径")])),_:1},8,["modelValue"])]),_:1})]),_:1}),a.info.genType=="1"?(m(),v(d,{key:0,lg:12},{default:l(()=>[e(f,{prop:"genPath"},{label:l(()=>[t[46]||(t[46]=u(" 自定义路径 ")),o("span",null,[e(s,{content:"填写磁盘绝对路径，若不填写，则生成到当前Web项目下",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(S,{modelValue:a.info.genPath,"onUpdate:modelValue":t[15]||(t[15]=n=>a.info.genPath=n),placeholder:"前端代码路径在后端配置文件gen->vuePath下配置"},null,8,["modelValue"])]),_:1})]),_:1})):x("",!0),e(d,{lg:12},{default:l(()=>[e(f,{label:"是否生成仓储层"},{label:l(()=>[t[47]||(t[47]=u(" 是否生成仓储层 ")),o("span",null,[e(s,{content:"不勾选代码将不会生成对应的ZR.Repository代码",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(I,{modelValue:a.info.generateRepo,"onUpdate:modelValue":t[16]||(t[16]=n=>a.info.generateRepo=n)},{default:l(()=>[e(V,{value:1},{default:l(()=>t[48]||(t[48]=[u("是")])),_:1}),e(V,{value:0},{default:l(()=>t[49]||(t[49]=[u("否")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a.info.genType=="1"?(m(),v(d,{key:1,lg:12},{default:l(()=>[e(f,{prop:"generateMenu",label:"添加菜单"},{label:l(()=>[t[50]||(t[50]=u(" 生成菜单 ")),o("span",null,[e(s,{content:"勾选将会自动生成目录、菜单、按钮菜单",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(K,{modelValue:a.info.generateMenu,"onUpdate:modelValue":t[17]||(t[17]=n=>a.info.generateMenu=n),class:"ml-2"},null,8,["modelValue"])]),_:1})]),_:1})):x("",!0),e(d,{lg:12},{default:l(()=>[e(f,{prop:"colNum",label:"一行显示列"},{default:l(()=>[e(V,{modelValue:a.info.colNum,"onUpdate:modelValue":t[18]||(t[18]=n=>a.info.colNum=n),value:12},{default:l(()=>t[51]||(t[51]=[u("2列")])),_:1},8,["modelValue"]),e(V,{modelValue:a.info.colNum,"onUpdate:modelValue":t[19]||(t[19]=n=>a.info.colNum=n),value:24},{default:l(()=>t[52]||(t[52]=[u("1列")])),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"operBtnStyle",label:"操作按钮样式"},{default:l(()=>[e(V,{modelValue:a.info.operBtnStyle,"onUpdate:modelValue":t[20]||(t[20]=n=>a.info.operBtnStyle=n),value:1},{default:l(()=>t[53]||(t[53]=[u("button")])),_:1},8,["modelValue"]),e(V,{modelValue:a.info.operBtnStyle,"onUpdate:modelValue":t[21]||(t[21]=n=>a.info.operBtnStyle=n),value:2},{default:l(()=>t[54]||(t[54]=[u("text button")])),_:1},8,["modelValue"])]),_:1})]),_:1}),J(e(d,{lg:24},{default:l(()=>[e(f,{label:"生成功能"},{default:l(()=>[e(W,{modelValue:a.info.checkedBtn,"onUpdate:modelValue":t[22]||(t[22]=n=>a.info.checkedBtn=n),onChange:D},{default:l(()=>[e(T,{label:1},{default:l(()=>[e(k,null,{default:l(()=>t[55]||(t[55]=[u("添加")])),_:1})]),_:1}),e(T,{label:2},{default:l(()=>[e(k,{type:"success"},{default:l(()=>t[56]||(t[56]=[u("修改")])),_:1})]),_:1}),e(T,{label:3},{default:l(()=>[e(k,{type:"danger"},{default:l(()=>t[57]||(t[57]=[u("删除")])),_:1})]),_:1}),e(T,{label:4},{default:l(()=>[e(k,{type:"warning"},{default:l(()=>t[58]||(t[58]=[u("导出")])),_:1})]),_:1}),e(T,{label:5},{default:l(()=>[e(k,{type:"info"},{default:l(()=>t[59]||(t[59]=[u("查看")])),_:1})]),_:1}),e(T,{label:6},{default:l(()=>[e(k,{type:"danger"},{default:l(()=>t[60]||(t[60]=[u("清空")])),_:1})]),_:1}),e(T,{label:7},{default:l(()=>[e(k,{type:"danger"},{default:l(()=>t[61]||(t[61]=[u("批量删除")])),_:1})]),_:1}),e(T,{label:8},{default:l(()=>[e(k,null,{default:l(()=>t[62]||(t[62]=[u("批量导入")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},512),[[Q,a.info.tplCategory!="select"]]),e(d,{lg:12},{default:l(()=>[e(f,null,{label:l(()=>[t[63]||(t[63]=u(" 是否记录差异化日志 ")),o("span",null,[e(s,{content:"表编辑、删除会自动记录差异化日志",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(I,{modelValue:a.info.enableLog,"onUpdate:modelValue":t[23]||(t[23]=n=>a.info.enableLog=n)},{default:l(()=>[e(V,{value:!0},{default:l(()=>t[64]||(t[64]=[u("是")])),_:1}),e(V,{value:!1},{default:l(()=>t[65]||(t[65]=[u("否")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a.info.tplCategory=="tree"?(m(),v(P,{key:0},{default:l(()=>[e(d,{lg:24},{default:l(()=>t[66]||(t[66]=[o("h4",{class:"form-header"},"树表信息",-1)])),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"treeCode"},{label:l(()=>[t[67]||(t[67]=u(" 树编码字段 ")),o("span",null,[e(s,{content:"树显示的编码字段名， 如：dept_id",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(N,{modelValue:a.info.treeCode,"onUpdate:modelValue":t[24]||(t[24]=n=>a.info.treeCode=n),placeholder:"请选择树编码字段"},{default:l(()=>[(m(!0),F(q,null,w(a.columns,(n,y)=>(m(),v(p,{key:y,label:n.columnComment,value:n.csharpField},{default:l(()=>[o("span",c,C(n.csharpField),1),o("span",ee,C(n.columnComment),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"treeName"},{label:l(()=>[t[68]||(t[68]=u(" 树名称字段 ")),o("span",null,[e(s,{content:"树节点的显示名称字段名， 如：dept_name",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(N,{modelValue:a.info.treeName,"onUpdate:modelValue":t[25]||(t[25]=n=>a.info.treeName=n),placeholder:"请选择树名称字段"},{default:l(()=>[(m(!0),F(q,null,w(a.columns,(n,y)=>(m(),v(p,{key:y,label:n.csharpField,value:n.csharpField},{default:l(()=>[o("span",le,C(n.csharpField),1),o("span",te,C(n.columnComment),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:24},{default:l(()=>[e(f,{prop:"treeParentCode"},{label:l(()=>[t[69]||(t[69]=u(" 树父编码字段 ")),o("span",null,[e(s,{content:"树显示的父编码字段名， 如：parent_Id",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(N,{modelValue:a.info.treeParentCode,"onUpdate:modelValue":t[26]||(t[26]=n=>a.info.treeParentCode=n),placeholder:"请选择树父编码字段"},{default:l(()=>[(m(!0),F(q,null,w(a.columns,(n,y)=>(m(),v(p,{key:y,label:n.csharpField+"："+n.columnComment,value:n.csharpField},{default:l(()=>[o("span",ne,C(n.csharpField),1),o("span",ae,C(n.columnComment),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):x("",!0),a.info.tplCategory=="sub"||a.info.tplCategory=="subNav"||a.info.tplCategory=="subNavMore"?(m(),v(P,{key:1},{default:l(()=>[e(d,{lg:24},{default:l(()=>t[70]||(t[70]=[o("h4",{class:"form-header"},"关联信息",-1)])),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"subTableName"},{label:l(()=>[t[71]||(t[71]=u(" 关联子表的表名 ")),o("span",null,[e(s,{content:"关联子表的表名， 如：sys_user",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(N,{modelValue:a.info.subTableName,"onUpdate:modelValue":t[27]||(t[27]=n=>a.info.subTableName=n),filterable:"",placeholder:"请选择",onChange:t[28]||(t[28]=n=>z(this))},{default:l(()=>[(m(!0),F(q,null,w(a.tables,(n,y)=>(m(),v(p,{disabled:n.tableName==a.info.tableName,key:y,label:n.tableName+"："+n.tableComment,value:n.tableName},null,8,["disabled","label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{lg:12},{default:l(()=>[e(f,{prop:"subTableFkName"},{label:l(()=>[t[72]||(t[72]=u(" 子表关联的外键名 ")),o("span",null,[e(s,{content:"子表关联的外键名， 如：user_id",placement:"top"},{default:l(()=>[e(r,null,{default:l(()=>[e(i)]),_:1})]),_:1})])]),default:l(()=>[e(N,{modelValue:a.info.subTableFkName,"onUpdate:modelValue":t[29]||(t[29]=n=>a.info.subTableFkName=n)},{default:l(()=>[(m(!0),F(q,null,w(M(A),(n,y)=>(m(),v(p,{key:y,label:n.csharpField,value:n.csharpField},{default:l(()=>[o("span",ue,C(n.csharpField),1),o("span",oe,C(n.columnComment),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):x("",!0)]),_:1},8,["model","rules"])}}});export{ie as default};
