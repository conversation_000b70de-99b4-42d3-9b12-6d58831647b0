import{_ as _e,r as u,M as Ve,x as p,N as xe,o as g,c as N,j as F,i as a,l as t,D as v,z as we,O as ye,p as w,t as C,q as y,s as ke,P as _,H as Ee}from"./index-CX4J5aM5.js";import{R as S,u as Fe,c as Re,l as Ne,d as Ce}from"./ResourceSelector-DacERif6.js";import X from"./InputParametersEditor-BSB-5fUm.js";const Te={class:"exe-program-manager"},Ue={class:"operation-bar"},Se={class:"left-section"},he={class:"right-section"},De={key:0,style:{"margin-top":"10px"}},ze={key:1,style:{"margin-top":"5px",color:"#409EFF"}},Me={key:0,style:{"margin-top":"10px"}},Ae={key:1,style:{"margin-top":"5px",color:"#409EFF"}},Oe={class:"dialog-footer"},Le=ke({name:"exeprogrammanager"}),Be=Object.assign(Le,{setup(Ie){const h=u([]),k=u(!1),c=u(!1),d=u("exe"),E=u(0),T=u([]),D=u(""),z=u(!1),M=u(1),A=u(10),ee=u(0),b=u(0),R=u(!1),V=u(!1),ae=200*1024*1024,$=r=>r===100?"上传完成":`${r}%`,q=u(null),j=u(null),G=u(null),O={RPA:0,EXE:1,ORCHESTRATION:2},L=u({programName:[{required:!0,message:"请输入程序名",trigger:"blur"}]}),B=()=>[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}],m=u({programName:"",programType:1,isExclusive:!1,inputParameters:"",remarks:"",resourceSelection:"",version:"",ProgramPackageFile:null,notificationResourceMachines:""}),s=u({programName:"",programType:0,isExclusive:!0,inputParameters:"",remarks:"",resourceSelection:"",version:"",ProgramPackageFile:null,notificationResourceMachines:""}),f=u({programName:"",programType:2,inputParameters:"",remarks:"",version:""}),le=()=>{d.value==="rpa"&&(!s.value.inputParameters||s.value.inputParameters==="[]")&&(s.value.inputParameters=JSON.stringify(B())),E.value++},re=r=>{m.value.inputParameters=r},te=r=>{s.value.inputParameters=r},oe=r=>{f.value.inputParameters=r},U=()=>{m.value={programName:"",programType:1,isExclusive:!1,inputParameters:"",remarks:"",resourceSelection:"",version:"",ProgramPackageFile:null,notificationResourceMachines:""},s.value={programName:"",programType:0,isExclusive:!0,inputParameters:JSON.stringify(B()),remarks:"",resourceSelection:"",version:"",ProgramPackageFile:null,notificationResourceMachines:""},f.value={programName:"",programType:2,inputParameters:"",remarks:"",version:""},T.value=[],E.value++,V.value=!1,b.value=0,R.value=!1},se=()=>{k.value=!1,U(),V.value=!1},ue=r=>({...r,remarks:r.remarks??"",inputParameters:r.inputParameters||"[]",ProgramPackageFile:null}),J=r=>{if(k.value=!0,r){c.value=!0;const e=ue(r);switch(r.programType){case 1:d.value="exe",m.value=e;break;case 0:d.value="rpa",s.value=e;break;case 2:d.value="orchestration",f.value=e;break}}else c.value=!1,U(),d.value==="rpa"&&(s.value.inputParameters=JSON.stringify(B()),E.value++)},ne=async()=>{V.value=!0;try{let r,e;switch(d.value){case"exe":r=q.value,e=m.value;break;case"rpa":r=j.value,e=s.value;break;case"orchestration":r=G.value,e=f.value;break}if(!r){V.value=!1;return}if(await r.validate(),R.value&&e.ProgramPackageFile&&d.value!=="orchestration"){b.value=0;try{const o=await Fe(e.ProgramPackageFile,e,P=>{b.value=P});b.value=100,setTimeout(()=>{b.value=0,_.success(o.data.message||"文件上传成功"),k.value=!1,U(),x(),V.value=!1},1e3);return}catch(o){throw b.value=0,console.error("大文件上传失败:",o),V.value=!1,o}}const n=new FormData;Object.keys(e).forEach(o=>{o==="ProgramPackageFile"&&e[o]?n.append(o,e[o]):o==="resourceSelection"?e[o]&&e[o]!=="null"&&n.append(o,e[o]):o==="notificationResourceMachines"?n.append(o,e[o]===null||e[o]==="null"?"":e[o]):o!=="ProgramPackageFile"&&n.append(o,e[o])}),await Re(n),_.success("保存成功"),k.value=!1,U(),x()}catch(r){if(console.error("提交失败:",r),r.response&&r.response.data){const e=r.response.data;e.message?_.error(e.message):typeof e=="string"?_.error(e):_.error("保存失败，请检查输入是否正确")}else r.message?_.error(r.message):_.error("保存失败")}finally{V.value=!1}},x=async()=>{z.value=!0;try{const r=await Ne(D.value);h.value=r.data,console.log("Programs loaded:",r.data)}catch(r){console.error("获取程序列表失败:",r)}finally{z.value=!1}},ie=r=>{M.value=r,x()},me=r=>{A.value=r,x()},de=async r=>{try{const e=h.value.find(n=>n.id===r);if(!e){_.error("未找到要删除的程序");return}await Ee.confirm(`确定要删除程序"${e.programName}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Ce(r),_.success("删除成功"),x()}catch(e){e!=="cancel"&&(console.error("删除EXE程序失败:",e),_.error("删除失败"))}},pe=r=>{switch(r){case O.RPA:return"RPA";case O.EXE:return"EXE";case O.ORCHESTRATION:return"编排";default:return"未知"}},K=()=>{x()},Q=r=>{let e;switch(d.value){case"exe":e=m.value;break;case"rpa":e=s.value;break;default:return}if(e.ProgramPackageFile=r.raw,R.value=r.raw&&r.raw.size>ae,!e.programName){const n=r.name;e.programName=n.toLowerCase().endsWith(".zip")?n.slice(0,-4):n}T.value=[r]};return Ve(()=>{x()}),(r,e)=>{const n=p("el-button"),o=p("el-input"),P=p("el-table-column"),ve=p("el-table"),W=p("el-upload"),Y=p("el-progress"),i=p("el-form-item"),Z=p("el-switch"),I=p("el-form"),H=p("el-tab-pane"),ce=p("el-tabs"),fe=p("el-dialog"),ge=p("el-pagination"),be=xe("loading");return g(),N("div",Te,[F("div",Ue,[F("div",Se,[a(o,{modelValue:D.value,"onUpdate:modelValue":e[0]||(e[0]=l=>D.value=l),placeholder:"请输入程序名称搜索",style:{width:"300px"},clearable:"",onKeyup:we(K,["enter"])},{append:t(()=>[a(n,{onClick:K},{default:t(()=>e[21]||(e[21]=[v("搜索")])),_:1})]),_:1},8,["modelValue"])]),F("div",he,[a(n,{type:"primary",onClick:e[1]||(e[1]=l=>J(null))},{default:t(()=>e[22]||(e[22]=[v("添加程序")])),_:1})])]),ye((g(),w(ve,{"element-loading-text":"加载中...",data:h.value,class:"custom-table",style:{"margin-top":"20px"}},{default:t(()=>[a(P,{prop:"programName",label:"程序名","min-width":"200","show-overflow-tooltip":""}),a(P,{prop:"version",label:"版本号",width:"100"},{default:t(l=>[v(C(l.row.version||"-"),1)]),_:1}),a(P,{prop:"inputParameters",label:"输入参数","min-width":"150","show-overflow-tooltip":""}),a(P,{prop:"isExclusive",label:"是否独占",width:"80"},{default:t(l=>[v(C(l.row.isExclusive?"是":"否"),1)]),_:1}),a(P,{prop:"programType",label:"程序类型",width:"100"},{default:t(l=>[v(C(pe(l.row.programType)),1)]),_:1}),a(P,{prop:"createdAt",label:"创建时间",width:"160","show-overflow-tooltip":""},{default:t(l=>[v(C(l.row.createdAt?new Date(l.row.createdAt).toLocaleString():"-"),1)]),_:1}),a(P,{prop:"updatedAt",label:"最后更新时间",width:"160","show-overflow-tooltip":""},{default:t(l=>[v(C(l.row.updatedAt?new Date(l.row.updatedAt).toLocaleString():"-"),1)]),_:1}),a(P,{label:"操作",width:"150",fixed:"right"},{default:t(l=>[a(n,{type:"primary",onClick:Pe=>J(l.row)},{default:t(()=>e[23]||(e[23]=[v("修改")])),_:2},1032,["onClick"]),a(n,{type:"danger",onClick:Pe=>de(l.row.id)},{default:t(()=>e[24]||(e[24]=[v("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[be,z.value]]),a(fe,{title:c.value?"修改程序":"添加程序",modelValue:k.value,"onUpdate:modelValue":e[18]||(e[18]=l=>k.value=l),width:"1000px","close-on-click-modal":!1},{footer:t(()=>[F("span",Oe,[a(n,{onClick:se,disabled:V.value},{default:t(()=>e[29]||(e[29]=[v("取消")])),_:1},8,["disabled"]),a(n,{type:"primary",onClick:ne,disabled:V.value},{default:t(()=>e[30]||(e[30]=[v("确定")])),_:1},8,["disabled"])])]),default:t(()=>[a(ce,{modelValue:d.value,"onUpdate:modelValue":e[17]||(e[17]=l=>d.value=l),onTabClick:le},{default:t(()=>[a(H,{label:"EXE程序",name:"exe",disabled:c.value&&d.value!=="exe"},{default:t(()=>[a(I,{model:m.value,"label-width":"120px",ref_key:"exeFormRef",ref:q,rules:L.value.value},{default:t(()=>[a(i,{label:"上传文件包",prop:"ProgramPackageFile"},{default:t(()=>[a(W,{"file-list":T.value,"auto-upload":!1,"on-change":Q,accept:".zip"},{default:t(()=>[a(n,{type:"primary"},{default:t(()=>e[25]||(e[25]=[v("选择文件")])),_:1}),e[26]||(e[26]=F("div",{slot:"tip",class:"el-upload__tip"},"仅支持.zip格式文件，支持大文件上传（最大10GB）",-1))]),_:1},8,["file-list"]),b.value>0?(g(),N("div",De,[a(Y,{percentage:b.value,format:$},null,8,["percentage"])])):y("",!0),R.value?(g(),N("div",ze," 检测到大文件，过程可能比较慢，请耐心等待。上传过程中请勿关闭页面。 ")):y("",!0)]),_:1}),a(i,{label:"程序名",prop:"programName"},{default:t(()=>[a(o,{modelValue:m.value.programName,"onUpdate:modelValue":e[2]||(e[2]=l=>m.value.programName=l),disabled:c.value},null,8,["modelValue","disabled"])]),_:1}),c.value?(g(),w(i,{key:0,label:"版本号"},{default:t(()=>[a(o,{modelValue:m.value.version,"onUpdate:modelValue":e[3]||(e[3]=l=>m.value.version=l),disabled:""},null,8,["modelValue"])]),_:1})):y("",!0),a(i,{label:"是否独占"},{default:t(()=>[a(Z,{modelValue:m.value.isExclusive,"onUpdate:modelValue":e[4]||(e[4]=l=>m.value.isExclusive=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"默认资源选择"},{default:t(()=>[a(S,{modelValue:m.value.resourceSelection,"onUpdate:modelValue":e[5]||(e[5]=l=>m.value.resourceSelection=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"失败通知资源机"},{default:t(()=>[a(S,{modelValue:m.value.notificationResourceMachines,"onUpdate:modelValue":e[6]||(e[6]=l=>m.value.notificationResourceMachines=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"备注"},{default:t(()=>[a(o,{modelValue:m.value.remarks,"onUpdate:modelValue":e[7]||(e[7]=l=>m.value.remarks=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"输入参数"},{default:t(()=>[(g(),w(X,{key:"exe-"+E.value,"program-type":1,"initial-parameters":m.value.inputParameters,"onUpdate:parameters":re},null,8,["initial-parameters"]))]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["disabled"]),a(H,{label:"RPA程序",name:"rpa",disabled:c.value&&d.value!=="rpa"},{default:t(()=>[a(I,{model:s.value,"label-width":"120px",ref_key:"rpaFormRef",ref:j,rules:L.value.value},{default:t(()=>[a(i,{label:"上传文件包",prop:"ProgramPackageFile"},{default:t(()=>[a(W,{"file-list":T.value,"auto-upload":!1,"on-change":Q,accept:".zip"},{default:t(()=>[a(n,{type:"primary"},{default:t(()=>e[27]||(e[27]=[v("选择文件")])),_:1}),e[28]||(e[28]=F("div",{slot:"tip",class:"el-upload__tip"},"仅支持.zip格式文件，支持大文件上传（最大10GB）",-1))]),_:1},8,["file-list"]),b.value>0?(g(),N("div",Me,[a(Y,{percentage:b.value,format:$},null,8,["percentage"])])):y("",!0),R.value?(g(),N("div",Ae," 检测到大文件，将使用流式上传。上传过程中请勿关闭页面。 ")):y("",!0)]),_:1}),a(i,{label:"程序名",prop:"programName"},{default:t(()=>[a(o,{modelValue:s.value.programName,"onUpdate:modelValue":e[8]||(e[8]=l=>s.value.programName=l),disabled:c.value},null,8,["modelValue","disabled"])]),_:1}),c.value?(g(),w(i,{key:0,label:"版本号"},{default:t(()=>[a(o,{modelValue:s.value.version,"onUpdate:modelValue":e[9]||(e[9]=l=>s.value.version=l),disabled:""},null,8,["modelValue"])]),_:1})):y("",!0),a(i,{label:"是否独占"},{default:t(()=>[a(Z,{modelValue:s.value.isExclusive,"onUpdate:modelValue":e[10]||(e[10]=l=>s.value.isExclusive=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"默认资源选择"},{default:t(()=>[a(S,{modelValue:s.value.resourceSelection,"onUpdate:modelValue":e[11]||(e[11]=l=>s.value.resourceSelection=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"失败通知资源机"},{default:t(()=>[a(S,{modelValue:s.value.notificationResourceMachines,"onUpdate:modelValue":e[12]||(e[12]=l=>s.value.notificationResourceMachines=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"备注"},{default:t(()=>[a(o,{modelValue:s.value.remarks,"onUpdate:modelValue":e[13]||(e[13]=l=>s.value.remarks=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"输入参数"},{default:t(()=>[(g(),w(X,{key:"rpa-"+E.value,"program-type":0,"initial-parameters":s.value.inputParameters,"onUpdate:parameters":te},null,8,["initial-parameters"]))]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["disabled"]),a(H,{label:"编排程序",name:"orchestration",disabled:c.value&&d.value!=="orchestration"},{default:t(()=>[a(I,{model:f.value,"label-width":"120px",ref_key:"orchestrationFormRef",ref:G,rules:L.value.value},{default:t(()=>[a(i,{label:"程序名",prop:"programName"},{default:t(()=>[a(o,{modelValue:f.value.programName,"onUpdate:modelValue":e[14]||(e[14]=l=>f.value.programName=l),disabled:c.value},null,8,["modelValue","disabled"])]),_:1}),c.value?(g(),w(i,{key:0,label:"版本号"},{default:t(()=>[a(o,{modelValue:f.value.version,"onUpdate:modelValue":e[15]||(e[15]=l=>f.value.version=l),disabled:""},null,8,["modelValue"])]),_:1})):y("",!0),a(i,{label:"备注"},{default:t(()=>[a(o,{modelValue:f.value.remarks,"onUpdate:modelValue":e[16]||(e[16]=l=>f.value.remarks=l)},null,8,["modelValue"])]),_:1}),a(i,{label:"输入参数"},{default:t(()=>[(g(),w(X,{key:"orchestration-"+E.value,"program-type":2,"initial-parameters":f.value.inputParameters,"onUpdate:parameters":oe},null,8,["initial-parameters"]))]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),a(ge,{"current-page":M.value,"onUpdate:currentPage":e[19]||(e[19]=l=>M.value=l),"page-size":A.value,"onUpdate:pageSize":e[20]||(e[20]=l=>A.value=l),total:ee.value,onCurrentChange:ie,onSizeChange:me,layout:"total, sizes, prev, pager, next","page-sizes":[10,20,50,100]},null,8,["current-page","page-size","total"])])}}}),qe=_e(Be,[["__scopeId","data-v-73e0bef7"]]);export{qe as default};
