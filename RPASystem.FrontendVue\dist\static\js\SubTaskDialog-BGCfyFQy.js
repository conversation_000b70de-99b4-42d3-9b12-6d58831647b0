import{_ as K,e as Z,r as T,M as ee,x as c,o as d,p as _,l,i as o,D as R,j as y,t as v,c as C,k as P,W as te,q as V,X as ae,Q as k,P as g,H as D}from"./index-CX4J5aM5.js";import{c as oe}from"./clipboard-DuHwE8hz.js";const ne={key:1},le=["onClick"],re={key:1},se=["onClick"],ie={key:1},ue={class:"pagination-container"},ce={__name:"SubTaskDialog",props:{parentTaskId:{type:Number,required:!0},parentTaskName:{type:String,required:!0},visible:{type:Boolean,required:!0}},emits:["update:visible"],setup(N,{emit:j}){const x=N,B=j,M=Z({get:()=>x.visible,set:a=>B("update:visible",a)}),m=T({resourceMachine:"",status:[]}),i=T({pageNumber:1,pageSize:100,total:0}),$=T([]),h=async()=>{try{const a=new URLSearchParams;a.append("parentTaskId",x.parentTaskId),a.append("resourceMachine",m.value.resourceMachine),m.value.status.forEach(f=>{a.append("status",f)}),a.append("pageNumber",i.value.pageNumber),a.append("pageSize",i.value.pageSize);const e=await k.get("/api/jobtask/subtasks",{params:a});$.value=e.data.items,i.value.total=e.data.totalCount}catch(a){console.error("获取子任务列表失败:",a),g.error("获取子任务列表失败")}},I=()=>{i.value.pageNumber=1,h()},F=()=>{m.value={resourceMachine:"",status:[]},I()},U=a=>{i.value.pageSize=a,i.value.pageNumber=1,h()},J=a=>{i.value.pageNumber=a,h()},O=async a=>{try{await D.confirm("确定要重试该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await k.post(`/api/jobtask/retry/${a}`),g.success("已开始重试任务"),await h()}catch(e){e!=="cancel"&&(console.error("重试任务失败:",e),g.error("重试任务失败"))}},E=async a=>{try{await D.confirm("确定要停止该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await k.post(`/api/jobtask/stop/${a}`),g.success("已发送停止指令"),await h()}catch(e){e!=="cancel"&&(console.error("停止任务失败:",e),g.error("停止任务失败"))}},L=a=>({Pending:"info",Running:"primary",Success:"success",Failed:"danger",Cancelled:"warning"})[a]||"info",z=(a,e)=>{if(!a||!e)return"";const f=new Date(a),w=new Date(e),s=Math.floor((w-f)/1e3);if(s<0)return"";const u=Math.floor(s/(24*3600)),r=Math.floor(s%(24*3600)/3600),p=Math.floor(s%3600/60),n=s%60;return u===0&&r===0&&p===0?`${n}秒`:u===0&&r===0?p===0?`${n}秒`:`${p}分${n}秒`:u===0?`${r}时${p}分${n}秒`:`${u}天${r}时${p}分${n}秒`},q=a=>{try{const e=JSON.parse(a||"{}");return e.InputFile&&e.InputFile!==""}catch{return!1}},A=async a=>{try{const e=JSON.parse(a||"{}");if(e.InputFile){const f=e.InputFile,w=await k.get(`/api/filestorage/${f}`,{responseType:"blob",headers:{Accept:"application/octet-stream"}}),s=window.URL.createObjectURL(new Blob([w.data])),u=document.createElement("a");u.href=s;const r=new Date,p=r.getFullYear()+String(r.getMonth()+1).padStart(2,"0")+String(r.getDate()).padStart(2,"0")+String(r.getHours()).padStart(2,"0")+String(r.getMinutes()).padStart(2,"0")+String(r.getSeconds()).padStart(2,"0")+".xlsx";u.setAttribute("download",p),document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s)}}catch(e){console.error("下载文件失败:",e),g.error("下载文件失败，请重试")}},H=a=>{try{return JSON.parse(a||"{}").ReturnResult!==void 0}catch{return!1}},Q=a=>{try{const e=JSON.parse(a||"{}");e.ReturnResult!==void 0&&oe(e.ReturnResult)}catch(e){console.error("解析输出结果失败:",e),g.error("解析输出结果失败")}};return ee(()=>{h()}),(a,e)=>{const f=c("el-input"),w=c("el-form-item"),s=c("el-option"),u=c("el-select"),r=c("el-button"),p=c("el-form"),n=c("el-table-column"),b=c("el-tooltip"),W=c("el-tag"),X=c("el-table"),Y=c("el-pagination"),G=c("el-dialog");return d(),_(G,{modelValue:M.value,"onUpdate:modelValue":e[4]||(e[4]=t=>M.value=t),title:"子任务列表 - "+N.parentTaskName,width:"90%","destroy-on-close":""},{default:l(()=>[o(p,{inline:!0,model:m.value,class:"search-form"},{default:l(()=>[o(w,{label:"资源机"},{default:l(()=>[o(f,{modelValue:m.value.resourceMachine,"onUpdate:modelValue":e[0]||(e[0]=t=>m.value.resourceMachine=t),placeholder:"请输入资源机",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(w,{label:"状态"},{default:l(()=>[o(u,{modelValue:m.value.status,"onUpdate:modelValue":e[1]||(e[1]=t=>m.value.status=t),multiple:"",placeholder:"请选择状态",clearable:"",style:{width:"300px"}},{default:l(()=>[o(s,{label:"待运行",value:"Pending"}),o(s,{label:"运行中",value:"Running"}),o(s,{label:"成功",value:"Success"}),o(s,{label:"失败",value:"Failed"}),o(s,{label:"已取消",value:"Cancelled"})]),_:1},8,["modelValue"])]),_:1}),o(w,null,{default:l(()=>[o(r,{type:"primary",onClick:I},{default:l(()=>e[5]||(e[5]=[R("搜索")])),_:1}),o(r,{onClick:F},{default:l(()=>e[6]||(e[6]=[R("重置")])),_:1})]),_:1})]),_:1},8,["model"]),o(X,{data:$.value,style:{width:"100%"}},{default:l(()=>[o(n,{prop:"jobTaskId",label:"任务ID",width:"120"}),o(n,{prop:"jobTaskName",label:"任务名称","min-width":"120","show-overflow-tooltip":""}),o(n,{prop:"exeProgramName",label:"程序名称","min-width":"150","show-overflow-tooltip":""}),o(n,{prop:"priority",label:"优先级",width:"70",align:"center"}),o(n,{prop:"createdAt",label:"创建时间",width:"85","show-overflow-tooltip":""}),o(n,{prop:"startTime",label:"开始时间",width:"85","show-overflow-tooltip":""}),o(n,{label:"运行时长",width:"90","show-overflow-tooltip":""},{default:l(t=>[t.row.endTime?(d(),_(b,{key:0,content:`结束时间: ${t.row.endTime}`,placement:"top",effect:"light"},{default:l(()=>[y("span",null,v(z(t.row.startTime,t.row.endTime)),1)]),_:2},1032,["content"])):(d(),C("span",ne,v(z(t.row.startTime,t.row.endTime)),1))]),_:1}),o(n,{prop:"resourceSelection",label:"资源选择",width:"100","show-overflow-tooltip":""}),o(n,{prop:"assignedResourceMachine",label:"资源机",width:"120","show-overflow-tooltip":""}),o(n,{prop:"inputParameters",label:"输入参数",width:"100","show-overflow-tooltip":""},{default:l(t=>[q(t.row.inputParameters)?(d(),_(b,{key:0,content:t.row.inputParameters,placement:"top",effect:"light"},{default:l(()=>[y("span",{class:"clickable-text",onClick:S=>A(t.row.inputParameters)}," 下载输入件 ",8,le)]),_:2},1032,["content"])):(d(),C("span",re,v(t.row.inputParameters),1))]),_:1}),o(n,{prop:"outputResults",label:"输出结果",width:"80","show-overflow-tooltip":""},{default:l(t=>[H(t.row.outputResults)?(d(),_(b,{key:0,content:t.row.outputResults,placement:"top",effect:"light"},{default:l(()=>[y("span",{class:"clickable-text",onClick:S=>Q(t.row.outputResults)}," 复制结果 ",8,se)]),_:2},1032,["content"])):(d(),C("span",ie,v(t.row.outputResults),1))]),_:1}),o(n,{prop:"status",label:"状态",width:"100"},{default:l(t=>[o(W,{type:L(t.row.status)},{default:l(()=>[R(v(t.row.status),1)]),_:2},1032,["type"])]),_:1}),o(n,{prop:"notes",label:"备注","min-width":"100","show-overflow-tooltip":""}),o(n,{label:"操作",width:"120",fixed:"right"},{default:l(t=>[o(b,{content:"重试",placement:"top",effect:"light"},{default:l(()=>[t.row.status!=="Pending"&&t.row.status!=="Running"?(d(),_(r,{key:0,type:"primary",icon:P(te),circle:"",size:"small",onClick:S=>O(t.row.jobTaskId)},null,8,["icon","onClick"])):V("",!0)]),_:2},1024),o(b,{content:"停止",placement:"top",effect:"light"},{default:l(()=>[t.row.status==="Running"||t.row.status==="Pending"?(d(),_(r,{key:0,type:"warning",icon:P(ae),circle:"",size:"small",onClick:S=>E(t.row.jobTaskId)},null,8,["icon","onClick"])):V("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"]),y("div",ue,[o(Y,{"current-page":i.value.pageNumber,"onUpdate:currentPage":e[2]||(e[2]=t=>i.value.pageNumber=t),"page-size":i.value.pageSize,"onUpdate:pageSize":e[3]||(e[3]=t=>i.value.pageSize=t),"page-sizes":[50,100,200],total:i.value.total,layout:"total, sizes, prev, pager, next, jumper",background:!0,onSizeChange:U,onCurrentChange:J},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"])}}},me=K(ce,[["__scopeId","data-v-135672f3"]]);export{me as default};
