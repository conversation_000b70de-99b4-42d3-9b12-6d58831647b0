import{Q as T,_ as ee,e as te,r as R,M as oe,x as c,o as p,p as v,l as n,i as a,D as N,j as S,t as _,c as y,k as I,W as ae,q as P,X as ne,P as h,H as V}from"./index-CX4J5aM5.js";import{c as re}from"./clipboard-DuHwE8hz.js";function le(w){return T.post(`/api/jobtask/retry/${w}`)}function se(w){return T.post(`/api/jobtask/stop/${w}`)}function ie(w){return T.get(`/api/filestorage/${w}`,{responseType:"blob",headers:{Accept:"application/octet-stream"}})}const ue={key:1},ce=["onClick"],pe={key:1},de=["onClick"],me={key:1},fe=["onClick","title"],we=["title"],ge={key:2},he={class:"pagination-container"},_e={__name:"SubTaskDialog",props:{parentTaskId:{type:Number,required:!0},parentTaskName:{type:String,required:!0},visible:{type:Boolean,required:!0}},emits:["update:visible"],setup(w,{emit:D}){const x=w,j=D,M=te({get:()=>x.visible,set:o=>j("update:visible",o)}),g=R({resourceMachine:"",status:[]}),i=R({pageNumber:1,pageSize:10,total:0}),$=R([]),b=async()=>{try{const o=new URLSearchParams;o.append("parentTaskId",x.parentTaskId),o.append("resourceMachine",g.value.resourceMachine),g.value.status.forEach(d=>{o.append("status",d)}),o.append("pageNumber",i.value.pageNumber),o.append("pageSize",i.value.pageSize);const e=await T.get("/api/jobtask/subtasks",{params:o});$.value=e.data.items,i.value.total=e.data.totalCount}catch(o){console.error("获取子任务列表失败:",o),h.error("获取子任务列表失败")}},F=()=>{i.value.pageNumber=1,b()},B=()=>{g.value={resourceMachine:"",status:[]},F()},U=o=>{i.value.pageSize=o,i.value.pageNumber=1,b()},L=o=>{i.value.pageNumber=o,b()},O=async o=>{try{await V.confirm("确定要重试该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await le(o),h.success("已开始重试任务"),await b()}catch(e){e!=="cancel"&&(console.error("重试任务失败:",e),h.error("重试任务失败"))}},J=async o=>{try{await V.confirm("确定要停止该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await se(o),h.success("已发送停止指令"),await b()}catch(e){e!=="cancel"&&(console.error("停止任务失败:",e),h.error("停止任务失败"))}},E=o=>({Pending:"info",Running:"primary",Success:"success",Failed:"danger",Cancelled:"warning"})[o]||"info",z=(o,e)=>{if(!o||!e)return"";const d=new Date(o),m=new Date(e),s=Math.floor((m-d)/1e3);if(s<0)return"";const u=Math.floor(s/(24*3600)),l=Math.floor(s%(24*3600)/3600),f=Math.floor(s%3600/60),r=s%60;return u===0&&l===0&&f===0?`${r}秒`:u===0&&l===0?f===0?`${r}秒`:`${f}分${r}秒`:u===0?`${l}时${f}分${r}秒`:`${u}天${l}时${f}分${r}秒`},q=o=>{try{const e=JSON.parse(o||"{}");return e.InputFile&&e.InputFile!==""}catch{return!1}},W=async o=>{try{const e=JSON.parse(o||"{}");if(e.InputFile){const d=e.InputFile,m=await ie(d),s=window.URL.createObjectURL(new Blob([m.data])),u=document.createElement("a");u.href=s;const l=new Date,f=l.getFullYear()+String(l.getMonth()+1).padStart(2,"0")+String(l.getDate()).padStart(2,"0")+String(l.getHours()).padStart(2,"0")+String(l.getMinutes()).padStart(2,"0")+String(l.getSeconds()).padStart(2,"0")+".xlsx";u.setAttribute("download",f),document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s)}}catch(e){console.error("下载文件失败:",e),h.error("下载文件失败，请重试")}},A=o=>{try{return JSON.parse(o||"{}").ReturnResult!==void 0}catch{return!1}},H=o=>{try{const e=JSON.parse(o||"{}");e.ReturnResult!==void 0&&re(e.ReturnResult)}catch(e){console.error("解析输出结果失败:",e),h.error("解析输出结果失败")}},Q=o=>{if(!o)return!1;const e=o.toLowerCase();if(e.startsWith("\\\\")||e.startsWith("//")||e.includes("://"))return!0;const d=e.match(/^([a-z]):\\/i);if(d){const m=d[1].toLowerCase();if(m>="g"&&m<="z")return!0}return!1},X=o=>{if(o)try{const e="opendir:"+o;console.log("Opening directory:",e),window.location.href=e}catch(e){console.error("打开目录失败:",e),h.error("打开目录失败")}};return oe(()=>{b()}),(o,e)=>{const d=c("el-input"),m=c("el-form-item"),s=c("el-option"),u=c("el-select"),l=c("el-button"),f=c("el-form"),r=c("el-table-column"),k=c("el-tooltip"),Y=c("el-tag"),G=c("el-table"),K=c("el-pagination"),Z=c("el-dialog");return p(),v(Z,{modelValue:M.value,"onUpdate:modelValue":e[4]||(e[4]=t=>M.value=t),title:"子任务列表 - "+w.parentTaskName,width:"90%","destroy-on-close":""},{default:n(()=>[a(f,{inline:!0,model:g.value,class:"search-form"},{default:n(()=>[a(m,{label:"资源机"},{default:n(()=>[a(d,{modelValue:g.value.resourceMachine,"onUpdate:modelValue":e[0]||(e[0]=t=>g.value.resourceMachine=t),placeholder:"请输入资源机",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(m,{label:"状态"},{default:n(()=>[a(u,{modelValue:g.value.status,"onUpdate:modelValue":e[1]||(e[1]=t=>g.value.status=t),multiple:"",placeholder:"请选择状态",clearable:"",style:{width:"300px"}},{default:n(()=>[a(s,{label:"待运行",value:"Pending"}),a(s,{label:"运行中",value:"Running"}),a(s,{label:"成功",value:"Success"}),a(s,{label:"失败",value:"Failed"}),a(s,{label:"已取消",value:"Cancelled"})]),_:1},8,["modelValue"])]),_:1}),a(m,null,{default:n(()=>[a(l,{type:"primary",onClick:F},{default:n(()=>e[5]||(e[5]=[N("搜索")])),_:1}),a(l,{onClick:B},{default:n(()=>e[6]||(e[6]=[N("重置")])),_:1})]),_:1})]),_:1},8,["model"]),a(G,{data:$.value,style:{width:"100%"}},{default:n(()=>[a(r,{prop:"jobTaskId",label:"任务ID",width:"70"}),a(r,{prop:"jobTaskName",label:"任务名称","min-width":"150","show-overflow-tooltip":""}),a(r,{prop:"exeProgramName",label:"程序名称","min-width":"150","show-overflow-tooltip":""}),a(r,{prop:"priority",label:"优先级",width:"70",align:"center"}),a(r,{prop:"startTime",label:"开始时间",width:"160","show-overflow-tooltip":""}),a(r,{label:"运行时长",width:"90","show-overflow-tooltip":""},{default:n(t=>[t.row.endTime?(p(),v(k,{key:0,content:`结束时间: ${t.row.endTime}`,placement:"top",effect:"light"},{default:n(()=>[S("span",null,_(z(t.row.startTime,t.row.endTime)),1)]),_:2},1032,["content"])):(p(),y("span",ue,_(z(t.row.startTime,t.row.endTime)),1))]),_:1}),a(r,{prop:"resourceSelection",label:"资源选择",width:"100","show-overflow-tooltip":""}),a(r,{prop:"assignedResourceMachine",label:"资源机",width:"120","show-overflow-tooltip":""}),a(r,{prop:"inputParameters",label:"输入参数",width:"100","show-overflow-tooltip":""},{default:n(t=>[q(t.row.inputParameters)?(p(),v(k,{key:0,content:t.row.inputParameters,placement:"top",effect:"light"},{default:n(()=>[S("span",{class:"clickable-text",onClick:C=>W(t.row.inputParameters)}," 下载输入件 ",8,ce)]),_:2},1032,["content"])):(p(),y("span",pe,_(t.row.inputParameters),1))]),_:1}),a(r,{prop:"outputResults",label:"输出结果",width:"80","show-overflow-tooltip":""},{default:n(t=>[A(t.row.outputResults)?(p(),v(k,{key:0,content:t.row.outputResults,placement:"top",effect:"light"},{default:n(()=>[S("span",{class:"clickable-text",onClick:C=>H(t.row.outputResults)}," 复制结果 ",8,de)]),_:2},1032,["content"])):(p(),y("span",me,_(t.row.outputResults),1))]),_:1}),a(r,{prop:"outputFile",label:"输出文件",width:"100","show-overflow-tooltip":""},{default:n(t=>[t.row.outputFile&&Q(t.row.outputFile)?(p(),y("span",{key:0,class:"clickable-text",onClick:C=>X(t.row.outputFile),title:t.row.outputFile}," 打开目录 ",8,fe)):t.row.outputFile?(p(),y("span",{key:1,title:t.row.outputFile},_(t.row.outputFile),9,we)):(p(),y("span",ge,"-"))]),_:1}),a(r,{prop:"retryCount",label:"重试数",width:"80",align:"center"},{default:n(t=>[S("span",null,_(t.row.retryCount||0),1)]),_:1}),a(r,{prop:"status",label:"状态",width:"110"},{default:n(t=>[a(Y,{type:E(t.row.status)},{default:n(()=>[N(_(t.row.status),1)]),_:2},1032,["type"])]),_:1}),a(r,{prop:"notes",label:"备注","min-width":"100","show-overflow-tooltip":""}),a(r,{label:"操作",width:"120",fixed:"right"},{default:n(t=>[a(k,{content:"重试",placement:"top",effect:"light"},{default:n(()=>[t.row.status!=="Pending"&&t.row.status!=="Running"?(p(),v(l,{key:0,type:"primary",icon:I(ae),circle:"",size:"small",onClick:C=>O(t.row.jobTaskId)},null,8,["icon","onClick"])):P("",!0)]),_:2},1024),a(k,{content:"停止",placement:"top",effect:"light"},{default:n(()=>[t.row.status==="Running"||t.row.status==="Pending"?(p(),v(l,{key:0,type:"warning",icon:I(ne),circle:"",size:"small",onClick:C=>J(t.row.jobTaskId)},null,8,["icon","onClick"])):P("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"]),S("div",he,[a(K,{"current-page":i.value.pageNumber,"onUpdate:currentPage":e[2]||(e[2]=t=>i.value.pageNumber=t),"page-size":i.value.pageSize,"onUpdate:pageSize":e[3]||(e[3]=t=>i.value.pageSize=t),"page-sizes":[10,50,100],total:i.value.total,layout:"total, sizes, prev, pager, next, jumper",background:!0,onSizeChange:U,onCurrentChange:L},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"])}}},ye=ee(_e,[["__scopeId","data-v-cd94d904"]]);export{ye as default};
