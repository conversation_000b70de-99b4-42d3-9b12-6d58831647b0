import{_ as D,a as K,E as M,a5 as Z,r as f,x as s,o as i,p as c,l as n,i as a,k as o,c as b,F as V,K as E,D as y,t as G,m as H,z as J,s as P,n as Q,v as W}from"./index-CX4J5aM5.js";import{s as X}from"./common-bbqFW65J.js";import{l as Y,g as ee}from"./emailtpl-BE1RVfmu.js";import{_ as le}from"./index-Av_SNAwg.js";const te=P({name:"sendemail"}),ae=Object.assign(te,{setup(oe){const $=K(),S=M({form:{fileUrl:"",htmlContent:"",toEmails:[],email:"",fromName:"system"},rules:{fromName:[{required:!0,message:"发送邮箱不能为空",trigger:"blur"}],subject:[{required:!0,message:"主题不能为空",trigger:"blur"}],content:[{required:!0,message:"内容不能为空",trigger:"blur"}],toEmails:[{required:!0,message:"收件人不能为空",trigger:"blur"}]},sendEmailOptions:[{dictLabel:"system",dictValue:"system"}]}),{form:t,rules:h,sendEmailOptions:z}=Z(S),{proxy:m}=W(),R=f(null),L=f(!1),C=f([]);Y({pageSize:100}).then(u=>{const{code:l,data:p}=u;p.result.filter(_=>{C.value.push({dictLabel:_.name,dictValue:_.id.toString()})})});function N(u){u&&ee(u).then(l=>{l.code==200?t.value.htmlContent=l.data.content:t.value.htmlContent=""})}function j(){m.$refs.formRef.validate(u=>{if(u){m.$modal.loading("loading...");var l={...t.value,toUser:t.value.toEmails.toString()};X(l).then(p=>{L.value=!1,p.code==200&&(m.$message.success("发送成功"),$.push({name:"emaillog"})),m.$modal.closeLoading()}),setTimeout(()=>{m.$modal.closeLoading()},5e3)}else return console.log("未通过"),!1})}const v=f(!1),x=f(),d=f("");function O(u){t.value.toEmails.splice(t.value.toEmails.indexOf(u),1)}const q=()=>{if(t.value.toEmails.length>=5){m.$modal.msgError("最多5个标签");return}v.value=!0,Q(()=>{x.value.input.focus()})};function k(){if(d.value){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(d.value)){m.$modal.msgError("请输入有效的邮箱");return}t.value.toEmails.push(d.value)}v.value=!1,d.value=""}return(u,l)=>{const p=s("el-option"),_=s("el-select"),r=s("el-form-item"),g=s("el-col"),U=s("el-switch"),B=s("el-row"),F=s("el-tag"),T=s("el-input"),w=s("el-button"),I=s("UploadFile"),A=s("el-form");return i(),c(A,{class:"mt10",ref_key:"formRef",ref:R,model:o(t),"label-width":"100px",rules:o(h)},{default:n(()=>[a(B,null,{default:n(()=>[a(g,{lg:6},{default:n(()=>[a(r,{label:"发送邮箱",prop:"fromName"},{default:n(()=>[a(_,{modelValue:o(t).fromName,"onUpdate:modelValue":l[0]||(l[0]=e=>o(t).fromName=e),placeholder:"请选择发送邮箱"},{default:n(()=>[(i(!0),b(V,null,E(o(z),e=>(i(),c(p,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(g,{lg:6},{default:n(()=>[a(r,{label:"选择模板",prop:"emailTpl"},{default:n(()=>[a(_,{modelValue:o(t).emailTpl,"onUpdate:modelValue":l[1]||(l[1]=e=>o(t).emailTpl=e),placeholder:"邮件模板",onChange:N,clearable:""},{default:n(()=>[(i(!0),b(V,null,E(o(C),e=>(i(),c(p,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(g,{lg:6},{default:n(()=>[a(r,{label:"是否立即送出",prop:"isSend"},{default:n(()=>[a(U,{modelValue:o(t).isSend,"onUpdate:modelValue":l[2]||(l[2]=e=>o(t).isSend=e),"active-value":!0,"in-active-value":!1,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1}),a(g,{lg:6},{default:n(()=>[a(r,{label:"发送自己",prop:"sendMe"},{default:n(()=>[a(U,{modelValue:o(t).sendMe,"onUpdate:modelValue":l[3]||(l[3]=e=>o(t).sendMe=e),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(r,{label:"接收人",prop:"toEmails"},{default:n(()=>[(i(!0),b(V,null,E(o(t).toEmails,e=>(i(),c(F,{key:e,class:"mr10",closable:"",onClose:ne=>O(e)},{default:n(()=>[y(G(e),1)]),_:2},1032,["onClose"]))),128)),o(v)?(i(),c(T,{key:0,size:"small",style:{width:"180px"},ref_key:"inputRef",ref:x,modelValue:o(d),"onUpdate:modelValue":l[4]||(l[4]=e=>H(d)?d.value=e:null),placeholder:"请输入邮箱地址",onKeyup:J(k,["enter"]),onBlur:k},null,8,["modelValue"])):(i(),c(w,{key:1,class:"button-new-tag",size:"small",icon:"plus",text:"",onClick:q},{default:n(()=>l[8]||(l[8]=[y("收件人邮箱")])),_:1}))]),_:1}),a(r,{label:"邮件主题",prop:"subject"},{default:n(()=>[a(T,{modelValue:o(t).subject,"onUpdate:modelValue":l[5]||(l[5]=e=>o(t).subject=e)},null,8,["modelValue"])]),_:1}),a(r,{label:"邮件内容",prop:"htmlContent"},{default:n(()=>[a(o(le),{modelValue:o(t).htmlContent,"onUpdate:modelValue":l[6]||(l[6]=e=>o(t).htmlContent=e)},null,8,["modelValue"])]),_:1}),a(r,{label:"附件"},{default:n(()=>[a(I,{modelValue:o(t).fileUrl,"onUpdate:modelValue":l[7]||(l[7]=e=>o(t).fileUrl=e),limit:5,fileSize:15,data:{fileDir:"email",uploadType:1}},null,8,["modelValue"])]),_:1}),a(r,null,{default:n(()=>[a(w,{type:"primary",icon:"upload",onClick:j},{default:n(()=>l[9]||(l[9]=[y("发送邮件")])),_:1})]),_:1})]),_:1},8,["model","rules"])}}}),me=D(ae,[["__scopeId","data-v-1b528712"]]);export{me as default};
