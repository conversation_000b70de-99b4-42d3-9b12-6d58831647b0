import{r as f,E as O,a5 as fe,x as u,N as P,o as s,c as $,O as y,S as ce,k as a,i as e,l,z as _e,F as A,K,p as g,D as c,t as r,m as Q,j,q as x,s as be,v as he,n as ge}from"./index-CX4J5aM5.js";import{l as G,g as ve,a as Ve,u as ye,b as ke,d as we}from"./dept-D6prJY8i.js";const Ne={class:"app-container"},Ie={key:0},Ce=be({name:"dept"}),Te=Object.assign(Ce,{setup($e){const D=f(!0),N=f(!0),L=f([]),T=f(!1),U=f(!0),S=f([]),F=f(""),h=f(!1),I=f([]),v=O({deptName:void 0,status:void 0}),H=O({form:{},rules:{deptName:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),J=f(),{form:d,rules:M}=fe(H),{proxy:p}=he();function V(){D.value=!0,G(v).then(o=>{L.value=p.handleTree(o.data,"deptId"),D.value=!1})}function W(){h.value=!1,q()}function q(){d.value={deptId:void 0,parentId:void 0,deptName:void 0,orderNum:999,leader:void 0,phone:void 0,email:void 0,status:0},p.resetForm("formRef")}function B(){V()}function X(){p.resetForm("queryForm"),B()}function R(o){q(),o!=null&&(d.value.parentId=o.deptId),h.value=!0,F.value="添加部门",G().then(n=>{S.value=p.handleTree(n.data,"deptId")})}function Y(o){q(),ve(o.deptId).then(n=>{d.value=n.data,h.value=!0,F.value="修改部门"}),Ve(o.deptId).then(n=>{S.value=p.handleTree(n.data,"deptId")})}function Z(){p.$refs.formRef.validate(o=>{o&&(d.value.deptId!=null?ye(d.value).then(n=>{p.$modal.msgSuccess("修改成功"),h.value=!1,V()}):ke(d.value).then(n=>{p.$modal.msgSuccess("新增成功"),h.value=!1,V()}))})}function ee(o){p.$confirm('是否确认删除名称为"'+o.deptName+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return we(o.deptId)}).then(()=>{V(),p.$modal.msgSuccess("删除成功")})}function le(){U.value=!1,T.value=!T.value,ge(()=>{U.value=!0})}return V(),p.getDicts("sys_normal_disable").then(o=>{I.value=o.data}),(o,n)=>{const k=u("el-input"),i=u("el-form-item"),te=u("el-option"),ae=u("el-select"),_=u("el-button"),E=u("el-form"),b=u("el-col"),ne=u("right-toolbar"),z=u("el-row"),m=u("el-table-column"),oe=u("dict-tag"),de=u("el-table"),ue=u("el-cascader"),re=u("el-input-number"),se=u("el-radio"),pe=u("el-radio-group"),ie=u("el-dialog"),C=P("hasPermi"),me=P("loading");return s(),$("div",Ne,[y(e(E,{model:a(v),ref:"queryForm",inline:!0},{default:l(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:l(()=>[e(k,{modelValue:a(v).deptName,"onUpdate:modelValue":n[0]||(n[0]=t=>a(v).deptName=t),placeholder:"请输入部门名称",onKeyup:_e(B,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"状态",prop:"status"},{default:l(()=>[e(ae,{modelValue:a(v).status,"onUpdate:modelValue":n[1]||(n[1]=t=>a(v).status=t),placeholder:"部门状态"},{default:l(()=>[(s(!0),$(A,null,K(a(I),t=>(s(),g(te,{key:t.dictValue,label:t.dictLabel,value:t.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,null,{default:l(()=>[e(_,{type:"primary",icon:"search",onClick:B},{default:l(()=>[c(r(o.$t("btn.search")),1)]),_:1}),e(_,{icon:"refresh",onClick:X},{default:l(()=>[c(r(o.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ce,a(N)]]),e(z,{gutter:10,class:"mb8"},{default:l(()=>[e(b,{span:1.5},{default:l(()=>[y((s(),g(_,{plain:"",type:"primary",icon:"plus",onClick:R},{default:l(()=>[c(r(o.$t("btn.add")),1)]),_:1})),[[C,["system:dept:add"]]])]),_:1}),e(b,{span:1.5},{default:l(()=>[e(_,{type:"info",plain:"",icon:"sort",onClick:le},{default:l(()=>[c(r(o.$t("btn.expand"))+"/"+r(o.$t("btn.collapse")),1)]),_:1})]),_:1}),e(ne,{showSearch:a(N),"onUpdate:showSearch":n[2]||(n[2]=t=>Q(N)?N.value=t:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),a(U)?y((s(),g(de,{key:0,data:a(L),"row-key":"deptId","default-expand-all":a(T),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:l(()=>[e(m,{prop:"deptName",label:"部门名称",width:"240"}),e(m,{prop:"deptId",label:"部门id"}),e(m,{prop:"leader",label:"负责人",width:"100"}),e(m,{prop:"phone",label:"联系电话",width:"120"}),e(m,{prop:"email",label:"邮箱",width:"120"}),e(m,{prop:"userNum",label:"部门人数",width:"100"}),e(m,{prop:"orderNum",label:"排序"}),e(m,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(oe,{options:a(I),value:t.row.status},null,8,["options","value"])]),_:1}),e(m,{label:"创建时间",align:"center",prop:"createTime",width:"200"},{default:l(t=>[j("span",null,r(o.parseTime(t.row.createTime)),1)]),_:1}),e(m,{label:"操作",width:"200"},{default:l(t=>[y((s(),g(_,{text:"",size:"small",icon:"edit",onClick:w=>Y(t.row)},{default:l(()=>[c(r(o.$t("btn.edit")),1)]),_:2},1032,["onClick"])),[[C,["system:dept:update"]]]),y((s(),g(_,{text:"",size:"small",icon:"plus",onClick:w=>R(t.row)},{default:l(()=>[c(r(o.$t("btn.add")),1)]),_:2},1032,["onClick"])),[[C,["system:dept:add"]]]),t.row.parentId!=0?y((s(),g(_,{key:0,text:"",size:"small",icon:"delete",onClick:w=>ee(t.row)},{default:l(()=>[c(r(o.$t("btn.delete")),1)]),_:2},1032,["onClick"])),[[C,["system:dept:remove"]]]):x("",!0)]),_:1})]),_:1},8,["data","default-expand-all"])),[[me,a(D)]]):x("",!0),e(ie,{title:a(F),modelValue:a(h),"onUpdate:modelValue":n[10]||(n[10]=t=>Q(h)?h.value=t:null),width:"600px","append-to-body":""},{footer:l(()=>[e(_,{text:"",onClick:W},{default:l(()=>[c(r(o.$t("btn.cancel")),1)]),_:1}),e(_,{type:"primary",onClick:Z},{default:l(()=>[c(r(o.$t("btn.submit")),1)]),_:1})]),default:l(()=>[e(E,{ref_key:"formRef",ref:J,model:a(d),rules:a(M),"label-width":"80px"},{default:l(()=>[e(z,{gutter:20},{default:l(()=>[a(d).parentId!==0?(s(),g(b,{key:0,lg:24},{default:l(()=>[e(i,{label:"上级部门",prop:"parentId"},{default:l(()=>[e(ue,{class:"w100",options:a(S),props:{checkStrictly:!0,value:"deptId",label:"deptName",emitPath:!1},placeholder:"请选择上级菜单",clearable:"",modelValue:a(d).parentId,"onUpdate:modelValue":n[3]||(n[3]=t=>a(d).parentId=t)},{default:l(({node:t,data:w})=>[j("span",null,r(w.deptName),1),t.isLeaf?x("",!0):(s(),$("span",Ie," ("+r(w.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1})]),_:1})):x("",!0),e(b,{lg:12},{default:l(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:l(()=>[e(k,{modelValue:a(d).deptName,"onUpdate:modelValue":n[4]||(n[4]=t=>a(d).deptName=t),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{lg:12},{default:l(()=>[e(i,{label:"显示排序",prop:"orderNum"},{default:l(()=>[e(re,{modelValue:a(d).orderNum,"onUpdate:modelValue":n[5]||(n[5]=t=>a(d).orderNum=t),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{lg:12},{default:l(()=>[e(i,{label:"负责人",prop:"leader"},{default:l(()=>[e(k,{modelValue:a(d).leader,"onUpdate:modelValue":n[6]||(n[6]=t=>a(d).leader=t),placeholder:"请输入负责人",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{lg:12},{default:l(()=>[e(i,{label:"联系电话",prop:"phone"},{default:l(()=>[e(k,{modelValue:a(d).phone,"onUpdate:modelValue":n[7]||(n[7]=t=>a(d).phone=t),placeholder:"请输入联系电话",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{lg:12},{default:l(()=>[e(i,{label:"邮箱",prop:"email"},{default:l(()=>[e(k,{modelValue:a(d).email,"onUpdate:modelValue":n[8]||(n[8]=t=>a(d).email=t),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{lg:12},{default:l(()=>[e(i,{label:"部门状态"},{default:l(()=>[e(pe,{modelValue:a(d).status,"onUpdate:modelValue":n[9]||(n[9]=t=>a(d).status=t)},{default:l(()=>[(s(!0),$(A,null,K(a(I),t=>(s(),g(se,{key:t.dictValue,value:parseInt(t.dictValue)},{default:l(()=>[c(r(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Te as default};
