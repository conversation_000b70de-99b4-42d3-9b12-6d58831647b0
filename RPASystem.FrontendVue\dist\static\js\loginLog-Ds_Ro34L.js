import{r as d,E as z,x as l,N as F,o as N,c as P,i as e,l as n,k as t,m as R,D as k,O as w,p as U,j as h,t as x,a8 as E,S as I,v as L}from"./index-CX4J5aM5.js";import{q as O}from"./logininfor-Bp1ShoQv.js";const Q={class:"app-container"},G={__name:"loginLog",setup(j){const c=d(!0),p=d(0),m=d([]),f=d([]),u=d([]),i=z({pageNum:1,pageSize:10,ipaddr:void 0,userName:void 0,status:void 0}),{proxy:_}=L();function g(){c.value=!0,O(_.addDateRange(i,u.value)).then(s=>{c.value=!1,s.code==200?(m.value=s.data.result,p.value=s.data.totalNum):(p.value=0,m.value=[])})}g(),_.getDicts("sys_common_status").then(s=>{f.value=s.data});function v(){i.pageNum=1,g()}function D(){u.value=[],_.resetForm("queryForm"),v()}return(s,o)=>{const S=l("el-date-picker"),b=l("el-form-item"),y=l("el-button"),V=l("el-form"),r=l("el-table-column"),q=l("dict-tag"),C=l("el-table"),B=l("pagination"),T=F("loading");return N(),P("div",Q,[e(V,{model:t(i),ref:"queryForm",inline:!0},{default:n(()=>[e(b,{label:"登录时间"},{default:n(()=>[e(S,{modelValue:t(u),"onUpdate:modelValue":o[0]||(o[0]=a=>R(u)?u.value=a:null),type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(b,null,{default:n(()=>[e(y,{type:"primary",icon:"search",onClick:v},{default:n(()=>o[3]||(o[3]=[k("搜索")])),_:1}),e(y,{icon:"refresh",onClick:D},{default:n(()=>o[4]||(o[4]=[k("重置")])),_:1})]),_:1})]),_:1},8,["model"]),w((N(),U(C,{data:t(m),border:""},{default:n(()=>[e(r,{label:"IP地址",align:"center",prop:"ipaddr",width:"130"},{default:n(({row:a})=>[h("div",null,x(a.ipaddr),1)]),_:1}),e(r,{label:"登录地点",align:"center",prop:"position"}),e(r,{label:"操作系统",align:"center",prop:"os"}),e(r,{label:"操作状态",align:"center",prop:"status",width:"90"},{default:n(({row:a})=>[e(q,{options:t(f),value:a.status},null,8,["options","value"])]),_:1}),e(r,{label:"操作信息",align:"center",prop:"msg"}),e(r,{label:"登录日期",align:"center",prop:"loginTime",width:"100"},{default:n(a=>[h("span",null,x(t(E)(a.row.loginTime)),1)]),_:1})]),_:1},8,["data"])),[[T,t(c)]]),w(e(B,{total:t(p),page:t(i).pageNum,"onUpdate:page":o[1]||(o[1]=a=>t(i).pageNum=a),limit:t(i).pageSize,"onUpdate:limit":o[2]||(o[2]=a=>t(i).pageSize=a),onPagination:g},null,8,["total","page","limit"]),[[I,t(p)>0]])])}}};export{G as default};
