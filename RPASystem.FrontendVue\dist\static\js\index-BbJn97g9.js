import{i as y}from"./requireIcons-B3C5g2-d.js";import{_ as C,r as d,at as B,x as a,o as s,c,i as t,l as n,F as u,K as m,k as f,D as v,t as l,j as _,p as D,aJ as E,s as N}from"./index-CX4J5aM5.js";const V={class:"icons-container"},j={class:"icon-item"},w={class:"icon-item"},F=N({name:"icons"}),L=Object.assign(F,{setup($){const g=d(y),r=d([]);for(const o in B)r.value.push(o);function h(o){return`<svg-icon name="${o}" />`}function x(o){return`<el-icon><${o} /></el-icon>`}return(o,J)=>{const I=a("svg-icon"),i=a("el-tooltip"),p=a("el-tab-pane"),b=a("el-icon"),k=a("el-tabs");return s(),c("div",V,[t(k,{type:"border-card"},{default:n(()=>[t(p,{label:"svg Icons"},{default:n(()=>[(s(!0),c(u,null,m(f(g),e=>(s(),c("div",{key:e,class:"item"},[t(i,{placement:"top"},{content:n(()=>[v(l(h(e)),1)]),default:n(()=>[_("div",j,[t(I,{name:e,style:{height:"40px",width:"40px"}},null,8,["name"]),_("span",null,l(e),1)])]),_:2},1024)]))),128))]),_:1}),t(p,{label:"Element-UI Icons"},{default:n(()=>[(s(!0),c(u,null,m(f(r),e=>(s(),c("div",{key:e},[t(i,{placement:"top"},{content:n(()=>[v(l(x(e)),1)]),default:n(()=>[_("div",w,[t(b,null,{default:n(()=>[(s(),D(E(e)))]),_:2},1024),_("span",null,l(e),1)])]),_:2},1024)]))),128))]),_:1})]),_:1})])}}}),S=C(L,[["__scopeId","data-v-e5677d43"]]);export{S as default};
