import{i as r}from"./index-BtiuLXK9.js";import{M as i,o as s,c as o,V as l,B as c,v as u}from"./index-CX4J5aM5.js";const g={__name:"PieChart",props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(e){const{proxy:a}=u();let t=null;function n(){t=r(a.$refs.chartRef),t.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{left:"center",bottom:"10",data:["Industries","Technology","Forex","Gold","Forecasts"]},series:[{name:"WEEKLY WRITE ARTICLES",type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:[{value:320,name:"Industries"},{value:240,name:"Technology"},{value:149,name:"Forex"},{value:100,name:"Gold"},{value:59,name:"Forecasts"}],animationEasing:"cubicInOut",animationDuration:2600}]})}return i(()=>{n()}),(m,d)=>(s(),o("div",{ref:"chartRef",class:l(e.className),style:c({height:e.height,width:e.width})},null,6))}};export{g as default};
