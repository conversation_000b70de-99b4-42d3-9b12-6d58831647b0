import{a as k}from"./user-DLtqobtK.js";import{r as w,x as n,o as y,p as N,l as o,i as e,D as s,t as m,j as x,k as v,v as C}from"./index-CX4J5aM5.js";const U={style:{"margin-left":"40px"}},B={__name:"userInfo",props:{user:{type:Object}},setup(l){const b=l,{proxy:i}=C(),g=w({nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],email:[{required:!0,message:"邮箱地址不能为空",trigger:"blur"},{type:"email",message:"'请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]});function c(){i.$refs.userRef.validate(t=>{t&&k(b.user).then(a=>{i.$modal.msgSuccess("修改成功")})})}function V(){i.$tab.closePage()}return(t,a)=>{const d=n("el-input"),u=n("el-form-item"),f=n("el-radio"),$=n("el-radio-group"),p=n("el-button"),h=n("el-form");return y(),N(h,{ref:"userRef",model:l.user,rules:v(g),"label-width":"100px","label-position":"left",style:{"max-width":"350px"}},{default:o(()=>[e(u,{label:t.$t("user.nickName"),prop:"nickName"},{default:o(()=>[e(d,{modelValue:l.user.nickName,"onUpdate:modelValue":a[0]||(a[0]=r=>l.user.nickName=r),maxlength:"20","show-word-limit":!0},null,8,["modelValue"])]),_:1},8,["label"]),e(u,{label:t.$t("user.phoneNumber"),prop:"phonenumber"},{default:o(()=>[e(d,{modelValue:l.user.phonenumber,"onUpdate:modelValue":a[1]||(a[1]=r=>l.user.phonenumber=r),maxlength:"11","show-word-limit":!0},null,8,["modelValue"])]),_:1},8,["label"]),e(u,{label:t.$t("user.userEmail"),prop:"email"},{default:o(()=>[e(d,{modelValue:l.user.email,"onUpdate:modelValue":a[2]||(a[2]=r=>l.user.email=r),maxlength:"50","show-word-limit":!0},null,8,["modelValue"])]),_:1},8,["label"]),e(u,{label:t.$t("common.sex")},{default:o(()=>[e($,{modelValue:l.user.sex,"onUpdate:modelValue":a[3]||(a[3]=r=>l.user.sex=r)},{default:o(()=>[e(f,{value:0},{default:o(()=>[s(m(t.$t("common.male")),1)]),_:1}),e(f,{value:1},{default:o(()=>[s(m(t.$t("common.female")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),x("div",U,[e(p,{type:"danger",icon:"Close",onClick:V},{default:o(()=>[s(m(t.$t("btn.close")),1)]),_:1}),e(p,{type:"primary",icon:"Check",onClick:c},{default:o(()=>[s(m(t.$t("btn.save")),1)]),_:1})])]),_:1},8,["model","rules"])}}};export{B as default};
