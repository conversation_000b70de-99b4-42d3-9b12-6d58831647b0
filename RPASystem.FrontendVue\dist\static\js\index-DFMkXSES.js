import{ai as Q,_ as Fe,r as f,E as Ie,a5 as qe,b4 as De,x as i,N as oe,o as m,c as T,O as k,S as Ee,k as a,i as e,l,m as M,D as d,F as L,K as z,p as v,t as u,A as Be,a8 as Me,q as ne,j as P,s as Qe,v as Oe}from"./index-CX4J5aM5.js";import{Q as Ae}from"./qrcode-CxxOwKMC.js";function je(w){return Q({url:"tool/file/list",method:"get",params:w})}function He(w){return Q({url:"tool/file/"+w,method:"get"})}function Ke(w){return Q({url:"tool/file/"+w,method:"delete"})}const Ge={class:"app-container"},Je={class:"dialog-footer"},We={ref:"imgContainerRef",id:"imgContainer",class:"qrCode"},Xe=Qe({name:"file"}),Ye=Object.assign(Xe,{setup(w){const R=f([]),ie=f(!0),O=f(!0),F=f(!0),A=f(!0),j=f(""),V=f(!1),N=f(!1),de=f(null),p=f({}),re=f(null),U=f([]),H=f([{dictLabel:"本地存储",dictValue:1},{dictLabel:"阿里云存储",dictValue:2}]),ue=f([{dictLabel:"原文件名",dictValue:1},{dictLabel:"自定义",dictValue:2},{dictLabel:"自动生成",dictValue:3}]),se=f([{dictLabel:"uploads",dictValue:"uploads"},{dictLabel:"video",dictValue:"video"},{dictLabel:"avatar",dictValue:"avatar"}]),K=f([]),G=f(0),ce=Ie({form:{storeType:1},rules:{accessUrl:[{required:!0,message:"上传文件不能为空",trigger:"blur"}],storeType:[{required:!0,message:"存储类型不能为空",trigger:"blur"}],fileName:[{required:!0,message:"文件名不能为空",trigger:"blur"}]},queryParams:{pageNum:1,pageSize:20,storeType:1,fileId:void 0}}),{queryParams:_,form:c,rules:J}=qe(ce),{proxy:g}=Oe(),W=f();function S(){g.addDateRange(_.value,U.value,"Create_time"),F.value=!0,je(_.value).then(n=>{n.code==200&&(K.value=n.data.result,G.value=n.data.totalNum,F.value=!1)})}function fe(){V.value=!1,X()}function X(){c.value={fileName:"",fileUrl:"",storePath:"",fileSize:0,fileExt:"",storeType:1,accessUrl:"",fileNameType:3},g.resetForm("formRef")}function pe(){U.value=[],g.resetForm("queryForm"),C()}function me(n){R.value=n.map(o=>o.id),ie.value=n.length!=1,O.value=!n.length}function C(){_.pageNum=1,S()}function _e(){X(),V.value=!0,j.value="上传文件"}function Y(n){const o=n.id||R.value;g.$confirm('是否确认删除参数编号为"'+o+'"的数据项？').then(function(){return Ke(o)}).then(()=>{C(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ge(n){const o=n.id||R.value;He(o).then(h=>{const{code:r,data:$}=h;r==200&&(N.value=!0,p.value=$,g.$nextTick(()=>{be($.accessUrl)}))})}function be(n){document.getElementById("imgContainer").innerHTML="",new Ae(document.getElementById("imgContainer"),{text:n,width:100,height:100})}function ye(n){V.value=!1,S()}function ve(){g.$refs.formRef.validate(n=>{if(n){var o=new Promise(h=>{W.value={fileDir:c.value.storePath,fileName:c.value.fileName,storeType:c.value.storeType,fileNameType:c.value.fileNameType},h(!0)});o.then(()=>{g.$refs.uploadRef.submitUpload()})}})}async function we(n){await g.downFile("/common/downloadFile",{fileId:n.id})}const{copy:Ve,isSupported:he}=De(),Z=async n=>{he?(Ve(n),g.$modal.msgSuccess("复制成功！")):g.$modal.msgError("当前浏览器不支持")};return C(),(n,o)=>{const h=i("el-input"),r=i("el-form-item"),$=i("el-date-picker"),I=i("el-radio-button"),q=i("el-radio-group"),b=i("el-button"),D=i("el-form"),s=i("el-col"),ke=i("right-toolbar"),E=i("el-row"),y=i("el-table-column"),Ue=i("el-link"),Ce=i("document"),ee=i("el-icon"),le=i("el-image"),Te=i("el-table"),Ne=i("pagination"),Se=i("questionFilled"),$e=i("el-tooltip"),xe=i("el-option"),Le=i("el-select"),ze=i("el-radio"),Pe=i("UploadFile"),te=i("el-dialog"),ae=i("el-tag"),x=oe("hasPermi"),Re=oe("loading");return m(),T("div",Ge,[k(e(D,{model:a(_),"label-position":"left",inline:"",ref:"queryForm",onSubmit:o[3]||(o[3]=Be(()=>{},["prevent"]))},{default:l(()=>[e(r,{label:"",prop:"fileId"},{default:l(()=>[e(h,{modelValue:a(_).fileId,"onUpdate:modelValue":o[0]||(o[0]=t=>a(_).fileId=t),placeholder:"请输入文件id",clearable:""},null,8,["modelValue"])]),_:1}),e(r,{label:""},{default:l(()=>[e($,{modelValue:a(U),"onUpdate:modelValue":o[1]||(o[1]=t=>M(U)?U.value=t:null),type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",placeholder:"请选择上传时间"},null,8,["modelValue"])]),_:1}),e(r,{label:"",prop:"storeType"},{default:l(()=>[e(q,{modelValue:a(_).storeType,"onUpdate:modelValue":o[2]||(o[2]=t=>a(_).storeType=t),onChange:C,placeholder:"请选择存储类型"},{default:l(()=>[e(I,{value:""},{default:l(()=>o[14]||(o[14]=[d(" 全部 ")])),_:1}),(m(!0),T(L,null,z(a(H),t=>(m(),v(I,{key:t.dictValue,value:t.dictValue},{default:l(()=>[d(u(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,null,{default:l(()=>[e(b,{type:"primary",icon:"search",onClick:C},{default:l(()=>[d(u(n.$t("btn.search")),1)]),_:1}),e(b,{icon:"refresh",onClick:pe},{default:l(()=>[d(u(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[Ee,a(A)]]),e(E,{gutter:10,class:"mb8"},{default:l(()=>[e(s,{span:1.5},{default:l(()=>[k((m(),v(b,{type:"primary",plain:"",icon:"upload",onClick:_e},{default:l(()=>[d(u(n.$t("btn.upload")),1)]),_:1})),[[x,["tool:file:add"]]])]),_:1}),e(s,{span:1.5},{default:l(()=>[k((m(),v(b,{type:"danger",disabled:a(O),plain:"",icon:"delete",onClick:Y},{default:l(()=>[d(u(n.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[x,["tool:file:delete"]]])]),_:1}),e(ke,{showSearch:a(A),onQueryTable:S},null,8,["showSearch"])]),_:1}),k((m(),v(Te,{data:a(K),ref:"table",border:"","highlight-current-row":"",onSelectionChange:me},{default:l(()=>[e(y,{type:"selection",width:"50",align:"center"}),e(y,{prop:"id",label:"文件id",width:"150","show-overflow-tooltip":!0}),e(y,{prop:"fileName",label:"文件名",align:"left",width:"180","show-overflow-tooltip":!0},{default:l(t=>[e(Ue,{type:"primary",href:t.row.accessUrl,target:"_blank"},{default:l(()=>[d(u(t.row.fileName),1)]),_:2},1032,["href"])]),_:1}),e(y,{prop:"accessUrl",align:"center",label:"预览图",width:"80"},{default:l(({row:t})=>[e(le,{"preview-teleported":"",src:t.accessUrl,"preview-src-list":[t.accessUrl],"hide-on-click-modal":!0,fit:"contain",lazy:"",class:"el-avatar"},{error:l(()=>[e(ee,null,{default:l(()=>[e(Ce)]),_:1})]),_:2},1032,["src","preview-src-list"])]),_:1}),e(y,{prop:"fileSize",label:"文件大小",align:"center","show-overflow-tooltip":!0}),e(y,{prop:"fileExt",label:"扩展名",align:"center","show-overflow-tooltip":!0,width:"80px"}),e(y,{prop:"storePath",label:"存储目录"}),e(y,{prop:"create_by",label:"操作人",align:"center"}),e(y,{prop:"create_time",label:"创建日期",align:"center"},{default:l(({row:t})=>[d(u(a(Me)(t.create_time)),1)]),_:1}),e(y,{label:"操作",align:"center",width:"160"},{default:l(t=>[e(b,{text:"",size:"small",icon:"view",title:"查看",onClick:B=>ge(t.row)},null,8,["onClick"]),t.row.storeType==1?k((m(),v(b,{key:0,text:"",size:"small",icon:"download",title:"下载",onClick:B=>we(t.row)},null,8,["onClick"])),[[x,["tool:file:download"]]]):ne("",!0),e(b,{class:"copy-btn-main",icon:"document-copy",title:"复制",text:"",size:"small",onClick:B=>Z(t.row.accessUrl)},null,8,["onClick"]),k(e(b,{title:"删除",text:"",size:"small",icon:"delete",onClick:B=>Y(t.row)},null,8,["onClick"]),[[x,["tool:file:delete"]]])]),_:1})]),_:1},8,["data"])),[[Re,a(F)]]),e(Ne,{background:"",total:a(G),page:a(_).pageNum,"onUpdate:page":o[4]||(o[4]=t=>a(_).pageNum=t),limit:a(_).pageSize,"onUpdate:limit":o[5]||(o[5]=t=>a(_).pageSize=t),onPagination:S},null,8,["total","page","limit"]),e(te,{title:a(j),"lock-scroll":!1,modelValue:a(V),"onUpdate:modelValue":o[11]||(o[11]=t=>M(V)?V.value=t:null),width:"400px",draggable:""},{footer:l(()=>[P("div",Je,[e(b,{text:"",onClick:fe},{default:l(()=>[d(u(n.$t("btn.cancel")),1)]),_:1}),e(b,{type:"primary",onClick:ve},{default:l(()=>[d(u(n.$t("btn.submit")),1)]),_:1})])]),default:l(()=>[e(D,{ref_key:"formRef",ref:de,model:a(c),rules:a(J),"label-width":"90px","label-position":"left"},{default:l(()=>[e(E,null,{default:l(()=>[e(s,{lg:24},{default:l(()=>[e(r,{label:"存储类型",prop:"storeType"},{default:l(()=>[e(q,{modelValue:a(c).storeType,"onUpdate:modelValue":o[6]||(o[6]=t=>a(c).storeType=t),placeholder:"请选择存储类型"},{default:l(()=>[(m(!0),T(L,null,z(a(H),t=>(m(),v(I,{key:t.dictValue,value:parseInt(t.dictValue)},{default:l(()=>[d(u(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{lg:24},{default:l(()=>[e(r,{label:"存储目录",prop:"storePath"},{label:l(()=>[P("span",null,[e($e,{content:"文件目录不填则默认使用本地上传格式：yyyy/MMdd",placement:"top"},{default:l(()=>[e(ee,{size:15},{default:l(()=>[e(Se)]),_:1})]),_:1}),o[15]||(o[15]=d(" 存储目录 "))])]),default:l(()=>[e(Le,{style:{width:"100%"},modelValue:a(c).storePath,"onUpdate:modelValue":o[7]||(o[7]=t=>a(c).storePath=t),"allow-create":"",clearable:"",filterable:"","default-first-option":"","reserve-keyword":!1,placeholder:"请输入文件目录，默认yyyy/MMdd格式"},{default:l(()=>[(m(!0),T(L,null,z(a(se),t=>(m(),v(xe,{key:t.dictValue,label:t.dictLabel,value:t.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{lg:24},{default:l(()=>[e(r,{label:"文件名规则",prop:"fileNameType"},{default:l(()=>[e(q,{modelValue:a(c).fileNameType,"onUpdate:modelValue":o[8]||(o[8]=t=>a(c).fileNameType=t),placeholder:"请选择文件名存储类型"},{default:l(()=>[(m(!0),T(L,null,z(a(ue),t=>(m(),v(ze,{key:t.dictValue,value:parseInt(t.dictValue)},{default:l(()=>[d(u(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(c).fileNameType==2?(m(),v(s,{key:0,lg:24},{default:l(()=>[e(r,{label:"自定文件名",prop:"fileName"},{default:l(()=>[e(h,{modelValue:a(c).fileName,"onUpdate:modelValue":o[9]||(o[9]=t=>a(c).fileName=t),placeholder:"请输入文件名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})):ne("",!0),e(s,{lg:24},{default:l(()=>[e(Pe,{ref_key:"uploadRef",ref:re,modelValue:a(c).accessUrl,"onUpdate:modelValue":o[10]||(o[10]=t=>a(c).accessUrl=t),fileType:[],fileSize:100,drag:!0,data:a(W),autoUpload:!1,onSuccess:ye},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(te,{"lock-scroll":!1,modelValue:a(N),"onUpdate:modelValue":o[13]||(o[13]=t=>M(N)?N.value=t:null)},{default:l(()=>[e(D,{ref_key:"form",ref:c,model:a(p),rules:a(J),"label-width":"90px","label-position":"left"},{default:l(()=>[e(E,null,{default:l(()=>[e(s,{lg:12},{default:l(()=>[e(r,{label:"文件id"},{default:l(()=>[d(u(a(p).id),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"源文件名"},{default:l(()=>[d(u(a(p).realName),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"文件类型"},{default:l(()=>[e(ae,null,{default:l(()=>[d(u(a(p).fileType),1)]),_:1})]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"扩展名"},{default:l(()=>[e(ae,null,{default:l(()=>[d(u(a(p).fileExt),1)]),_:1})]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"文件名"},{default:l(()=>[d(u(a(p).fileName),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"存储目录"},{default:l(()=>[d(u(a(p).storePath),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"文件大小"},{default:l(()=>[d(u(a(p).fileSize),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"创建人"},{default:l(()=>[d(u(a(p).create_by),1)]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"预览"},{default:l(()=>[e(le,{src:a(p).accessUrl,fit:"contain",style:{width:"100px"}},null,8,["src"])]),_:1})]),_:1}),e(s,{lg:12},{default:l(()=>[e(r,{label:"二维码"},{default:l(()=>[P("div",We,null,512)]),_:1})]),_:1}),e(s,{lg:24},{default:l(()=>[e(r,{label:"访问路径"},{default:l(()=>[d(u(a(p).accessUrl)+" ",1),e(b,{class:"copy-btn-main",icon:"document-copy",text:"",onClick:o[12]||(o[12]=t=>Z(a(p).accessUrl))},{default:l(()=>[d(u(n.$t("btn.copy")),1)]),_:1})]),_:1})]),_:1}),e(s,{lg:24},{default:l(()=>[e(r,{label:"存储路径"},{default:l(()=>[P("div",null,u(a(p).fileUrl),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),ll=Fe(Ye,[["__scopeId","data-v-f5568489"]]);export{ll as default};
