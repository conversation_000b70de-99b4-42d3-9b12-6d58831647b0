import{i as p}from"./requireIcons-B3C5g2-d.js";import{_ as K,r as c,at as R,x as a,o as i,c as u,i as t,l as s,k as d,m as y,j as m,F as N,K as w,t as $,D as U,v as T}from"./index-CX4J5aM5.js";const z={class:"icon-body"},A={class:"icon-list"},G=["onClick"],J={class:"name"},M={class:"icon-list"},P=["onClick"],Q={class:"name"},W={__name:"index",emits:["selected"],setup(X,{expose:B,emit:D}){const{proxy:E}=T(),r=c([]),v=c([]);for(const l in R)r.value.push(l),v.value.push(l);const o=c(""),_=c(p),f=c("1"),C=D;function g(){o.value?(_.value=p.filter(l=>l.indexOf(o.value)!==-1),r.value=v.value.filter(l=>l.toLocaleLowerCase().indexOf(o.value.toLocaleLowerCase())!==-1)):(_.value=p,r.value=v.value)}function x(l,e){const b=e!=null?e+l:l;C("selected",b),document.body.click()}function F(){C("selected",o.value),document.body.click()}function O(){o.value="",_.value=p}function S(){E.$modal.msg("请将svg图标放置在目录/src/assets/icons/svg里面")}return B({reset:O}),(l,e)=>{const b=a("search"),V=a("el-icon"),j=a("delete"),h=a("el-input"),k=a("svg-icon"),I=a("el-tab-pane"),q=a("el-button"),H=a("el-tabs");return i(),u("div",z,[t(h,{modelValue:d(o),"onUpdate:modelValue":e[1]||(e[1]=n=>y(o)?o.value=n:null),style:{position:"relative"},clearable:"",placeholder:"请输入图标名称",onClear:g,onInput:g},{prefix:s(()=>[t(V,{class:"el-input__icon"},{default:s(()=>[t(b)]),_:1})]),suffix:s(()=>[t(V,{class:"el-input__icon",onClick:e[0]||(e[0]=n=>x(""))},{default:s(()=>[t(j)]),_:1})]),_:1},8,["modelValue"]),t(H,{modelValue:d(f),"onUpdate:modelValue":e[4]||(e[4]=n=>y(f)?f.value=n:null)},{default:s(()=>[t(I,{label:"svg-icon",name:"1"},{default:s(()=>[m("div",A,[(i(!0),u(N,null,w(d(_),(n,L)=>(i(),u("div",{class:"icon-item mb10",key:L,onClick:Y=>x(n,"")},[t(k,{name:n,style:{height:"20px",width:"20px"}},null,8,["name"]),m("div",J,$(n),1)],8,G))),128))]),m("div",{class:"help text-muted mt5",onClick:S},[t(k,{name:"question"}),e[5]||(e[5]=U(" 如何增加icon "))])]),_:1}),t(I,{label:"Element-UI Icons",name:"2"},{default:s(()=>[m("div",M,[(i(!0),u(N,null,w(d(r),n=>(i(),u("div",{class:"icon-item mb10",key:n,onClick:L=>x(n,"ele-")},[t(k,{name:"ele-"+n,style:{height:"25px",width:"25px"}},null,8,["name"]),m("div",Q,$(n),1)],8,P))),128))])]),_:1}),t(I,{label:"网络图",name:"3"},{default:s(()=>[t(h,{modelValue:d(o),"onUpdate:modelValue":e[3]||(e[3]=n=>y(o)?o.value=n:null),placeholder:"请输入网络路径"},{append:s(()=>[t(q,{type:"primary",onClick:e[2]||(e[2]=n=>F())},{default:s(()=>e[6]||(e[6]=[U("确定")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["modelValue"])])}}},ne=K(W,[["__scopeId","data-v-736dd782"]]);export{ne as I};
