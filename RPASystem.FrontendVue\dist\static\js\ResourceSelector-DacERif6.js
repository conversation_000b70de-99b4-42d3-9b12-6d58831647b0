import{Q as s,_ as M,r as i,w as S,M as F,x as d,o as n,p as m,l as _,i as b,c as x,F as v,K as C}from"./index-CX4J5aM5.js";function B(t){return s.get("/api/exeprogram/search",{params:{query:t}})}function E(t){return s.post("/api/exeprogram",t,{headers:{"Content-Type":"multipart/form-data"}})}function I(t){return s.delete(`/api/exeprogram/${t}`)}function N(){return s.get("/api/exeprogram/resources")}function T(t,a,u){const r=new FormData;return r.append("file",t),a&&Object.keys(a).forEach(e=>{e!=="ProgramPackageFile"&&a[e]!==null&&a[e]!==void 0&&(e==="id"||e==="ID"?r.append("ID",a[e]):typeof a[e]=="boolean"?r.append(e,a[e]?"true":"false"):r.append(e,a[e]))}),s.post("/api/exeprogram/upload-large-file",r,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{if(e.lengthComputable&&u){const c=Math.round(e.loaded*100/e.total);u(c)}},timeout:36e5})}const j={__name:"ResourceSelector",props:{modelValue:{type:String,default:""}},emits:["update:modelValue"],setup(t,{emit:a}){const u=t,r=a,e=i([]),c=i([]),p=i([]);S(()=>u.modelValue,o=>{o?p.value=o.split("|"):p.value=[]},{immediate:!0});const P=async()=>{try{const o=await N();e.value=o.data.resourcePools,c.value=o.data.resourceMachines}catch(o){console.error("获取资源列表失败:",o)}},R=o=>{r("update:modelValue",o.join("|"))};return F(()=>{P()}),(o,f)=>{const g=d("el-option"),h=d("el-option-group"),V=d("el-select");return n(),m(V,{modelValue:p.value,"onUpdate:modelValue":f[0]||(f[0]=l=>p.value=l),multiple:"",filterable:"",placeholder:"请选择资源",class:"resource-selector",onChange:R},{default:_(()=>[b(h,{label:"资源池"},{default:_(()=>[(n(!0),x(v,null,C(e.value,l=>(n(),m(g,{key:"pool_"+l.id,label:l.poolName,value:l.poolName},null,8,["label","value"]))),128))]),_:1}),b(h,{label:"资源机"},{default:_(()=>[(n(!0),x(v,null,C(c.value,l=>(n(),m(g,{key:"machine_"+l.id,label:l.machineName,value:l.machineName},null,8,["label","value"]))),128))]),_:1})]),_:1},8,["modelValue"])}}},w=M(j,[["__scopeId","data-v-ab0423b9"]]),U=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"}));export{w as R,U as a,E as c,I as d,B as l,T as u};
