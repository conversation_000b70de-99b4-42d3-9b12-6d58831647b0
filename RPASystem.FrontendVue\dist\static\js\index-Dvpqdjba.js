import{ai as B,r as b,E as Y,e as Z,J as ee,w as te,x as l,N as h,o as s,c as z,i as o,l as t,O as p,p as m,D as i,k as a,m as oe,j as $,t as _,aG as ne,q as I,F as le,K as ae,S as ie,s as re,v as se}from"./index-CX4J5aM5.js";function ue(f){return B({url:"/monitor/online/list",method:"get",params:f})}function ce(f){return B({url:"/monitor/online/force",method:"delete",data:f})}function de(f){return B({url:"/monitor/online/batchForce",method:"delete",data:f})}const me={class:"app-container"},pe=re({name:"onlineuser"}),fe=Object.assign(pe,{setup(f){const{proxy:g}=se(),q=b(null),u=Y({pageNum:1,pageSize:10}),M=Z(()=>ee().onlineNum),v=b(1),k=b(!1),x=b([]),y=b(0);function L(){u.pageNum=1,w()}function w(){k.value=!0,ue(u).then(r=>{r.code==200&&(y.value=r.data.totalNum,x.value=r.data.result,setTimeout(()=>{k.value=!1},200))})}w();function R(r){g.$prompt("请输入消息内容","",{confirmButtonText:"发送",cancelButtonText:"取消",inputPattern:/\S/,inputErrorMessage:"消息内容不能为空"}).then(({value:e})=>{g.signalr.SR.invoke("sendMessage",r.userid,e).catch(function(c){console.error(c.toString())})}).catch(()=>{})}function D(r){g.$prompt("请输入强退原因","",{confirmButtonText:"发送",cancelButtonText:"取消"}).then(e=>{ce({...r,time:10,reason:e.value,clientId:r.clientId}).then(()=>{g.$modal.msgSuccess("强退成功")})})}function U(){g.$prompt("请输入强退原因","",{confirmButtonText:"发送",cancelButtonText:"取消"}).then(r=>{de({time:10,reason:r.value}).then(e=>{g.$modal.msgSuccess("强退成功")})})}return te(M,()=>{L()},{immediate:!0}),(r,e)=>{const c=l("el-button"),C=l("el-form-item"),P=l("el-radio-button"),j=l("el-radio-group"),E=l("el-form"),d=l("el-table-column"),F=l("el-table"),S=l("el-descriptions-item"),O=l("el-tag"),A=l("el-descriptions"),H=l("el-text"),G=l("el-card"),J=l("el-col"),K=l("el-empty"),Q=l("el-row"),W=l("pagination"),N=h("hasPermi"),V=h("hasRole"),X=h("loading");return s(),z("div",me,[o(E,{model:a(u),ref_key:"queryRef",ref:q,inline:!0},{default:t(()=>[o(C,null,{default:t(()=>[p((s(),m(c,{plain:"",type:"primary",onClick:e[0]||(e[0]=n=>U()),icon:"lock"},{default:t(()=>e[4]||(e[4]=[i("全部强退")])),_:1})),[[N,["monitor:online:forceLogout"]]])]),_:1}),o(C,null,{default:t(()=>[o(j,{modelValue:a(v),"onUpdate:modelValue":e[1]||(e[1]=n=>oe(v)?v.value=n:null)},{default:t(()=>[o(P,{value:"1"},{default:t(()=>e[5]||(e[5]=[i("表格")])),_:1}),o(P,{value:"2"},{default:t(()=>e[6]||(e[6]=[i("卡片")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(C,null,{default:t(()=>[o(c,{type:"primary",icon:"Search",onClick:L},{default:t(()=>e[7]||(e[7]=[i("刷新")])),_:1})]),_:1})]),_:1},8,["model"]),a(v)==1?p((s(),m(F,{key:0,data:a(x),ref:"tableRef",border:"","highlight-current-row":""},{default:t(()=>[o(d,{label:"No",type:"index",width:"50",align:"center"},{default:t(n=>[$("span",null,_((a(u).pageNum-1)*a(u).pageSize+n.$index+1),1)]),_:1}),o(d,{prop:"name",label:"用户名",align:"center"}),o(d,{label:"登录地点",prop:"location",align:"center"}),o(d,{label:"登录IP",prop:"userIP",align:"center"}),o(d,{prop:"browser",label:"登录浏览器",width:"210"}),o(d,{prop:"platform",label:"登录平台",align:"center"}),o(d,{prop:"loginTime",label:"登录时间",witdh:"280px"},{default:t(n=>[i(_(a(ne)(n.row.loginTime).format("MM/DD日HH:mm:ss"))+" ",1),$("div",null,"在线时长："+_(n.row.onlineTime)+"分钟",1)]),_:1}),o(d,{label:"操作",align:"center",width:"160"},{default:t(n=>[p((s(),m(c,{text:"",onClick:T=>R(n.row),icon:"ChatDotRound"},{default:t(()=>e[8]||(e[8]=[i("私信")])),_:2},1032,["onClick"])),[[V,["admin"]]]),p((s(),m(c,{text:"",onClick:T=>D(n.row),icon:"lock"},{default:t(()=>e[9]||(e[9]=[i("强退")])),_:2},1032,["onClick"])),[[N,["monitor:online:forceLogout"]]])]),_:1})]),_:1},8,["data"])),[[X,a(k)]]):I("",!0),a(v)==2?(s(),m(Q,{key:1,gutter:20},{default:t(()=>[(s(!0),z(le,null,ae(a(x),n=>(s(),m(J,{lg:4,span:24},{default:t(()=>[o(G,{"body-style":{padding:"15px 15px 0"}},{default:t(()=>[o(A,{column:1,title:n.name},{default:t(()=>[o(S,{label:"登录平台"},{default:t(()=>[i(_(n.platform),1)]),_:2},1024),o(S,{label:"登录地点"},{default:t(()=>[i(_(n.location),1)]),_:2},1024),o(S,{label:"在线时长",span:2},{default:t(()=>[o(O,{type:"success"},{default:t(()=>[i(_(n.onlineTime)+"分钟",1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["title"]),o(H,{truncated:""},{default:t(()=>[i(_(n.browser),1)]),_:2},1024),$("div",null,[p((s(),m(c,{text:"",onClick:T=>R(n),size:"small",icon:"ChatDotRound",title:"私信"},{default:t(()=>e[10]||(e[10]=[i("私信")])),_:2},1032,["onClick"])),[[V,["admin"]]]),p((s(),m(c,{text:"",onClick:T=>D(n),size:"small",icon:"lock",title:"强退"},{default:t(()=>e[11]||(e[11]=[i("强退")])),_:2},1032,["onClick"])),[[N,["monitor:online:forceLogout"]]])])]),_:2},1024)]),_:2},1024))),256)),p(o(K,{description:"no data"},null,512),[[ie,a(y)==0]])]),_:1})):I("",!0),o(W,{total:a(y),page:a(u).pageNum,"onUpdate:page":e[2]||(e[2]=n=>a(u).pageNum=n),limit:a(u).pageSize,"onUpdate:limit":e[3]||(e[3]=n=>a(u).pageSize=n),onPagination:w},null,8,["total","page","limit"])])}}});export{fe as default};
