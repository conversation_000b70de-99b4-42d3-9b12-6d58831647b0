using Microsoft.Win32;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace RPASystem.Client.Common
{
    /// <summary>
    /// 环境参数设置，注册表存储
    /// </summary>
    public class EnvParUtils
    {
        static string VPath = @"SOFTWARE\WOW6432Node\HUAWEI\V";
        public static string Read(string key, string defaultValue = "")
        {

            using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
            {
                using (var regkey = baseKey.OpenSubKey(VPath))
                {
                    if (regkey != null)
                    {
                        var value = regkey.GetValue(key);
                        if (value != null)
                        {
                            return value.ToString();
                        }
                    }
                }
            }
            Write(key, defaultValue);
            return defaultValue;

        }

        public static void Write(string key, string value)
        {
            using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
            {
                using (var regkey = baseKey.CreateSubKey(VPath, true))
                {
                    if (regkey != null)
                    {
                        regkey.SetValue(key, value, RegistryValueKind.String);
                    }
                }
            }
        }

    }
}
