import{s as P,r as v,w as D,M as k,R as M,o as y,c as C,av as N,j as s,aa as O,x as S,p as $,l as b,i as f,t as x,k as _,F as Y,K as q,D as T}from"./index-CX4J5aM5.js";import U from"./full-screen-container-DqIKXGW_.js";import L from"./BarChart-BBEVQmr2.js";import R from"./lineEcharts-BYb_A7IC.js";import G from"./simpleGauge-B9aKUN2r.js";import H from"./doughnutCharts-BkpLgMIO.js";import I from"./index-ae1PnrUZ.js";import j from"./starBackground-BMTLJT3n.js";import"./index-BtiuLXK9.js";const K="/static/png/circle-bg-CaLcXlFY.png";var F=function(){return F=Object.assign||function(c){for(var e,o=1,n=arguments.length;o<n;o++)for(var t in e=arguments[o])Object.prototype.hasOwnProperty.call(e,t)&&(c[t]=e[t]);return c},F.apply(this,arguments)},X=function(){function c(e,o,n){var t=this;this.endVal=o,this.options=n,this.version="2.8.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(u){t.startTime||(t.startTime=u);var r=u-t.startTime;t.remaining=t.duration-r,t.useEasing?t.countDown?t.frameVal=t.startVal-t.easingFn(r,0,t.startVal-t.endVal,t.duration):t.frameVal=t.easingFn(r,t.startVal,t.endVal-t.startVal,t.duration):t.frameVal=t.startVal+(t.endVal-t.startVal)*(r/t.duration);var d=t.countDown?t.frameVal<t.endVal:t.frameVal>t.endVal;t.frameVal=d?t.endVal:t.frameVal,t.frameVal=Number(t.frameVal.toFixed(t.options.decimalPlaces)),t.printValue(t.frameVal),r<t.duration?t.rAF=requestAnimationFrame(t.count):t.finalEndVal!==null?t.update(t.finalEndVal):t.options.onCompleteCallback&&t.options.onCompleteCallback()},this.formatNumber=function(u){var r,d,i,a,m=u<0?"-":"";r=Math.abs(u).toFixed(t.options.decimalPlaces);var h=(r+="").split(".");if(d=h[0],i=h.length>1?t.options.decimal+h[1]:"",t.options.useGrouping){a="";for(var g=3,l=0,p=0,w=d.length;p<w;++p)t.options.useIndianSeparators&&p===4&&(g=2,l=1),p!==0&&l%g==0&&(a=t.options.separator+a),l++,a=d[w-p-1]+a;d=a}return t.options.numerals&&t.options.numerals.length&&(d=d.replace(/[0-9]/g,function(V){return t.options.numerals[+V]}),i=i.replace(/[0-9]/g,function(V){return t.options.numerals[+V]})),m+t.options.prefix+d+i+t.options.suffix},this.easeOutExpo=function(u,r,d,i){return d*(1-Math.pow(2,-10*u/i))*1024/1023+r},this.options=F(F({},this.defaults),n),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(o),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,this.options.separator===""&&(this.options.useGrouping=!1),this.el=typeof e=="string"?document.getElementById(e):e,this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined",typeof window<"u"&&this.options.enableScrollSpy&&(this.error?console.error(this.error,e):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return t.handleScroll(t)}),window.onscroll=function(){window.onScrollFns.forEach(function(u){return u()})},this.handleScroll(this)))}return c.prototype.handleScroll=function(e){if(e&&window&&!e.once){var o=window.innerHeight+window.scrollY,n=e.el.getBoundingClientRect(),t=n.top+window.pageYOffset,u=n.top+n.height+window.pageYOffset;u<o&&u>window.scrollY&&e.paused?(e.paused=!1,setTimeout(function(){return e.start()},e.options.scrollSpyDelay),e.options.scrollSpyOnce&&(e.once=!0)):(window.scrollY>u||t>o)&&!e.paused&&e.reset()}},c.prototype.determineDirectionAndSmartEasing=function(){var e=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>e;var o=e-this.startVal;if(Math.abs(o)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=e;var n=this.countDown?1:-1;this.endVal=e+n*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=e,this.finalEndVal=null;this.finalEndVal!==null?this.useEasing=!1:this.useEasing=this.options.useEasing},c.prototype.start=function(e){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),e&&(this.options.onCompleteCallback=e),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},c.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},c.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},c.prototype.update=function(e){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(e),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,this.finalEndVal==null&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},c.prototype.printValue=function(e){var o;if(this.el){var n=this.formattingFn(e);(o=this.options.plugin)!==null&&o!==void 0&&o.render?this.options.plugin.render(this.el,n):this.el.tagName==="INPUT"?this.el.value=n:this.el.tagName==="text"||this.el.tagName==="tspan"?this.el.textContent=n:this.el.innerHTML=n}},c.prototype.ensureNumber=function(e){return typeof e=="number"&&!isNaN(e)},c.prototype.validateValue=function(e){var o=Number(e);return this.ensureNumber(o)?o:(this.error="[CountUp] invalid start or end value: ".concat(e),null)},c.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},c}();const z={class:"countup-wrap"},J=P({name:"CountUp",props:{endVal:{},startVal:{default:0},duration:{default:2.5},decimalPlaces:{default:0},autoplay:{type:Boolean,default:!0},loop:{type:[Boolean,Number],default:!1},delay:{default:0},options:{default:void 0}},emits:["init","finished"],setup(c,{expose:e,emit:o}){const n=c,t=o;let u=v(),r=v(),d=0;const i=v(!1);let a;function m(){if(!u.value)return void console.warn("[vue-countup-v3]","elRef can't found");d=0,i.value=!1;const l=Number(n.startVal),p=Number(n.endVal),w=Number(n.duration);r.value=new X(u.value,p,{startVal:l,duration:w,decimalPlaces:n.decimalPlaces,...n.options}),r.value.error?console.error("[vue-countup-v3]",r.value.error):t("init",r.value)}function h(){var l;r.value||m(),(l=r.value)==null||l.start(function(){typeof n.loop=="boolean"&&n.loop||n.loop>d?a=function(p,w=1){const V=v(-1);let E;return V.value=requestAnimationFrame(function B(A){E||(E=A),A-E<1e3*w?V.value=requestAnimationFrame(B):p()}),{cancel:function(){window.cancelAnimationFrame(V.value)}}}(()=>{var p;(p=r.value)==null||p.reset(),h()},n.delay):i.value=!0}),d++}function g(){a==null||a.cancel(),m(),h()}return D([()=>n.startVal,()=>n.endVal],()=>{n.autoplay&&g()}),D(i,l=>{var p;l&&((p=n.options)!=null&&p.onCompleteCallback&&n.options.onCompleteCallback(),t("finished"))}),k(()=>{m(),n.autoplay&&h()}),M(()=>{var l;a==null||a.cancel(),(l=r.value)==null||l.reset()}),e({init:m,restart:g}),(l,p)=>(y(),C("div",z,[N(l.$slots,"prefix"),s("span",{ref_key:"elRef",ref:u},null,512),N(l.$slots,"suffix")]))}}),Q={class:"m-data-screen"},W={class:"header"},Z={class:"date"},tt={class:"center"},et={class:"item"},nt={class:"item-right"},at={class:"item-right-inner"},st={class:"text-title"},it={class:"text-number"},ot={class:"text-der text-decenter-wrapr"},rt={class:"statistic-footer"},lt={class:"footer-item"},ut={style:{color:"green"}},ct={style:{color:"red"}},dt={class:"footer"},pt={class:"left"},ht={class:"item-complex",style:{"margin-bottom":"20px"}},mt={class:"item-complex"},ft={class:"middle"},vt={class:"migration"},gt={class:"right"},Vt={class:"item-complex",style:{"margin-bottom":"20px"}},_t={class:"item-complex"},Dt={__name:"index",setup(c){const e=v(),o=v(),n=v(),t=()=>{let i=new Date,a=i.getHours().toString().padStart(2,"00"),m=i.getMinutes().toString().padStart(2,"00"),h=i.getSeconds().toString().padStart(2,"00");o.value=`${a}:${m}:${h}`,n.value=setTimeout(()=>{n.value&&clearTimeout(n.value),t(),d()},1e3)},u=()=>{let i=new Date,a=i.getFullYear(),m=i.getMonth()+1,h=i.getDate();e.value=`${a}年${m}月${h}日`,t()};k(()=>{u()}),O(()=>{n.value=null});const r=v([{title:"在线用户",value:1e3,percent:78.21,start:0},{title:"消息",value:2e3,percent:78.21,start:0},{title:"销量",value:2550,percent:18.21,start:0},{title:"访问量",value:700,percent:18.21,start:0},{title:"订单",value:1700,percent:18.21,start:0}]);function d(){r.value.forEach(i=>{i.start=i.value,i.value=i.value+=3})}return(i,a)=>{const m=S("CaretTop"),h=S("el-icon"),g=S("CaretBottom");return y(),$(U,null,{default:b(()=>[f(j),s("div",Q,[s("div",W,[a[0]||(a[0]=s("div",{class:"header-bg-title"},"大屏数据展示",-1)),s("div",Z,x(_(e))+" "+x(_(o)),1)]),s("div",tt,[(y(!0),C(Y,null,q(_(r),l=>(y(),C("div",et,[a[5]||(a[5]=s("div",{class:"item-icon item-icon1"},[s("img",{src:K,class:"circle-bg"})],-1)),s("div",nt,[s("div",at,[s("div",st,x(l.title),1),s("div",it,[f(_(J),{"start-val":l.start,loop:!0,"end-val":l.value},null,8,["start-val","end-val"])]),s("div",ot,[s("div",rt,[s("div",lt,[a[3]||(a[3]=s("span",null,"环比",-1)),s("span",ut,[a[1]||(a[1]=T(" 16% ")),f(h,null,{default:b(()=>[f(m)]),_:1})]),a[4]||(a[4]=s("span",null,"同比",-1)),s("span",ct,[a[2]||(a[2]=T(" -16% ")),f(h,null,{default:b(()=>[f(g)]),_:1})])])])])])])]))),256))]),s("div",dt,[s("div",pt,[s("div",ht,[f(_(R),{width:"100%",height:"100%"})]),s("div",mt,[f(_(L))])]),s("div",ft,[s("div",vt,[f(_(I))])]),s("div",gt,[s("div",Vt,[f(H)]),s("div",_t,[f(G)])])])])]),_:1})}}};export{Dt as default};
