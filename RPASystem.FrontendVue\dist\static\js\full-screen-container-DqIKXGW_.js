import{s as f,r as d,M as u,aa as _,o as m,c as p,j as v,av as h,_ as w}from"./index-CX4J5aM5.js";const x={class:"m-full-screen-container"},B=f({__name:"full-screen-container",setup($){const s=d(!0),i=d(),o=()=>{const{clientWidth:e,clientHeight:n}=document.body;var a=1920,r=1080;let c,l,t;e/n>a/r?(t=n/r,l=0,c=(e-a*t)/2):(t=e/a,c=0,l=(n-r*t)/2),s.value&&Object.assign(i.value.style,{transform:`scale(${t})`,left:`${c}px`,top:`${l}px`})};return u(()=>{s.value=!0,o(),window.addEventListener("resize",o)}),_(()=>{s.value=!1,window.removeEventListener("resize",o)}),(e,n)=>(m(),p("div",x,[v("div",{ref_key:"domRef",ref:i,class:"inner"},[h(e.$slots,"default",{},void 0,!0)],512)]))}}),k=w(B,[["__scopeId","data-v-4600d226"]]);export{k as default};
