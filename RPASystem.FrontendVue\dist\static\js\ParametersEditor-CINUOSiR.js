import{Q as i,_ as ae,r as _,w as S,e as te,M as re,x as c,o as s,c as p,i as f,l as n,F as v,K as y,j as I,D as C,t as T,q as m,p as u,k as le,a0 as se,s as oe,P as ne}from"./index-CX4J5aM5.js";function be(o){return i.get("/api/jobtask/search",{params:o})}function ke(){return i.get("/api/exeprogram/GetAllExeProgramsForList")}function he(o){return i.get(`/api/exeprogram/${o}`)}function Ve(o){return i.post("/api/orchestration/createOrcJobTask",o)}function xe(o){return i.delete(`/api/jobtask/${o}`)}function Te(o){return i.post(`/api/jobtask/retry/${o}`)}function Re(o){return i.post(`/api/jobtask/stop/${o}`)}function Ue(o,b){return i.put(`/api/JobTask/update/${o}`,b)}function Ee(o){return i.get(`/api/filestorage/${o}`,{responseType:"blob",headers:{Accept:"application/octet-stream"}})}function ue(){return i.get("/api/ResourcePool")}function ie(){return i.get("/api/ResourcePool/machines")}function ce(){return i.get("/api/RpaCredential")}const me={class:"parameter-name"},de={key:0,class:"required-mark"},pe={key:0,class:"el-upload__tip uploaded-file"},fe={class:"parameter-description"},_e=oe({name:"JobTaskParametersEditor"}),Pe=Object.assign(_e,{props:{initialParameters:{type:String,default:"[]"},programInputParameters:{type:String,required:!0},taskType:{type:Number,default:0}},emits:["update:parameters","file-type-error"],setup(o,{expose:b,emit:O}){const N=o,k=O,l=_({}),P=_([]),R=_([]),U=_([]),E=_([]),h=_(null),w=_({}),F=()=>{const e={};return P.value.forEach(r=>{const d=l.value[r.ParametersName];r.ParametersType==="ResourceSelection"&&Array.isArray(d)?e[r.ParametersName]=d.join("|"):e[r.ParametersName]=d}),e};S(l,()=>{const e=F();k("update:parameters",JSON.stringify(e))},{deep:!0});const j=()=>{const e={};P.value.forEach(r=>{r.IsRequired&&(r.ParametersType==="string"||r.ParametersType==="int")&&(e[r.ParametersName]=[{required:!0,message:`请输入${r.ParametersName}`,trigger:"blur"}])}),w.value=e};S(()=>N.programInputParameters,()=>{try{P.value=JSON.parse(N.programInputParameters);const e=N.initialParameters?JSON.parse(N.initialParameters):{};P.value.forEach(r=>{e[r.ParametersName]?l.value[r.ParametersName]=e[r.ParametersName]:r.DefaultValue&&(r.ParametersType==="string"||r.ParametersType==="int")?l.value[r.ParametersName]=r.DefaultValue:l.value[r.ParametersName]=""}),j()}catch(e){console.error("解析参数失败:",e)}},{immediate:!0});const q=(e,r)=>{if(r==="InputFile"){const d=e.name.toLowerCase();if(!(d.endsWith(".xlsx")||d.endsWith(".xls")))return k("file-type-error","InputFile只能上传xlsx或xls格式的文件"),!1}return!0},A=(e,r)=>{e.fileId?(l.value[r]=e.fileId.toString(),k("update:parameters",JSON.stringify(F()))):console.error("文件上传失败:",e)},D=e=>{console.error("文件上传失败:",e)},M=e=>e,$=te(()=>"/api/FileStorage/uploadForTask"),z=()=>{ne.warning("只能上传一个文件，请先删除已上传的文件")},B=async()=>{try{const e=await ue();R.value=e.data}catch(e){console.error("获取资源池列表失败:",e)}},L=async()=>{try{const e=await ie();U.value=e.data}catch(e){console.error("获取资源机列表失败:",e)}},W=async()=>{try{const e=await ce();E.value=e.data}catch(e){console.error("获取RPA账号凭证列表失败:",e)}},G=async()=>{await Promise.all([B(),L(),W()])};return b({validateForm:async()=>{if(!h.value)return!0;try{return await h.value.validate(),!0}catch{return!1}}}),re(()=>{G()}),(e,r)=>{const d=c("el-input"),V=c("el-form-item"),K=c("el-input-number"),Q=c("el-button"),H=c("el-icon"),X=c("el-tooltip"),Y=c("el-upload"),g=c("el-option"),x=c("el-select"),J=c("el-option-group"),Z=c("el-form");return s(),p("div",null,[f(Z,{model:l.value,"label-width":"120px",ref_key:"formRef",ref:h,rules:w.value},{default:n(()=>[(s(!0),p(v,null,y(P.value,(a,ee)=>(s(),p("div",{key:ee,class:"parameter-row"},[I("span",me,[C(T(a.ParametersName)+" ",1),a.IsRequired?(s(),p("span",de,"*")):m("",!0)]),a.ParametersType==="string"?(s(),u(V,{key:0,prop:a.ParametersName},{default:n(()=>[f(d,{modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,class:"parameter-value",placeholder:a.IsRequired?"请输入"+a.ParametersName:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["prop"])):m("",!0),a.ParametersType==="int"?(s(),u(V,{key:1,prop:a.ParametersName},{default:n(()=>[f(K,{modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,class:"parameter-value"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])):m("",!0),a.ParametersType==="bool"?(s(),u(d,{key:2,modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,class:"parameter-value"},null,8,["modelValue","onUpdate:modelValue"])):m("",!0),a.ParametersType==="file"?(s(),u(Y,{key:3,action:$.value,"on-success":t=>A(t,a.ParametersName),"on-error":D,"before-upload":t=>q(t,a.ParametersName),limit:1,"on-exceed":z,class:"parameter-value upload-compact"},{tip:n(()=>[l.value[a.ParametersName]?(s(),p("div",pe," 已上传: "+T(M(l.value[a.ParametersName])),1)):m("",!0),a.ParametersName==="InputFile"?(s(),u(X,{key:1,content:"只能上传 xlsx/xls 格式文件",placement:"top"},{default:n(()=>[f(H,null,{default:n(()=>[f(le(se))]),_:1})]),_:1})):m("",!0)]),default:n(()=>[f(Q,{size:"small",type:"primary"},{default:n(()=>r[0]||(r[0]=[C("上传文件")])),_:1})]),_:2},1032,["action","on-success","before-upload"])):m("",!0),a.ParametersType==="select"?(s(),u(x,{key:4,modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,multiple:a.ParametersOptions==="2",class:"parameter-value"},{default:n(()=>[(s(!0),p(v,null,y(a.ParametersSelectValue.split("|"),t=>(s(),u(g,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","multiple"])):m("",!0),a.ParametersType==="ResourceSelection"?(s(),u(x,{key:5,modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,multiple:"",filterable:"",placeholder:"请选择资源",class:"parameter-value"},{default:n(()=>[f(J,{label:"资源池"},{default:n(()=>[(s(!0),p(v,null,y(R.value,t=>(s(),u(g,{key:"pool_"+t.id,label:t.poolName,value:t.poolName},null,8,["label","value"]))),128))]),_:1}),f(J,{label:"资源机"},{default:n(()=>[(s(!0),p(v,null,y(U.value,t=>(s(),u(g,{key:"machine_"+t.id,label:t.machineName,value:t.machineName},null,8,["label","value"]))),128))]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])):m("",!0),a.ParametersType==="RpaCredentials"?(s(),u(x,{key:6,modelValue:l.value[a.ParametersName],"onUpdate:modelValue":t=>l.value[a.ParametersName]=t,filterable:"",placeholder:"请选择RPA账号",class:"parameter-value"},{default:n(()=>[(s(!0),p(v,null,y(E.value,t=>(s(),u(g,{key:t.id,label:t.username,value:t.username},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):m("",!0),I("span",fe,T(a.ParametersDescription),1)]))),128))]),_:1},8,["model","rules"])])}}}),ve=ae(Pe,[["__scopeId","data-v-5680280e"]]),we=Object.freeze(Object.defineProperty({__proto__:null,default:ve},Symbol.toStringTag,{value:"Module"}));export{ve as J,we as P,Re as a,he as b,Ve as c,xe as d,Ee as e,ke as g,Te as r,be as s,Ue as u};
