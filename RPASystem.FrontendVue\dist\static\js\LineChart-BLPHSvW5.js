import{i as c}from"./index-BtiuLXK9.js";import{r as u,w as h,M as d,o as f,c as m,V as p,B as y,v as g}from"./index-CX4J5aM5.js";const S={__name:"LineChart",props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"350px"},autoResize:{type:<PERSON><PERSON>an,default:!0},chartData:{type:Object,required:!0}},setup(t){const{proxy:o}=g(),s=u(null),i=t;let a=null;h(()=>i.chartData,e=>{n(e)},{deep:!0});function n({expectedData:e,actualData:r}={}){a.setOption({xAxis:{data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],boundaryGap:!1,axisTick:{show:!1}},title:{text:"图表测试数据"},grid:{left:10,right:10,bottom:20,top:60,containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},yAxis:{axisTick:{show:!1}},legend:{data:["expected","actual"]},series:[{name:"expected",itemStyle:{color:"#FF005A",lineStyle:{color:"#FF005A",width:2}},smooth:!0,type:"line",data:e,animationDuration:2800,animationEasing:"cubicInOut"},{name:"actual",smooth:!0,type:"line",itemStyle:{color:"#3888fa",areaStyle:{color:"#f3f8ff"},lineStyle:{color:"#3888fa",width:2}},data:r,animationDuration:2800,animationEasing:"quadraticOut"}]})}function l(){a=c(o.$refs.chartRef,"macarons"),n(i.chartData)}return d(()=>{l()}),window.onresize=function(){a.resize()},(e,r)=>(f(),m("div",{ref_key:"chartRef",ref:s,class:p(t.className),style:y({height:t.height,width:t.width})},null,6))}};export{S as default};
