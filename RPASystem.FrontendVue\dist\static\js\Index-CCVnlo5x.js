import{Q as _,_ as Q,r as n,M as q,x as i,N as A,o as y,c as D,j as b,i as o,l,D as c,z as H,O as G,p as P,F as J,K as W,t as X,s as Y,P as f,H as Z}from"./index-CX4J5aM5.js";function ee(){return _.get("/api/resourcepool")}function oe(){return _.get("/api/resourcepool/machines")}function ae(d){return _.post("/api/resourcepool",d)}function le(d){return _.put("/api/resourcepool",d)}function te(d){return _.delete(`/api/resourcepool/${d}`)}const re={class:"resource-pool-manager"},se={class:"operation-bar"},ne={class:"left-section"},ie={class:"right-section"},ue={class:"dialog-footer"},ce=Y({name:"resourcepoolmanager"}),de=Object.assign(ce,{setup(d){const k=n([]),M=n([]),p=n(!1),u=n(!1),m=n([]),N=n(null),C=n(""),g=n(!1),s=n({poolName:"",description:"",resourceMachineNames:""}),B={poolName:[{required:!0,message:"请输入资源池名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],selectedMachines:[{validator:(a,e,r)=>{m.value.length===0?r(new Error("请至少选择一个资源机")):r()},trigger:"change"}]},w=async()=>{g.value=!0;try{const a=await ee();k.value=a.data,g.value=!1}catch(a){f.error("获取资源池列表失败"),console.error("获取资源池列表失败:",a),g.value=!1}},E=async()=>{try{const a=await oe();M.value=a.data.map(e=>({machineName:e.machineName,key:e.machineName}))}catch(a){f.error("获取资源机列表失败"),console.error("获取资源机列表失败:",a)}},T=()=>{u.value=!1,s.value={poolName:"",description:"",resourceMachineNames:""},m.value=[],p.value=!0},U=a=>{u.value=!0,s.value={...a},m.value=a.machineList||[],p.value=!0},z=async a=>{try{await Z.confirm("确定要删除该资源池吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),await te(a.id),f.success("删除成功"),await w()}catch(e){e!=="cancel"&&(f.error("删除失败"),console.error("删除失败:",e))}},L=async()=>{if(N.value)try{await N.value.validate(),s.value.resourceMachineNames=m.value.join("|"),u.value?await le(s.value):await ae(s.value),f.success(u.value?"更新成功":"创建成功"),p.value=!1,await w()}catch(a){a!=="cancel"&&(f.error(u.value?"更新失败":"创建失败"),console.error(u.value?"更新失败:":"创建失败:",a))}},S=(a,e,r)=>r?new Date(r).toLocaleString():"",R=()=>{w()};return q(async()=>{await E(),await w()}),(a,e)=>{const r=i("el-button"),x=i("el-input"),v=i("el-table-column"),$=i("el-tag"),j=i("el-table"),V=i("el-form-item"),K=i("el-transfer"),F=i("el-form"),I=i("el-dialog"),O=A("loading");return y(),D("div",re,[b("div",se,[b("div",ne,[o(x,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=t=>C.value=t),placeholder:"请输入资源池名称搜索",style:{width:"300px"},clearable:"",onKeyup:H(R,["enter"])},{append:l(()=>[o(r,{onClick:R},{default:l(()=>e[6]||(e[6]=[c("搜索")])),_:1})]),_:1},8,["modelValue"])]),b("div",ie,[o(r,{type:"primary",onClick:T},{default:l(()=>e[7]||(e[7]=[c("新增资源池")])),_:1})])]),G((y(),P(j,{data:k.value,style:{width:"100%"}},{default:l(()=>[o(v,{prop:"poolName",label:"资源池名称",width:"180"}),o(v,{prop:"description",label:"描述"}),o(v,{label:"包含资源机","min-width":"300"},{default:l(t=>[(y(!0),D(J,null,W(t.row.machineList,h=>(y(),P($,{key:h,class:"mx-1",size:"small"},{default:l(()=>[c(X(h),1)]),_:2},1024))),128))]),_:1}),o(v,{prop:"createdTime",label:"创建时间",width:"180",formatter:S}),o(v,{label:"操作",width:"180",fixed:"right"},{default:l(t=>[o(r,{size:"small",onClick:h=>U(t.row)},{default:l(()=>e[8]||(e[8]=[c("编辑")])),_:2},1032,["onClick"]),o(r,{size:"small",type:"danger",onClick:h=>z(t.row)},{default:l(()=>e[9]||(e[9]=[c("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[O,g.value]]),o(I,{modelValue:p.value,"onUpdate:modelValue":e[5]||(e[5]=t=>p.value=t),title:u.value?"编辑资源池":"新增资源池",width:"1200px","destroy-on-close":""},{footer:l(()=>[b("span",ue,[o(r,{onClick:e[4]||(e[4]=t=>p.value=!1)},{default:l(()=>e[10]||(e[10]=[c("取消")])),_:1}),o(r,{type:"primary",onClick:L},{default:l(()=>e[11]||(e[11]=[c("确定")])),_:1})])]),default:l(()=>[o(F,{model:s.value,"label-width":"100px",rules:B,ref_key:"formRef",ref:N},{default:l(()=>[o(V,{label:"资源池名称",prop:"poolName"},{default:l(()=>[o(x,{modelValue:s.value.poolName,"onUpdate:modelValue":e[1]||(e[1]=t=>s.value.poolName=t),placeholder:"请输入资源池名称"},null,8,["modelValue"])]),_:1}),o(V,{label:"描述",prop:"description"},{default:l(()=>[o(x,{modelValue:s.value.description,"onUpdate:modelValue":e[2]||(e[2]=t=>s.value.description=t),type:"textarea",rows:2,placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),o(V,{label:"资源机",prop:"selectedMachines"},{default:l(()=>[o(K,{modelValue:m.value,"onUpdate:modelValue":e[3]||(e[3]=t=>m.value=t),data:M.value,filterable:"",titles:["可用资源机","已选资源机"],props:{key:"machineName",label:"machineName"}},null,8,["modelValue","data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),me=Q(de,[["__scopeId","data-v-d4207bf6"]]);export{me as default};
