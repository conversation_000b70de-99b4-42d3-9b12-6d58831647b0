import{ai as V,r as b,E as J,a5 as Se,x as r,N as W,o as s,c as F,O as v,S as Ne,k as n,i as l,l as e,D as f,F as X,K as Y,p as _,t as y,A as Ue,m as Z,q as R,a6 as De,s as Be,v as Pe}from"./index-CX4J5aM5.js";import{i as Re}from"./index-CMuNoVkf.js";function xe(g){return V({url:"business/Testtable/list",method:"get",params:g})}function Ie(g){return V({url:"business/Testtable",method:"post",data:g})}function Me(g){return V({url:"business/Testtable",method:"PUT",data:g})}function ee(g){return V({url:"business/Testtable/"+g,method:"get"})}function Fe(g){return V({url:"business/Testtable/delete/"+g,method:"POST"})}function ze(){return V({url:"business/Testtable/clean",method:"POST"})}const Le=Be({name:"testtable"}),Oe=Object.assign(Le,{setup(g){const{proxy:u}=Pe(),x=b([]),I=b(!1),U=b(!0),i=J({pageNum:1,pageSize:10,sort:"Id",sortType:"desc",name:void 0,myType:void 0}),D=b([{visible:!0,align:"center",type:"",prop:"id",label:"Id"},{visible:!0,align:"center",type:"",prop:"name",label:"Name",showOverflowTooltip:!0},{visible:!0,align:"center",type:"dict",prop:"myType",label:"MyType",dictType:"rpa_run_type"}]),z=b(0),L=b([]),te=b();b([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]);var le=["rpa_run_type"];u.getDicts(le).then(o=>{o.data.forEach(t=>{q.options[t.dictType]=t.list})});function C(){I.value=!0,xe(i).then(o=>{const{code:t,data:d}=o;t==200&&(L.value=d.result,z.value=d.totalNum,I.value=!1)})}function S(){i.pageNum=1,C()}function ne(){u.resetForm("queryRef"),S()}function oe(o){x.value=o.map(t=>t.id),E.value=o.length!=1,O.value=!o.length}function ae(o){var t=void 0,d=void 0;o.prop!=null&&o.order!=null&&(t=o.prop,d=o.order),i.sort=t,i.sortType=d,S()}const re=b(),B=b(""),$=b(0),h=b(!1),q=J({single:!0,multiple:!0,form:{},rules:{},options:{rpa_run_type:[]}}),{form:p,rules:ue,options:M,single:E,multiple:O}=Se(q);function se(){h.value=!1,P()}function P(){p.value={id:null,name:null,myType:null},u.resetForm("formRef")}function ie(o){P();const t=o.id;ee(t).then(d=>{const{code:c,data:T}=d;c==200&&(h.value=!0,B.value="查看",$.value=3,p.value={...T})})}function de(){P(),h.value=!0,B.value="添加只是测试",$.value=1}function Q(o){P();const t=o.id||x.value;ee(t).then(d=>{const{code:c,data:T}=d;c==200&&(h.value=!0,B.value="修改只是测试",$.value=2,p.value={...T})})}function pe(){u.$refs.formRef.validate(o=>{o&&(p.value.id!=null&&$.value===2?Me(p.value).then(t=>{u.$modal.msgSuccess("修改成功"),h.value=!1,C()}):Ie(p.value).then(t=>{u.$modal.msgSuccess("新增成功"),h.value=!1,C()}))})}function A(o){const t=o.id||x.value;u.$confirm('是否确认删除参数编号为"'+t+'"的数据项？',"警告",{confirmButtonText:u.$t("common.ok"),cancelButtonText:u.$t("common.cancel"),type:"warning"}).then(function(){return Fe(t)}).then(()=>{C(),u.$modal.msgSuccess("删除成功")})}function ce(){u.$confirm("是否确认清空所有数据项?","警告",{confirmButtonText:u.$t("common.ok"),cancelButtonText:u.$t("common.cancel"),type:"warning"}).then(function(){return ze()}).then(()=>{S(),u.$modal.msgSuccess("清空成功")})}const me=o=>{const{item1:t,item2:d}=o.data;var c="";d.forEach(T=>{c+=T.storageMessage+","}),u.$alert(t+"<p>"+c+"</p>","导入结果",{dangerouslyUseHTMLString:!0}),C()};function fe(){u.$confirm("是否确认导出只是测试数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await u.downFile("/business/Testtable/export",{...i})})}return S(),(o,t)=>{const d=r("el-input"),c=r("el-form-item"),T=r("el-radio"),j=r("el-radio-group"),m=r("el-button"),H=r("el-form"),w=r("el-col"),_e=r("arrow-down"),be=r("el-icon"),ye=r("el-dropdown-item"),ge=r("el-dropdown-menu"),ve=r("el-dropdown"),he=r("right-toolbar"),K=r("el-row"),N=r("el-table-column"),Te=r("dict-tag"),we=r("el-table"),ke=r("pagination"),Ce=r("el-input-number"),$e=r("el-dialog"),k=W("hasPermi"),Ve=W("loading");return s(),F("div",null,[v(l(H,{model:n(i),"label-position":"right",inline:"",ref_key:"queryRef",ref:te,onSubmit:t[2]||(t[2]=Ue(()=>{},["prevent"]))},{default:e(()=>[l(c,{label:"Name",prop:"name"},{default:e(()=>[l(d,{modelValue:n(i).name,"onUpdate:modelValue":t[0]||(t[0]=a=>n(i).name=a),placeholder:"请输入Name"},null,8,["modelValue"])]),_:1}),l(c,{label:"MyType",prop:"myType"},{default:e(()=>[l(j,{modelValue:n(i).myType,"onUpdate:modelValue":t[1]||(t[1]=a=>n(i).myType=a)},{default:e(()=>[l(T,null,{default:e(()=>t[10]||(t[10]=[f("全部")])),_:1}),(s(!0),F(X,null,Y(n(M).rpa_run_type,a=>(s(),_(T,{key:a.dictValue,value:a.dictValue},{default:e(()=>[f(y(a.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,null,{default:e(()=>[l(m,{icon:"search",type:"primary",onClick:S},{default:e(()=>[f(y(o.$t("btn.search")),1)]),_:1}),l(m,{icon:"refresh",onClick:ne},{default:e(()=>[f(y(o.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[Ne,n(U)]]),l(K,{gutter:15,class:"mb10"},{default:e(()=>[l(w,{span:1.5},{default:e(()=>[v((s(),_(m,{type:"primary",plain:"",icon:"plus",onClick:de},{default:e(()=>[f(y(o.$t("btn.add")),1)]),_:1})),[[k,["testtable:add"]]])]),_:1}),l(w,{span:1.5},{default:e(()=>[v((s(),_(m,{type:"success",disabled:n(E),plain:"",icon:"edit",onClick:Q},{default:e(()=>[f(y(o.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[k,["testtable:edit"]]])]),_:1}),l(w,{span:1.5},{default:e(()=>[v((s(),_(m,{type:"danger",disabled:n(O),plain:"",icon:"delete",onClick:A},{default:e(()=>[f(y(o.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[k,["testtable:delete"]]])]),_:1}),l(w,{span:1.5},{default:e(()=>[v((s(),_(m,{type:"danger",plain:"",icon:"delete",onClick:ce},{default:e(()=>[f(y(o.$t("btn.clean")),1)]),_:1})),[[k,["testtable:delete"]]])]),_:1}),l(w,{span:1.5},{default:e(()=>[v((s(),_(ve,{trigger:"click"},{dropdown:e(()=>[l(ge,null,{default:e(()=>[l(ye,{command:"upload"},{default:e(()=>[l(n(Re),{templateUrl:"business/Testtable/importTemplate",importUrl:"/business/Testtable/importData",onSuccess:me})]),_:1})]),_:1})]),default:e(()=>[l(m,{type:"primary",plain:"",icon:"Upload"},{default:e(()=>[f(y(o.$t("btn.import")),1),l(be,{class:"el-icon--right"},{default:e(()=>[l(_e)]),_:1})]),_:1})]),_:1})),[[k,["testtable:import"]]])]),_:1}),l(w,{span:1.5},{default:e(()=>[v((s(),_(m,{type:"warning",plain:"",icon:"download",onClick:fe},{default:e(()=>[f(y(o.$t("btn.export")),1)]),_:1})),[[k,["testtable:export"]]])]),_:1}),l(he,{showSearch:n(U),"onUpdate:showSearch":t[3]||(t[3]=a=>Z(U)?U.value=a:null),onQueryTable:C,columns:n(D)},null,8,["showSearch","columns"])]),_:1}),v((s(),_(we,{data:n(L),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:ae,onSelectionChange:oe},{default:e(()=>[l(N,{type:"selection",width:"50",align:"center"}),n(D).showColumn("id")?(s(),_(N,{key:0,prop:"id",label:"Id",align:"center"})):R("",!0),n(D).showColumn("name")?(s(),_(N,{key:1,prop:"name",label:"Name",align:"center","show-overflow-tooltip":!0})):R("",!0),n(D).showColumn("myType")?(s(),_(N,{key:2,prop:"myType",label:"MyType",align:"center",sortable:""},{default:e(a=>[l(Te,{options:n(M).rpa_run_type,value:a.row.myType},null,8,["options","value"])]),_:1})):R("",!0),l(N,{label:"操作",width:"160"},{default:e(a=>[l(m,{type:"primary",size:"small",icon:"view",title:"详情",onClick:G=>ie(a.row)},null,8,["onClick"]),v(l(m,{type:"success",size:"small",icon:"edit",title:"编辑",onClick:G=>Q(a.row)},null,8,["onClick"]),[[k,["testtable:edit"]]]),v(l(m,{type:"danger",size:"small",icon:"delete",title:"删除",onClick:G=>A(a.row)},null,8,["onClick"]),[[k,["testtable:delete"]]])]),_:1})]),_:1},8,["data"])),[[Ve,n(I)]]),l(ke,{total:n(z),page:n(i).pageNum,"onUpdate:page":t[4]||(t[4]=a=>n(i).pageNum=a),limit:n(i).pageSize,"onUpdate:limit":t[5]||(t[5]=a=>n(i).pageSize=a),onPagination:C},null,8,["total","page","limit"]),l($e,{title:n(B),"lock-scroll":!1,modelValue:n(h),"onUpdate:modelValue":t[9]||(t[9]=a=>Z(h)?h.value=a:null)},De({default:e(()=>[l(H,{ref_key:"formRef",ref:re,model:n(p),rules:n(ue),"label-width":"100px"},{default:e(()=>[l(K,{gutter:20},{default:e(()=>[n($)!=1?(s(),_(w,{key:0,lg:12},{default:e(()=>[l(c,{label:"Id",prop:"id"},{default:e(()=>[l(Ce,{modelValue:n(p).id,"onUpdate:modelValue":t[6]||(t[6]=a=>n(p).id=a),modelModifiers:{number:!0},"controls-position":"right",placeholder:"请输入Id",disabled:!0},null,8,["modelValue"])]),_:1})]),_:1})):R("",!0),l(w,{lg:12},{default:e(()=>[l(c,{label:"Name",prop:"name"},{default:e(()=>[l(d,{modelValue:n(p).name,"onUpdate:modelValue":t[7]||(t[7]=a=>n(p).name=a),placeholder:"请输入Name"},null,8,["modelValue"])]),_:1})]),_:1}),l(w,{lg:12},{default:e(()=>[l(c,{label:"MyType",prop:"myType"},{default:e(()=>[l(j,{modelValue:n(p).myType,"onUpdate:modelValue":t[8]||(t[8]=a=>n(p).myType=a)},{default:e(()=>[(s(!0),F(X,null,Y(n(M).rpa_run_type,a=>(s(),_(T,{key:a.dictValue,value:parseInt(a.dictValue)},{default:e(()=>[f(y(a.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:2},[n($)!=3?{name:"footer",fn:e(()=>[l(m,{text:"",onClick:se},{default:e(()=>[f(y(o.$t("btn.cancel")),1)]),_:1}),l(m,{type:"primary",onClick:pe},{default:e(()=>[f(y(o.$t("btn.submit")),1)]),_:1})]),key:"0"}:void 0]),1032,["title","modelValue"])])}}});export{Oe as default};
