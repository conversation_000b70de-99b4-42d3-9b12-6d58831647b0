import{r as F,E as le,a5 as te,x as m,o as r,p as f,l as e,i as l,D as s,t as d,k as n,j as c,c as w,q as g,F as R,K as q,m as ne,aD as ae,aE as oe,aF as ue,v as me}from"./index-CX4J5aM5.js";import{I as de}from"./index-DeSrdQqi.js";import"./requireIcons-B3C5g2-d.js";const se={key:0},fe={__name:"menuForm",props:{options:{},menuOptions:{}},setup(h,{expose:M,emit:K}){const{proxy:v}=me(),T=K,P=F(null),b=F(!1),I=F(""),A=F(null),B=le({form:{},rules:{menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],menuNameKey:[{pattern:/^[A-Za-z].+$/,message:"输入格式不正确",trigger:"blur"}],orderNum:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}],path:[{required:!1,message:"路由地址不能为空",trigger:"blur"},{pattern:/^[/A-Za-z].+$/,message:"输入格式不正确，字母开头",trigger:"blur"}],visible:[{required:!0,message:"显示状态不能为空",trigger:"blur"}]},sys_show_hide:[],sys_normal_disable:[]}),{form:t,rules:L}=te(B);function D(){b.value=!1,U()}function U(){t.value={menuId:void 0,parentId:0,menuName:void 0,icon:void 0,menuType:"M",orderNum:999,isFrame:"0",isCache:"0",visible:"0",status:"0"},v.resetForm("menuRef")}function E(u){t.value.icon=u}function O(u){U(),u!=null&&u.menuId!=null?t.value.parentId=u.menuId:t.value.parentId=0,b.value=!0,I.value=v.$t("btn.add")}async function Z(u){U(),ae(u.menuId).then(o=>{t.value=o.data,b.value=!0,I.value=v.$t("btn.edit")})}function j(){v.$refs.menuRef.validate(u=>{u&&(t.value.menuId!=null?oe(t.value).then(()=>{v.$modal.msgSuccess("修改成功"),b.value=!1,T("success",t.value.parentId)}):ue(t.value).then(()=>{v.$modal.msgSuccess("新增成功"),b.value=!1,T("success",t.value.parentId)}))})}return M({handleAdd:O,handleUpdate:Z}),(u,o)=>{const G=m("el-cascader"),p=m("el-form-item"),i=m("el-col"),C=m("el-radio-button"),k=m("el-radio-group"),$=m("el-input"),y=m("questionFilled"),_=m("el-icon"),V=m("el-tooltip"),H=m("svg-icon"),J=m("search"),Q=m("el-popover"),W=m("el-input-number"),N=m("el-radio"),X=m("el-switch"),Y=m("el-row"),x=m("el-form"),S=m("el-button"),ee=m("el-dialog");return r(),f(ee,{title:n(I),modelValue:n(b),"onUpdate:modelValue":o[13]||(o[13]=a=>ne(b)?b.value=a:null),width:"720px","append-to-body":""},{footer:e(()=>[l(S,{text:"",onClick:D},{default:e(()=>[s(d(u.$t("btn.cancel")),1)]),_:1}),l(S,{type:"primary",onClick:j},{default:e(()=>[s(d(u.$t("btn.submit")),1)]),_:1})]),default:e(()=>[l(x,{ref_key:"menuRef",ref:A,model:n(t),rules:n(L),"label-width":"110px"},{default:e(()=>[l(Y,null,{default:e(()=>[l(i,{lg:24},{default:e(()=>[l(p,{label:u.$t("m.parentMenu")},{default:e(()=>[l(G,{class:"w100",options:h.menuOptions,props:{checkStrictly:!0,value:"menuId",label:"menuName",emitPath:!1},placeholder:"请选择上级菜单",clearable:"",modelValue:n(t).parentId,"onUpdate:modelValue":o[0]||(o[0]=a=>n(t).parentId=a)},{default:e(({node:a,data:z})=>[c("span",null,d(z.menuName),1),a.isLeaf?g("",!0):(r(),w("span",se," ("+d(z.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1},8,["label"])]),_:1}),l(i,{lg:24},{default:e(()=>[l(p,{label:u.$t("m.menuType"),prop:"menuType"},{default:e(()=>[l(k,{modelValue:n(t).menuType,"onUpdate:modelValue":o[1]||(o[1]=a=>n(t).menuType=a)},{default:e(()=>[l(C,{value:"M"},{default:e(()=>[s(d(u.$t("m.directory"))+"M",1)]),_:1}),l(C,{value:"C"},{default:e(()=>[s(d(u.$t("m.menu"))+"C",1)]),_:1}),l(C,{value:"F"},{default:e(()=>[s(d(u.$t("m.button"))+"F",1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1}),l(i,{lg:12},{default:e(()=>[l(p,{label:u.$t("m.menuName"),prop:"menuName"},{default:e(()=>[l($,{modelValue:n(t).menuName,"onUpdate:modelValue":o[2]||(o[2]=a=>n(t).menuName=a),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),l(i,{lg:12},{default:e(()=>[l(p,{label:"菜单名",prop:"menuNameKey"},{label:e(()=>[c("span",null,[l(V,{content:"多语言翻译key：eg：menu.system，不需要多语言的可不用填写",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.menuNameKey")),1)])]),default:e(()=>[l($,{modelValue:n(t).menuNameKey,"onUpdate:modelValue":o[3]||(o[3]=a=>n(t).menuNameKey=a),placeholder:"请输入菜单名翻译key"},null,8,["modelValue"])]),_:1})]),_:1}),n(t).menuType!="F"?(r(),f(i,{key:0,lg:12},{default:e(()=>[l(p,{label:u.$t("m.icon"),prop:"icon"},{default:e(()=>[l(Q,{placement:"bottom-start",width:540,trigger:"click"},{reference:e(()=>[l($,{modelValue:n(t).icon,"onUpdate:modelValue":o[4]||(o[4]=a=>n(t).icon=a),placeholder:"点击选择图标",readonly:""},{prefix:e(()=>[n(t).icon?(r(),f(H,{key:0,name:n(t).icon},null,8,["name"])):(r(),f(_,{key:1},{default:e(()=>[l(J)]),_:1}))]),_:1},8,["modelValue"])]),default:e(()=>[l(n(de),{ref_key:"iconSelectRef",ref:P,onSelected:E},null,512)]),_:1})]),_:1},8,["label"])]),_:1})):g("",!0),l(i,{lg:12},{default:e(()=>[l(p,{label:u.$t("m.sort"),prop:"orderNum"},{default:e(()=>[l(W,{modelValue:n(t).orderNum,"onUpdate:modelValue":o[5]||(o[5]=a=>n(t).orderNum=a),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),n(t).menuType!="F"?(r(),f(i,{key:1,lg:12},{default:e(()=>[l(p,{prop:"path"},{label:e(()=>[c("span",null,[l(V,{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.routePath")),1)])]),default:e(()=>[l($,{modelValue:n(t).path,"onUpdate:modelValue":o[6]||(o[6]=a=>n(t).path=a),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1})]),_:1})):g("",!0),n(t).menuType!="F"?(r(),f(i,{key:2,lg:12},{default:e(()=>[l(p,{prop:"component"},{label:e(()=>[c("span",null,[l(V,{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.componentPath")),1)])]),default:e(()=>[l($,{modelValue:n(t).component,"onUpdate:modelValue":o[7]||(o[7]=a=>n(t).component=a),placeholder:"请输入组件路径"},{prepend:e(()=>o[14]||(o[14]=[c("span",{style:{width:"40px"}},"src/views/",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1})):g("",!0),l(i,{lg:12},{default:e(()=>[l(p,null,{label:e(()=>[c("span",null,[l(V,{content:"控制器中定义的权限字符，如：[ActionPermissionFilter(Permission = 'system:user:delete')])",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.permissionStr")),1)])]),default:e(()=>[l($,{modelValue:n(t).perms,"onUpdate:modelValue":o[8]||(o[8]=a=>n(t).perms=a),placeholder:"请输入权限标识",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1}),n(t).menuType!="F"?(r(),f(i,{key:3,lg:12},{default:e(()=>[l(p,null,{label:e(()=>[c("span",null,[l(V,{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.isFrame")),1)])]),default:e(()=>[l(k,{modelValue:n(t).isFrame,"onUpdate:modelValue":o[9]||(o[9]=a=>n(t).isFrame=a)},{default:e(()=>[l(N,{value:"0"},{default:e(()=>[s(d(u.$t("common.no")),1)]),_:1}),l(N,{value:"1"},{default:e(()=>[s(d(u.$t("common.yes")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):g("",!0),n(t).menuType=="C"?(r(),f(i,{key:4,lg:12},{default:e(()=>[l(p,{prop:"isCache"},{label:e(()=>[c("span",null,[l(V,{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.isCache")),1)])]),default:e(()=>[l(X,{modelValue:n(t).isCache,"onUpdate:modelValue":o[10]||(o[10]=a=>n(t).isCache=a),"active-value":"0","inactive-value":"1"},null,8,["modelValue"])]),_:1})]),_:1})):g("",!0),n(t).menuType!="F"?(r(),f(i,{key:5,lg:12},{default:e(()=>[l(p,{prop:"visible"},{label:e(()=>[c("span",null,[l(V,{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.isShow")),1)])]),default:e(()=>[l(k,{modelValue:n(t).visible,"onUpdate:modelValue":o[11]||(o[11]=a=>n(t).visible=a)},{default:e(()=>[(r(!0),w(R,null,q(h.options.sys_show_hide,a=>(r(),f(N,{key:a.dictValue,value:a.dictValue},{default:e(()=>[s(d(a.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):g("",!0),n(t).menuType!="F"?(r(),f(i,{key:6,lg:12},{default:e(()=>[l(p,null,{label:e(()=>[c("span",null,[l(V,{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"},{default:e(()=>[l(_,{size:15},{default:e(()=>[l(y)]),_:1})]),_:1}),s(" "+d(u.$t("m.menuState")),1)])]),default:e(()=>[l(k,{modelValue:n(t).status,"onUpdate:modelValue":o[12]||(o[12]=a=>n(t).status=a)},{default:e(()=>[(r(!0),w(R,null,q(h.options.sys_normal_disable,a=>(r(),f(N,{key:a.dictValue,value:a.dictValue},{default:e(()=>[s(d(a.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):g("",!0)]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])}}};export{fe as default};
