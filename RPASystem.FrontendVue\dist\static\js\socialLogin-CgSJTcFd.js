import{_ as L,d as C,r as e,C as j,c as a,i as c,l as f,k as t,j as r,s as B,v as M,u as N,a as R,x as i,o as u,t as p,y as v,D as T}from"./index-CX4J5aM5.js";const V={class:"login"},q={class:"title"},w={key:0,style:{"text-align":"center",color:"red"},class:"pb20"},D={key:1,class:"loading"},H={class:"el-login-footer"},I=["innerHTML"],O=B({name:"socialLogin"}),Q=Object.assign(O,{setup($){const{proxy:h}=M(),_=N(),m=R(),y=C(),k=e(void 0),o=e(void 0),d=e(),g=e(""),n=e(!1);o.value=j(),d.value=_.query.redirect,g.value=_.query.authSource;const l=e("未获取到授权信息，请返回重新授权登录");return o.value&&o.value.state!=null&&(n.value=!0,y.oauthLogin(o.value,{authSource:g.value}).then(()=>{l.value=h.$t("login.loginSuccess"),m.push({path:d.value||"/"})}).catch(s=>{console.error("login-error",s),l.value=s.msg}).finally(()=>{n.value=!1})),(s,E)=>{const S=i("el-result"),b=i("router-link"),x=i("el-form");return u(),a("div",V,[c(x,{ref:"loginRef",model:t(k),class:"login-form"},{default:f(()=>[r("h3",q,p(t(v).title),1),t(n)?(u(),a("div",D,"登 录 中...")):(u(),a("div",w,[c(S,{icon:"warning","sub-title":t(l)},null,8,["sub-title"]),c(b,{class:"link-type",to:"/login"},{default:f(()=>[T("返回"+p(s.$t("login.btnLogin")),1)]),_:1})]))]),_:1},8,["model"]),r("div",H,[r("div",{innerHTML:t(v).copyright},null,8,I)])])}}}),U=L(Q,[["__scopeId","data-v-8e1f13ec"]]);export{U as default};
