﻿using RPASystem.Service.Common;

namespace ZR.Admin.WebApi.RPA.Common
{
    public class VersionMan
    {
        private const string VERSION_FILE = "version.txt";
        private const string ClientPackageType = "ClientPackage";
        /// <summary>
        /// 获取当前版本号
        /// </summary>
        /// <returns></returns>
        public static async Task<string> GetCurrentVersion()
        {
            var versionPath = FileUploadManager.GetFileRealPath(ClientPackageType, VERSION_FILE);
            if (File.Exists(versionPath))
            {
                return await File.ReadAllTextAsync(versionPath);
            }
            return "";
        }
        /// <summary>
        /// 设置当前版本号
        /// </summary>
        /// <param name="version"></param>
        public static async void SetCurrentVersion(string version)
        {
            var versionPath = FileUploadManager.GetFileRealPath(ClientPackageType, VERSION_FILE);
            await File.WriteAllTextAsync(versionPath, version);
        }
        /// <summary>
        /// 获取zip包文件路径
        /// </summary>
        /// <returns></returns>
        public static string GetZipFilePath()
        {
            return FileUploadManager.GetFileRealPath(ClientPackageType, "latest.zip");
        }

    }
}
