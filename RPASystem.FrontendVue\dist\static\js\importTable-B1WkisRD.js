import{c as G,l as Q,i as j}from"./gen-BRdzRWqq.js";import{r,E as y,x as s,o as g,p as V,l as u,i as l,D as _,k as o,c as A,F as H,K as J,m as x,v as M}from"./index-CX4J5aM5.js";const Y={__name:"importTable",emits:["ok"],setup(O,{expose:C,emit:S}){const m=r(0),i=r(!1),d=r([]),v=r([]),{proxy:N}=M(),k=r([]),n=y({dbName:"",pageNum:1,pageSize:10,tableName:void 0}),T=y({dbName:[{required:!0,message:"请选择数据库名称",trigger:"blur"}]}),L=S;function R(){p(),i.value=!0}function U(a){N.$refs.table.toggleRowSelection(a)}function B(a){d.value=a}function p(){G().then(a=>{k.value=a.data.dbList}),n.dbName!==""&&Q(n).then(a=>{v.value=a.data.result,m.value=a.data.totalNum})}function w(){n.pageNum=1,p()}function $(){j({tables:d.value,dbName:n.dbName,frontTpl:2}).then(a=>{N.$modal.msgSuccess(a.msg),a.code===200&&(i.value=!1,L("ok"))})}function h(a){return a.name}return C({show:R}),(a,e)=>{const q=s("el-option"),z=s("el-select"),b=s("el-form-item"),D=s("el-input"),f=s("el-button"),E=s("el-form"),c=s("el-table-column"),F=s("el-table"),I=s("pagination"),K=s("el-row"),P=s("el-dialog");return g(),V(P,{title:"导入表",modelValue:o(i),"onUpdate:modelValue":e[7]||(e[7]=t=>x(i)?i.value=t:null),width:"900px",top:"5vh","append-to-body":""},{footer:u(()=>[l(f,{text:"",onClick:e[6]||(e[6]=t=>i.value=!1)},{default:u(()=>e[9]||(e[9]=[_("取 消")])),_:1}),l(f,{type:"primary",disabled:o(d).length<=0,onClick:$},{default:u(()=>e[10]||(e[10]=[_("确 定")])),_:1},8,["disabled"])]),default:u(()=>[l(E,{ref:"queryRef",inline:!0,rules:o(T),model:o(n)},{default:u(()=>[l(b,{label:"数据库",prop:"dbName"},{default:u(()=>[l(z,{modelValue:o(n).dbName,"onUpdate:modelValue":e[0]||(e[0]=t=>o(n).dbName=t),clearable:"",placeholder:"请选择",onChange:w},{default:u(()=>[(g(!0),A(H,null,J(o(k),t=>(g(),V(q,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(b,{label:"表名"},{default:u(()=>[l(D,{modelValue:o(n).tableName,"onUpdate:modelValue":e[1]||(e[1]=t=>o(n).tableName=t),clearable:"",placeholder:"输入要查询的表名"},null,8,["modelValue"])]),_:1}),l(b,null,{default:u(()=>[l(f,{type:"primary",icon:"search",onClick:e[2]||(e[2]=t=>w())},{default:u(()=>e[8]||(e[8]=[_("查询")])),_:1})]),_:1})]),_:1},8,["rules","model"]),l(K,null,{default:u(()=>[l(F,{ref:"table",onRowClick:U,data:o(v),"highlight-current-row":"",height:"300px","row-key":h,onSelectionChange:B},{default:u(()=>[l(c,{type:"selection","reserve-selection":!0,width:"55"}),l(c,{prop:"name",label:"表名",sortable:"custom",width:"380"}),l(c,{prop:"description",label:"表描述"})]),_:1},8,["data"]),l(I,{page:o(n).pageNum,"onUpdate:page":e[3]||(e[3]=t=>o(n).pageNum=t),limit:o(n).pageSize,"onUpdate:limit":e[4]||(e[4]=t=>o(n).pageSize=t),total:o(m),"onUpdate:total":e[5]||(e[5]=t=>x(m)?m.value=t:null),onPagination:p},null,8,["page","limit","total"])]),_:1})]),_:1},8,["modelValue"])}}};export{Y as default};
