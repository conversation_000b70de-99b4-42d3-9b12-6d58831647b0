import{r as u,E as x,a5 as Ke,w as Ne,aO as Ue,x as d,N as ee,o as g,c as le,O as f,S as Le,k as o,i as l,l as t,F as Te,K as Re,p as _,D as s,m as E,t as p,A as ze,q as F,j as te,s as De,v as Me,aP as qe,aQ as Be,aR as Ee,aS as Fe,aT as Pe,aU as Ae,aV as Oe}from"./index-CX4J5aM5.js";import{i as Qe}from"./index-CMuNoVkf.js";const Ye={class:"app-container"},He=<PERSON>({name:"commonlang"}),Ze=Object.assign(He,{setup(je){const{proxy:i}=Me(),R=u([]),P=u(!0),A=u(!0),N=u(!1),U=u(!0),r=x({pageNum:1,pageSize:10,sort:void 0,sortType:void 0,langCode:void 0,langKey:void 0,addtime:void 0,showMode:2}),L=u(""),z=u(0),w=u(!1),O=x({form:{},rules:{id:[{required:!0,message:"id不能为空",trigger:"blur",type:"number"}],langKey:[{required:!0,pattern:/^[A-Za-z].+$/,message:"语言key不能为空",trigger:"change"}],langName:[{required:!0,message:"内容不能为空",trigger:"blur"}]},options:{}});var ne=[{dictType:"sys_lang_type"}];i.getDicts(ne).then(e=>{e.data.forEach(n=>{O.options[n.dictType]=n.list})});const{form:y,rules:ae,options:Q}=Ke(O),Y=u(0),D=u([]),oe=u(),de=u();u([]);const T=u([]);Ne(()=>r.showMode,()=>{$()},{immediate:!0});function $(){i.addDateRange(r,i.dateRangeAddtime,"Addtime"),N.value=!0,Ue(r).then(e=>{e.code==200&&(D.value=e.data.result,Y.value=e.data.totalNum,N.value=!1)})}function re(){w.value=!1,S()}function S(){y.value={langKey:void 0,langList:[{langCode:"zh-cn",label:i.$t("common.chinese"),langName:void 0},{langCode:"zh-tw",label:i.$t("common.traditionalChinese"),langName:void 0},{langCode:"en",label:i.$t("common.english"),langName:void 0}]},i.resetForm("formRef")}function k(){r.pageNum=1,$()}function ie(){S(),w.value=!0,L.value=i.$t("btn.add"),z.value=1}function H(e){const n=e.id||R.value;i.$confirm('是否确认删除参数编号为"'+n+'"的数据项？').then(function(){return qe(n)}).then(()=>{k(),i.$modal.msgSuccess("删除成功")}).catch(()=>{})}function se(e){i.$confirm('是否确认删除key为"'+e.langKey+'"的数据项？').then(function(){return Be(e.langKey)}).then(()=>{k(),i.$modal.msgSuccess("删除成功")})}function j(e){S();const n=e.id||R.value;Ee(n).then(v=>{const{code:C,data:b}=v;C==200&&(w.value=!0,L.value="修改数据",z.value=2,y.value={...b})})}function ue(e){S(),Fe(e.langKey).then(n=>{const{code:v,data:C}=n;v==200&&(w.value=!0,L.value=i.$t("btn.edit"),z.value=2,y.value={...C})})}function me(){const e=u(!0);if(y.value.langList.forEach(n=>{Pe(n.langName)&&(e.value=!1)}),!e.value){i.$modal.msgError("请完成表格内容填写");return}i.$refs.formRef.validate(n=>{n&&Ae(y.value).then(v=>{i.$modal.msgSuccess("操作成功"),w.value=!1,$()}).catch(()=>{})})}function pe(){T.value=[],i.resetForm("queryRef"),k()}function ce(){i.$confirm("是否确认导出所有多语言配置数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Oe(r)}).then(e=>{i.download(e.data.path)})}function I(e){R.value=e.map(n=>n.id),P.value=e.length!=1,A.value=!e.length}function W(e){e.prop==null||e.order==null?(r.sort=void 0,r.sortType=void 0):(r.sort=e.prop,r.sortType=e.order),k()}const ge=e=>{const{item1:n,item2:v}=e.data;var C="";v.forEach(b=>{C+=b.storageMessage+","}),i.$alert(n+"<p>"+C+"</p>","导入结果",{dangerouslyUseHTMLString:!0}),$()};return k(),S(),(e,n)=>{const v=d("el-option"),C=d("el-select"),b=d("el-form-item"),M=d("el-input"),Z=d("el-radio-button"),fe=d("el-radio-group"),_e=d("el-date-picker"),c=d("el-button"),G=d("el-form"),V=d("el-col"),be=d("arrow-down"),J=d("el-icon"),ye=d("el-dropdown-item"),he=d("el-dropdown-menu"),we=d("el-dropdown"),ve=d("right-toolbar"),q=d("el-row"),m=d("el-table-column"),Ce=d("dict-tag"),B=d("el-table"),$e=d("pagination"),ke=d("questionFilled"),Ve=d("el-tooltip"),Se=d("el-dialog"),h=ee("hasPermi"),X=ee("loading");return g(),le("div",Ye,[f(l(G,{model:o(r),"label-position":"right",inline:"",ref_key:"queryRef",ref:oe,onSubmit:n[4]||(n[4]=ze(()=>{},["prevent"]))},{default:t(()=>[l(b,{label:e.$t("language"),prop:"langCode"},{default:t(()=>[l(C,{modelValue:o(r).langCode,"onUpdate:modelValue":n[0]||(n[0]=a=>o(r).langCode=a),placeholder:"请选择语言code"},{default:t(()=>[(g(!0),le(Te,null,Re(o(Q).sys_lang_type,a=>(g(),_(v,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),l(b,{label:e.$t("languageKey"),prop:"langKey"},{default:t(()=>[l(M,{modelValue:o(r).langKey,"onUpdate:modelValue":n[1]||(n[1]=a=>o(r).langKey=a),placeholder:"请输入语言key"},null,8,["modelValue"])]),_:1},8,["label"]),l(b,{label:e.$t("showWay")},{default:t(()=>[l(fe,{modelValue:o(r).showMode,"onUpdate:modelValue":n[2]||(n[2]=a=>o(r).showMode=a)},{default:t(()=>[l(Z,{value:1},{default:t(()=>n[10]||(n[10]=[s("表格")])),_:1}),l(Z,{value:2},{default:t(()=>n[11]||(n[11]=[s("行列")])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),l(b,{label:e.$t("common.addTime")},{default:t(()=>[l(_e,{modelValue:o(T),"onUpdate:modelValue":n[3]||(n[3]=a=>E(T)?T.value=a:null),style:{width:"240px"},type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",placeholder:"请选择添加时间","value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:e.dateOptions},null,8,["modelValue","shortcuts"])]),_:1},8,["label"]),l(b,null,{default:t(()=>[l(c,{icon:"search",type:"primary",onClick:k},{default:t(()=>[s(p(e.$t("btn.search")),1)]),_:1}),l(c,{icon:"refresh",onClick:pe},{default:t(()=>[s(p(e.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[Le,o(U)]]),l(q,{gutter:10,class:"mb8"},{default:t(()=>[l(V,{span:1.5},{default:t(()=>[f((g(),_(c,{type:"primary",plain:"",icon:"plus",onClick:ie},{default:t(()=>[s(p(e.$t("btn.add")),1)]),_:1})),[[h,["system:lang:add"]]])]),_:1}),l(V,{span:1.5},{default:t(()=>[f((g(),_(c,{type:"success",disabled:o(P),plain:"",icon:"edit",onClick:j},{default:t(()=>[s(p(e.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[h,["system:lang:edit"]]])]),_:1}),l(V,{span:1.5},{default:t(()=>[f((g(),_(c,{type:"danger",disabled:o(A),plain:"",icon:"delete",onClick:H},{default:t(()=>[s(p(e.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[h,["system:lang:delete"]]])]),_:1}),l(V,{span:1.5},{default:t(()=>[f((g(),_(we,{trigger:"click"},{dropdown:t(()=>[l(he,null,{default:t(()=>[l(ye,{command:"upload"},{default:t(()=>[l(o(Qe),{templateUrl:"system/CommonLang/importTemplate",importUrl:"/system/CommonLang/importData",onSuccess:ge})]),_:1})]),_:1})]),default:t(()=>[l(c,{type:"primary",plain:"",icon:"Upload"},{default:t(()=>[s(p(e.$t("btn.import")),1),l(J,{class:"el-icon--right"},{default:t(()=>[l(be)]),_:1})]),_:1})]),_:1})),[[h,["business:commonlang:import"]]])]),_:1}),l(V,{span:1.5},{default:t(()=>[f((g(),_(c,{type:"warning",plain:"",icon:"download",onClick:ce},{default:t(()=>[s(p(e.$t("btn.export")),1)]),_:1})),[[h,["system:lang:export"]]])]),_:1}),l(ve,{showSearch:o(U),"onUpdate:showSearch":n[5]||(n[5]=a=>E(U)?U.value=a:null),onQueryTable:$},null,8,["showSearch"])]),_:1}),o(r).showMode==1?f((g(),_(B,{key:0,data:o(D),ref:"table",border:"","highlight-current-row":"",onSortChange:W,onSelectionChange:I},{default:t(()=>[l(m,{type:"selection",width:"50",align:"center"}),l(m,{prop:"id",label:"id",align:"center"}),l(m,{prop:"langCode",label:e.$t("language"),align:"center"},{default:t(a=>[l(Ce,{options:o(Q).sys_lang_type,value:a.row.langCode},null,8,["options","value"])]),_:1},8,["label"]),l(m,{prop:"langKey",label:e.$t("languageKey"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{prop:"langName",label:e.$t("common.content"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{prop:"addtime",label:e.$t("common.addTime"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{label:e.$t("btn.operate"),align:"center",width:"140"},{default:t(a=>[f(l(c,{text:"",size:"small",icon:"edit",title:"编辑",onClick:K=>j(a.row)},null,8,["onClick"]),[[h,["system:lang:edit"]]]),f(l(c,{text:"",size:"small",icon:"delete",title:"删除",onClick:K=>H(a.row)},null,8,["onClick"]),[[h,["system:lang:delete"]]])]),_:1},8,["label"])]),_:1},8,["data"])),[[X,o(N)]]):F("",!0),o(r).showMode==2?f((g(),_(B,{key:1,data:o(D),ref:"table",border:"","highlight-current-row":"",onSortChange:W,onSelectionChange:I},{default:t(()=>[l(m,{prop:"langKey",label:e.$t("languageKey"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{prop:"zh-cn",label:e.$t("common.chinese"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{prop:"en",label:e.$t("common.english"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{prop:"zh-tw",label:e.$t("common.traditionalChinese"),align:"center","show-overflow-tooltip":!0},null,8,["label"]),l(m,{label:e.$t("btn.operate"),align:"center",width:"140"},{default:t(a=>[f((g(),_(c,{text:"",size:"small",icon:"edit",title:"编辑",onClick:K=>ue(a.row)},{default:t(()=>[s(p(e.$t("btn.edit")),1)]),_:2},1032,["onClick"])),[[h,["system:lang:edit"]]]),f((g(),_(c,{text:"",type:"danger",icon:"delete",title:"删除",onClick:K=>se(a.row)},{default:t(()=>[s(p(e.$t("btn.delete")),1)]),_:2},1032,["onClick"])),[[h,["system:lang:delete"]]])]),_:1},8,["label"])]),_:1},8,["data"])),[[X,o(N)]]):F("",!0),o(r).showMode==1?(g(),_($e,{key:2,total:o(Y),page:o(r).pageNum,"onUpdate:page":n[6]||(n[6]=a=>o(r).pageNum=a),limit:o(r).pageSize,"onUpdate:limit":n[7]||(n[7]=a=>o(r).pageSize=a),onPagination:$},null,8,["total","page","limit"])):F("",!0),l(Se,{title:o(L),"lock-scroll":!1,modelValue:o(w),"onUpdate:modelValue":n[9]||(n[9]=a=>E(w)?w.value=a:null),width:"550px"},{footer:t(()=>[l(c,{text:"",onClick:re},{default:t(()=>[s(p(e.$t("btn.cancel")),1)]),_:1}),l(c,{type:"primary",onClick:me},{default:t(()=>[s(p(e.$t("btn.submit")),1)]),_:1})]),default:t(()=>[l(G,{ref_key:"formRef",ref:de,model:o(y),rules:o(ae),"label-width":"140px"},{default:t(()=>[l(q,{gutter:20},{default:t(()=>[l(V,{lg:24},{default:t(()=>[l(b,{prop:"langKey"},{label:t(()=>[te("span",null,[l(Ve,{content:"翻译key，eg：message.title",placement:"top"},{default:t(()=>[l(J,{size:15},{default:t(()=>[l(ke)]),_:1})]),_:1})]),s(" "+p(e.$t("languageKey")),1)]),default:t(()=>[l(M,{modelValue:o(y).langKey,"onUpdate:modelValue":n[8]||(n[8]=a=>o(y).langKey=a),placeholder:"请输入语言key"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(q,null,{default:t(()=>[l(B,{data:o(y).langList},{default:t(()=>[l(m,{label:e.$t("language"),align:"center",prop:"langCode",width:"100"},{default:t(a=>[s(p(a.row.label)+" ",1),n[12]||(n[12]=te("br",null,null,-1)),s(" "+p(a.row.langCode),1)]),_:1},8,["label"]),l(m,{label:e.$t("common.content"),align:"center"},{default:t(a=>[l(M,{type:"textarea",rows:"2",prop:"langName",modelValue:a.row.langName,"onUpdate:modelValue":K=>a.row.langName=K},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"])]),_:1},8,["data"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ze as default};
