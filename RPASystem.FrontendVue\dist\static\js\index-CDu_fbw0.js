import{ai as S,aq as ge,r as m,E,a5 as be,x as s,N as O,o as i,c as F,O as v,S as Q,k as o,i as e,l,z as j,F as A,K as G,p as y,D as c,t as p,m as H,j as ve,s as ye,v as Ve}from"./index-CX4J5aM5.js";function he(d){return S({url:"/system/post/list",method:"get",params:d})}function Ce(d){return S({url:"/system/post/"+d,method:"get"})}function we(d){return S({url:"/system/post",method:"post",data:d})}function ke(d){return S({url:"/system/post",method:"put",data:d})}function Se(d){return S({url:"/system/post/"+d,method:"delete"})}async function Ne(d){await ge("/system/post/export",d)}const $e={class:"app-container"},Ue=ye({name:"post"}),Pe=Object.assign(Ue,{setup(d){const{proxy:_}=Ve(),x=m(!0),P=m([]),q=m(!0),z=m(!0),N=m(!0),I=m(0),D=m([]),T=m(""),V=m(!1),$=m([]);let r=E({pageNum:1,pageSize:10,postCode:void 0,postName:void 0,status:void 0});const J=E({form:{},rules:{postName:[{required:!0,message:"岗位名称不能为空",trigger:"blur"}],postCode:[{required:!0,message:"岗位编码不能为空",trigger:"blur"}],postSort:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}]}}),M=m(null),{form:u,rules:W}=be(J);function C(){x.value=!0,he(r).then(n=>{D.value=n.data.result,I.value=n.data.totalNum,x.value=!1})}function X(){V.value=!1,B()}function B(){u.value={postId:void 0,postCode:void 0,postName:void 0,postSort:0,status:"0",remark:void 0},_.resetForm("formRef")}_.getDicts("sys_normal_disable").then(n=>{$.value=n.data});function k(){r.pageNum=1,C()}function Y(){_.resetForm("queryForm"),k()}function Z(n){P.value=n.map(a=>a.postId),q.value=n.length!=1,z.value=!n.length}function ee(){B(),V.value=!0,T.value="添加岗位"}function R(n){B();const a=n.postId||P.value;Ce(a).then(h=>{u.value=h.data,V.value=!0,T.value="修改岗位"})}function te(){_.$refs.formRef.validate(n=>{n&&(u.value.postId!=null?ke(u.value).then(a=>{_.$modal.msgSuccess("修改成功"),V.value=!1,C()}):we(u.value).then(a=>{_.$modal.msgSuccess("新增成功"),V.value=!1,C()}))})}function L(n){const a=n.postId||P.value;_.$confirm('是否确认删除岗位编号为"'+a+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Se(a)}).then(()=>{C(),_.$modal.msgSuccess("删除成功")})}function oe(){_.$confirm("是否确认导出所有岗位数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await Ne(r)})}return k(),(n,a)=>{const h=s("el-input"),g=s("el-form-item"),le=s("el-option"),ae=s("el-select"),f=s("el-button"),K=s("el-form"),U=s("el-col"),ne=s("right-toolbar"),se=s("el-row"),b=s("el-table-column"),ue=s("dict-tag"),re=s("el-table"),de=s("pagination"),ie=s("el-input-number"),pe=s("el-radio"),me=s("el-radio-group"),ce=s("el-dialog"),w=O("hasPermi"),fe=O("loading");return i(),F("div",$e,[v(e(K,{model:o(r),ref:"queryForm",inline:!0},{default:l(()=>[e(g,{label:"岗位编码",prop:"postCode"},{default:l(()=>[e(h,{modelValue:o(r).postCode,"onUpdate:modelValue":a[0]||(a[0]=t=>o(r).postCode=t),placeholder:"请输入岗位编码",onKeyup:j(k,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"岗位名称",prop:"postName"},{default:l(()=>[e(h,{modelValue:o(r).postName,"onUpdate:modelValue":a[1]||(a[1]=t=>o(r).postName=t),placeholder:"请输入岗位名称",onKeyup:j(k,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"状态",prop:"status"},{default:l(()=>[e(ae,{modelValue:o(r).status,"onUpdate:modelValue":a[2]||(a[2]=t=>o(r).status=t),placeholder:"岗位状态"},{default:l(()=>[(i(!0),F(A,null,G(o($),t=>(i(),y(le,{key:t.dictValue,label:t.dictLabel,value:t.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:l(()=>[e(f,{type:"primary",icon:"search",onClick:k},{default:l(()=>[c(p(n.$t("btn.search")),1)]),_:1}),e(f,{icon:"refresh",onClick:Y},{default:l(()=>[c(p(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[Q,o(N)]]),e(se,{gutter:10,class:"mb8"},{default:l(()=>[e(U,{span:1.5},{default:l(()=>[v((i(),y(f,{type:"primary",plain:"",icon:"plus",onClick:ee},{default:l(()=>[c(p(n.$t("btn.add")),1)]),_:1})),[[w,["system:post:add"]]])]),_:1}),e(U,{span:1.5},{default:l(()=>[v((i(),y(f,{type:"success",plain:"",icon:"edit",disabled:o(q),onClick:R},{default:l(()=>[c(p(n.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[w,["system:post:edit"]]])]),_:1}),e(U,{span:1.5},{default:l(()=>[v((i(),y(f,{type:"danger",plain:"",icon:"delete",disabled:o(z),onClick:L},{default:l(()=>[c(p(n.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[w,["system:post:remove"]]])]),_:1}),e(U,{span:1.5},{default:l(()=>[v((i(),y(f,{type:"warning",plain:"",icon:"download",onClick:oe},{default:l(()=>[c(p(n.$t("btn.export")),1)]),_:1})),[[w,["system:post:export"]]])]),_:1}),e(ne,{showSearch:o(N),"onUpdate:showSearch":a[3]||(a[3]=t=>H(N)?N.value=t:null),onQueryTable:C},null,8,["showSearch"])]),_:1}),v((i(),y(re,{data:o(D),onSelectionChange:Z},{default:l(()=>[e(b,{type:"selection",width:"55",align:"center"}),e(b,{label:"岗位编号",align:"center",prop:"postId",sortable:""}),e(b,{label:"岗位编码",align:"center",prop:"postCode"}),e(b,{label:"岗位名称",align:"center",prop:"postName"}),e(b,{label:"用户数",align:"center",prop:"userNum",sortable:""}),e(b,{label:"岗位排序",align:"center",prop:"postSort",sortable:""}),e(b,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(ue,{options:o($),value:t.row.status},null,8,["options","value"])]),_:1}),e(b,{label:"创建时间",align:"center",prop:"createTime",width:"180",sortable:""},{default:l(t=>[ve("span",null,p(n.parseTime(t.row.createTime)),1)]),_:1}),e(b,{label:"操作",align:"center",width:"150"},{default:l(t=>[v((i(),y(f,{text:"",size:"small",icon:"edit",onClick:_e=>R(t.row)},{default:l(()=>[c(p(n.$t("btn.edit")),1)]),_:2},1032,["onClick"])),[[w,["system:post:edit"]]]),v((i(),y(f,{text:"",size:"small",icon:"delete",onClick:_e=>L(t.row)},{default:l(()=>[c(p(n.$t("btn.delete")),1)]),_:2},1032,["onClick"])),[[w,["system:post:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,o(x)]]),v(e(de,{total:o(I),page:o(r).pageNum,"onUpdate:page":a[4]||(a[4]=t=>o(r).pageNum=t),limit:o(r).pageSize,"onUpdate:limit":a[5]||(a[5]=t=>o(r).pageSize=t),onPagination:C},null,8,["total","page","limit"]),[[Q,o(I)>0]]),e(ce,{title:o(T),modelValue:o(V),"onUpdate:modelValue":a[11]||(a[11]=t=>H(V)?V.value=t:null),width:"500px"},{footer:l(()=>[e(f,{text:"",onClick:X},{default:l(()=>[c(p(n.$t("btn.cancel")),1)]),_:1}),e(f,{type:"primary",onClick:te},{default:l(()=>[c(p(n.$t("btn.submit")),1)]),_:1})]),default:l(()=>[e(K,{ref_key:"formRef",ref:M,model:o(u),rules:o(W),"label-width":"80px"},{default:l(()=>[e(g,{label:"岗位名称",prop:"postName"},{default:l(()=>[e(h,{modelValue:o(u).postName,"onUpdate:modelValue":a[6]||(a[6]=t=>o(u).postName=t),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),e(g,{label:"岗位编码",prop:"postCode"},{default:l(()=>[e(h,{modelValue:o(u).postCode,"onUpdate:modelValue":a[7]||(a[7]=t=>o(u).postCode=t),placeholder:"请输入编码名称"},null,8,["modelValue"])]),_:1}),e(g,{label:"岗位顺序",prop:"postSort"},{default:l(()=>[e(ie,{modelValue:o(u).postSort,"onUpdate:modelValue":a[8]||(a[8]=t=>o(u).postSort=t),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),e(g,{label:"岗位状态",prop:"status"},{default:l(()=>[e(me,{modelValue:o(u).status,"onUpdate:modelValue":a[9]||(a[9]=t=>o(u).status=t)},{default:l(()=>[(i(!0),F(A,null,G(o($),t=>(i(),y(pe,{key:t.dictValue,value:t.dictValue},{default:l(()=>[c(p(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"备注",prop:"remark"},{default:l(()=>[e(h,{modelValue:o(u).remark,"onUpdate:modelValue":a[10]||(a[10]=t=>o(u).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Pe as default};
