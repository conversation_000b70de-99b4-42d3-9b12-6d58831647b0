import{_ as Q,r as p,e as B,M as J,x as i,o as g,c as S,j as w,i as t,l as a,D as u,z as W,F as U,K as T,p as x,t as X,q as Y,s as Z,P as d,H as D}from"./index-CX4J5aM5.js";import{l as ee,d as le,i as te,u as oe,c as ae}from"./SystemConfigManager-BBI_j5TY.js";const ne={class:"system-config-manager"},se={class:"operation-bar"},re={class:"left-section"},ie={class:"right-section"},ue={class:"dialog-footer"},de=Z({name:"systemconfigmanager"}),ce=Object.assign(de,{setup(fe){const V=p([]),m=p(!1),r=p(!1),C=p(null),y=p(""),_=p(""),n=p({configKey:"",configValue:"",description:"",configGroup:"",isSystem:!1}),h=B(()=>{const o=new Set;return V.value.forEach(e=>{e.configGroup&&o.add(e.configGroup)}),Array.from(o)}),E=B(()=>{let o=V.value;if(_.value&&(o=o.filter(e=>e.configGroup===_.value)),y.value){const e=y.value.toLowerCase();o=o.filter(s=>{var c;return s.configKey.toLowerCase().includes(e)||((c=s.description)==null?void 0:c.toLowerCase().includes(e))||s.configValue.toLowerCase().includes(e)})}return o}),L={configKey:[{required:!0,message:"请输入配置键名",trigger:"blur"}],configValue:[{required:!0,message:"请输入配置值",trigger:"blur"}],configGroup:[{required:!0,message:"请选择或输入分组",trigger:"change"}]},b=async()=>{try{const o=await ee();V.value=o.data}catch(o){d.error("获取配置列表失败"),console.error("获取配置列表失败:",o)}},k=()=>{},q=()=>{},z=()=>{r.value=!1,n.value={configKey:"",configValue:"",description:"",configGroup:"",isSystem:!1},m.value=!0},M=o=>{r.value=!0,n.value={...o},m.value=!0},F=async o=>{if(o.isSystem){d.warning("系统内置配置不能删除");return}try{await D.confirm("确定要删除该配置吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),await le(o.id),d.success("删除成功"),await b()}catch(e){e!=="cancel"&&(d.error("删除失败"),console.error("删除失败:",e))}},N=async()=>{try{await D.confirm("确定要初始化默认配置吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});const o=await te();d.success(`初始化成功，添加了 ${o.data} 条默认配置`),await b()}catch(o){o!=="cancel"&&(d.error("初始化失败"),console.error("初始化失败:",o))}},$=async()=>{if(C.value)try{await C.value.validate(),r.value?await oe(n.value):await ae(n.value),d.success(r.value?"更新成功":"创建成功"),m.value=!1,await b()}catch(o){o!=="cancel"&&(d.error(r.value?"更新失败":"创建失败"),console.error(r.value?"更新失败:":"创建失败:",o))}},A=(o,e,s)=>s?new Date(s).toLocaleString():"";return J(()=>{b()}),(o,e)=>{const s=i("el-button"),c=i("el-input"),G=i("el-option"),K=i("el-select"),f=i("el-table-column"),j=i("el-tag"),I=i("el-table"),v=i("el-form-item"),R=i("el-switch"),H=i("el-form"),O=i("el-dialog");return g(),S("div",ne,[w("div",se,[w("div",re,[t(c,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=l=>y.value=l),placeholder:"请输入配置键名或描述搜索",style:{width:"300px"},clearable:"",onKeyup:W(k,["enter"])},{append:a(()=>[t(s,{onClick:k},{default:a(()=>e[9]||(e[9]=[u("搜索")])),_:1})]),_:1},8,["modelValue"]),t(K,{modelValue:_.value,"onUpdate:modelValue":e[1]||(e[1]=l=>_.value=l),placeholder:"按分组筛选",style:{width:"150px","margin-left":"10px"},clearable:"",onChange:q},{default:a(()=>[(g(!0),S(U,null,T(h.value,l=>(g(),x(G,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),w("div",ie,[t(s,{type:"primary",onClick:z},{default:a(()=>e[10]||(e[10]=[u("新增配置")])),_:1}),t(s,{type:"success",onClick:N},{default:a(()=>e[11]||(e[11]=[u("初始化默认配置")])),_:1})])]),t(I,{data:E.value,style:{width:"100%"}},{default:a(()=>[t(f,{prop:"configKey",label:"配置键名",width:"220"}),t(f,{prop:"configValue",label:"配置值",width:"220"}),t(f,{prop:"description",label:"描述"}),t(f,{prop:"configGroup",label:"分组",width:"120"}),t(f,{prop:"isSystem",label:"系统内置",width:"100"},{default:a(l=>[t(j,{type:l.row.isSystem?"success":"info"},{default:a(()=>[u(X(l.row.isSystem?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"createdTime",label:"创建时间",width:"180",formatter:A}),t(f,{label:"操作",width:"180",fixed:"right"},{default:a(l=>[t(s,{size:"small",onClick:P=>M(l.row)},{default:a(()=>e[12]||(e[12]=[u("编辑")])),_:2},1032,["onClick"]),t(s,{size:"small",type:"danger",onClick:P=>F(l.row),disabled:l.row.isSystem},{default:a(()=>e[13]||(e[13]=[u("删除")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"]),t(O,{modelValue:m.value,"onUpdate:modelValue":e[8]||(e[8]=l=>m.value=l),title:r.value?"编辑配置":"新增配置",width:"500px","destroy-on-close":""},{footer:a(()=>[w("span",ue,[t(s,{onClick:e[7]||(e[7]=l=>m.value=!1)},{default:a(()=>e[14]||(e[14]=[u("取消")])),_:1}),t(s,{type:"primary",onClick:$},{default:a(()=>e[15]||(e[15]=[u("确定")])),_:1})])]),default:a(()=>[t(H,{model:n.value,"label-width":"100px",rules:L,ref_key:"formRef",ref:C},{default:a(()=>[t(v,{label:"配置键名",prop:"configKey",disabled:r.value&&n.value.isSystem},{default:a(()=>[t(c,{modelValue:n.value.configKey,"onUpdate:modelValue":e[2]||(e[2]=l=>n.value.configKey=l),placeholder:"请输入配置键名",disabled:r.value&&n.value.isSystem},null,8,["modelValue","disabled"])]),_:1},8,["disabled"]),t(v,{label:"配置值",prop:"configValue"},{default:a(()=>[t(c,{modelValue:n.value.configValue,"onUpdate:modelValue":e[3]||(e[3]=l=>n.value.configValue=l),placeholder:"请输入配置值"},null,8,["modelValue"])]),_:1}),t(v,{label:"描述",prop:"description"},{default:a(()=>[t(c,{modelValue:n.value.description,"onUpdate:modelValue":e[4]||(e[4]=l=>n.value.description=l),type:"textarea",rows:2,placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1}),t(v,{label:"分组",prop:"configGroup",disabled:r.value&&n.value.isSystem},{default:a(()=>[t(K,{modelValue:n.value.configGroup,"onUpdate:modelValue":e[5]||(e[5]=l=>n.value.configGroup=l),placeholder:"请选择分组",style:{width:"100%"},disabled:r.value&&n.value.isSystem,filterable:"","allow-create":""},{default:a(()=>[(g(!0),S(U,null,T(h.value,l=>(g(),x(G,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1},8,["disabled"]),r.value?(g(),x(v,{key:0,label:"系统内置"},{default:a(()=>[t(R,{modelValue:n.value.isSystem,"onUpdate:modelValue":e[6]||(e[6]=l=>n.value.isSystem=l),disabled:""},null,8,["modelValue"])]),_:1})):Y("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),ge=Q(ce,[["__scopeId","data-v-7ceb1b2b"]]);export{ge as default};
