import{r,E as O,a5 as fe,x as i,N as Q,o as s,c as R,O as h,S as me,k as o,i as e,l as t,z as Y,F as j,K as E,p as v,m as D,D as f,t as c,j as ge,s as _e,v as be,aW as ye,aX as ve,aY as Ve,aZ as he,a_ as ke}from"./index-CX4J5aM5.js";const Ce={class:"app-container"},we=_e({name:"config"}),Ke=Object.assign(we,{setup(Ne){const K=r(!0),S=r([]),F=r(!0),q=r(!0),z=r(!0),w=r(0),L=r([]),T=r(""),b=r(!1),k=r([]),N=r([]),d=O({pageNum:1,pageSize:10,configName:void 0,configKey:void 0,configType:void 0}),A=O({form:{},rules:{configName:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],configKey:[{required:!0,message:"参数键名不能为空",trigger:"blur"}],configValue:[{required:!0,message:"参数键值不能为空",trigger:"blur"}]}}),W=r(),{form:u,rules:X}=fe(A),{proxy:m}=be();function V(){K.value=!0,ye(m.addDateRange(d,k.value)).then(a=>{L.value=a.data.result,w.value=a.data.totalNum,K.value=!1})}function Z(){b.value=!1,U()}function U(){u.value={configId:void 0,configName:void 0,configKey:void 0,configValue:void 0,configType:"Y",remark:void 0},m.resetForm("formRef")}function $(){d.pageNum=1,V()}function G(){k.value=[],m.resetForm("queryForm"),$()}function H(){U(),b.value=!0,T.value="添加参数"}function J(a){S.value=a.map(n=>n.configId),F.value=a.length!=1,q.value=!a.length}function x(a){U();const n=a.configId||S.value;ve(n).then(y=>{u.value=y.data,b.value=!0,T.value="修改参数"})}function M(){m.$refs.formRef.validate(a=>{a&&(u.value.configId!=null?Ve(u.value).then(n=>{m.$modal.msgSuccess("修改成功"),b.value=!1,V()}):he(u.value).then(n=>{m.$modal.msgSuccess("新增成功"),b.value=!1,V()}))})}function B(a){const n=a.configId||S.value;m.$confirm('是否确认删除参数编号为"'+n+'"的数据项？').then(function(){return ke(n)}).then(()=>{V(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}return m.getDicts("sys_yes_no").then(a=>{N.value=a.data}),V(),(a,n)=>{const y=i("el-input"),p=i("el-form-item"),ee=i("el-option"),le=i("el-select"),oe=i("el-date-picker"),g=i("el-button"),P=i("el-form"),I=i("el-col"),ne=i("right-toolbar"),te=i("el-row"),_=i("el-table-column"),ae=i("dict-tag"),ie=i("el-table"),ue=i("pagination"),de=i("el-radio"),re=i("el-radio-group"),se=i("el-dialog"),C=Q("hasPermi"),ce=Q("loading");return s(),R("div",Ce,[h(e(P,{model:o(d),ref:"queryForm",inline:!0,"label-width":"68px"},{default:t(()=>[e(p,{label:"参数名称",prop:"configName"},{default:t(()=>[e(y,{modelValue:o(d).configName,"onUpdate:modelValue":n[0]||(n[0]=l=>o(d).configName=l),placeholder:"请输入参数名称",clearable:"",onKeyup:Y($,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"参数键名",prop:"configKey"},{default:t(()=>[e(y,{modelValue:o(d).configKey,"onUpdate:modelValue":n[1]||(n[1]=l=>o(d).configKey=l),placeholder:"请输入参数键名",clearable:"",onKeyup:Y($,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"系统内置",prop:"configType"},{default:t(()=>[e(le,{modelValue:o(d).configType,"onUpdate:modelValue":n[2]||(n[2]=l=>o(d).configType=l),placeholder:"系统内置",clearable:""},{default:t(()=>[(s(!0),R(j,null,E(o(N),l=>(s(),v(ee,{key:l.dictValue,label:l.dictLabel,value:l.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"创建时间"},{default:t(()=>[e(oe,{modelValue:o(k),"onUpdate:modelValue":n[3]||(n[3]=l=>D(k)?k.value=l:null),type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(p,null,{default:t(()=>[e(g,{type:"primary",icon:"search",onClick:$},{default:t(()=>[f(c(a.$t("btn.search")),1)]),_:1}),e(g,{icon:"refresh",onClick:G},{default:t(()=>[f(c(a.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[me,o(z)]]),e(te,{gutter:10,class:"mb8"},{default:t(()=>[e(I,{span:1.5},{default:t(()=>[h((s(),v(g,{type:"primary",plain:"",icon:"plus",onClick:H},{default:t(()=>[f(c(a.$t("btn.add")),1)]),_:1})),[[C,["system:config:add"]]])]),_:1}),e(I,{span:1.5},{default:t(()=>[h((s(),v(g,{type:"success",plain:"",icon:"edit",disabled:o(F),onClick:x},{default:t(()=>[f(c(a.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[C,["system:config:edit"]]])]),_:1}),e(I,{span:1.5},{default:t(()=>[h((s(),v(g,{type:"danger",plain:"",icon:"delete",disabled:o(q),onClick:B},{default:t(()=>[f(c(a.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[C,["system:config:remove"]]])]),_:1}),e(ne,{showSearch:o(z),onQueryTable:V},null,8,["showSearch"])]),_:1}),h((s(),v(ie,{data:o(L),onSelectionChange:J},{default:t(()=>[e(_,{type:"selection",width:"55",align:"center"}),e(_,{label:"参数主键",align:"center",prop:"configId"}),e(_,{label:"参数名称",align:"center",prop:"configName"}),e(_,{label:"参数键名",align:"center",prop:"configKey"}),e(_,{label:"参数键值",align:"center",prop:"configValue"}),e(_,{label:"系统内置",align:"center",prop:"configType"},{default:t(l=>[e(ae,{options:o(N),value:l.row.configType},null,8,["options","value"])]),_:1}),e(_,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),e(_,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(l=>[ge("span",null,c(l.row.createTime),1)]),_:1}),e(_,{label:"操作",align:"center",width:"160"},{default:t(l=>[h((s(),v(g,{size:"small",text:"",icon:"edit",onClick:pe=>x(l.row)},{default:t(()=>[f(c(a.$t("btn.edit")),1)]),_:2},1032,["onClick"])),[[C,["system:config:edit"]]]),h((s(),v(g,{size:"small",text:"",icon:"delete",onClick:pe=>B(l.row)},{default:t(()=>[f(c(a.$t("btn.delete")),1)]),_:2},1032,["onClick"])),[[C,["system:config:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,o(K)]]),e(ue,{total:o(w),"onUpdate:total":n[4]||(n[4]=l=>D(w)?w.value=l:null),page:o(d).pageNum,"onUpdate:page":n[5]||(n[5]=l=>o(d).pageNum=l),limit:o(d).pageSize,"onUpdate:limit":n[6]||(n[6]=l=>o(d).pageSize=l),onPagination:V},null,8,["total","page","limit"]),e(se,{title:o(T),modelValue:o(b),"onUpdate:modelValue":n[12]||(n[12]=l=>D(b)?b.value=l:null),width:"500px","append-to-body":""},{footer:t(()=>[e(g,{text:"",onClick:Z},{default:t(()=>[f(c(a.$t("btn.cancel")),1)]),_:1}),e(g,{type:"primary",onClick:M},{default:t(()=>[f(c(a.$t("btn.submit")),1)]),_:1})]),default:t(()=>[e(P,{ref_key:"formRef",ref:W,model:o(u),rules:o(X),"label-width":"80px"},{default:t(()=>[e(p,{label:"参数名称",prop:"configName"},{default:t(()=>[e(y,{modelValue:o(u).configName,"onUpdate:modelValue":n[7]||(n[7]=l=>o(u).configName=l),placeholder:"请输入参数名称"},null,8,["modelValue"])]),_:1}),e(p,{label:"参数键名",prop:"configKey"},{default:t(()=>[e(y,{modelValue:o(u).configKey,"onUpdate:modelValue":n[8]||(n[8]=l=>o(u).configKey=l),placeholder:"请输入参数键名"},null,8,["modelValue"])]),_:1}),e(p,{label:"参数键值",prop:"configValue"},{default:t(()=>[e(y,{modelValue:o(u).configValue,"onUpdate:modelValue":n[9]||(n[9]=l=>o(u).configValue=l),placeholder:"请输入参数键值"},null,8,["modelValue"])]),_:1}),e(p,{label:"系统内置",prop:"configType"},{default:t(()=>[e(re,{modelValue:o(u).configType,"onUpdate:modelValue":n[10]||(n[10]=l=>o(u).configType=l)},{default:t(()=>[(s(!0),R(j,null,E(o(N),l=>(s(),v(de,{key:l.dictValue,value:l.dictValue},{default:t(()=>[f(c(l.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"备注",prop:"remark"},{default:t(()=>[e(y,{modelValue:o(u).remark,"onUpdate:modelValue":n[11]||(n[11]=l=>o(u).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ke as default};
