import{r as _,E as ie,a5 as se,x as i,N as z,o as c,c as x,O as f,S as ue,k as l,i as n,l as o,z as re,F as M,K as ce,p as g,D as d,t as s,m as q,j as y,aG as de,q as pe,s as me,v as _e}from"./index-CX4J5aM5.js";import{_ as fe,l as ge,d as ye,s as be}from"./publishNoticeForm-DBvEbbGA.js";import"./index-Av_SNAwg.js";const ve={class:"app-container"},he={style:{"text-align":"center","margin-top":"0"},class:"mb10"},we=["innerHTML"],Te={class:"n_right"},ke={class:"n_right"},Ce=me({name:"notice"}),De=Object.assign(Ce,{setup($e){const{proxy:p}=_e(),B=_([]),C=_(!0),k=_(!0),$=_([]),E=_(!0),P=_(!0),R=_(0),U=ie({form:{},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},options:{sys_notice_type:[],sys_notice_status:[],popupStatus:[{dictLabel:"是",dictValue:"1"}]}});p.getDicts(["sys_notice_type","sys_notice_status"]).then(a=>{a.data.forEach(t=>{U.options[t.dictType]=t.list})});const{queryParams:u,options:w}=se(U);function b(){C.value=!0,ge(u.value).then(a=>{B.value=a.data.result,R.value=a.data.totalNum,C.value=!1})}function S(){u.value.pageNum=1,b()}function F(){p.resetForm("queryRef"),S()}function H(a){$.value=a.map(t=>t.noticeId),E.value=a.length!=1,P.value=!a.length}function j(){p.$refs.publishRef.handleAdd()}function K(a){p.$refs.publishRef.handleUpdate(a)}function Y(a){const t=a.noticeId||$.value;p.$modal.confirm('是否确认删除公告编号为"'+t+'"的数据项？').then(function(){return ye(t)}).then(()=>{b(),p.$modal.msgSuccess("删除成功")})}function O(a){const t=a.noticeId||$.value;be(t).then(I=>{p.$modal.msgSuccess("发送通知成功")})}function Q(){p.$confirm("是否确认导出通知公告表数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await p.downFile("/system/Notice/export",{...u})})}const T=_(!1),v=_(void 0),A=function(a){T.value=!0,v.value={...a}};return b(),(a,t)=>{const I=i("el-input"),V=i("el-form-item"),G=i("el-option"),J=i("el-select"),m=i("el-button"),W=i("el-form"),N=i("el-col"),X=i("right-toolbar"),Z=i("el-row"),r=i("el-table-column"),ee=i("el-link"),L=i("dict-tag"),te=i("DictTag"),ne=i("el-table"),oe=i("pagination"),le=i("zr-dialog"),h=z("hasPermi"),ae=z("loading");return c(),x("div",ve,[f(n(W,{model:l(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[n(V,{label:"公告标题",prop:"noticeTitle"},{default:o(()=>[n(I,{modelValue:l(u).noticeTitle,"onUpdate:modelValue":t[0]||(t[0]=e=>l(u).noticeTitle=e),placeholder:"请输入公告标题",clearable:"",onKeyup:re(S,["enter"])},null,8,["modelValue"])]),_:1}),n(V,{label:"类型",prop:"noticeType"},{default:o(()=>[n(J,{modelValue:l(u).noticeType,"onUpdate:modelValue":t[1]||(t[1]=e=>l(u).noticeType=e),placeholder:"公告类型",clearable:""},{default:o(()=>[(c(!0),x(M,null,ce(l(w).sys_notice_type,e=>(c(),g(G,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(V,null,{default:o(()=>[n(m,{type:"primary",icon:"Search",onClick:S},{default:o(()=>[d(s(a.$t("btn.search")),1)]),_:1}),n(m,{icon:"Refresh",onClick:F},{default:o(()=>[d(s(a.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ue,l(k)]]),n(Z,{gutter:10,class:"mb8"},{default:o(()=>[n(N,{span:1.5},{default:o(()=>[f((c(),g(m,{type:"primary",plain:"",icon:"Plus",onClick:j},{default:o(()=>[d(s(a.$t("btn.add")),1)]),_:1})),[[h,["system:notice:add"]]])]),_:1}),n(N,{span:1.5},{default:o(()=>[f((c(),g(m,{type:"danger",plain:"",icon:"Delete",disabled:l(P),onClick:Y},{default:o(()=>[d(s(a.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[h,["system:notice:delete"]]])]),_:1}),n(N,{span:1.5},{default:o(()=>[f((c(),g(m,{type:"warning",plain:"",icon:"download",onClick:Q},{default:o(()=>[d(s(a.$t("btn.export")),1)]),_:1})),[[h,["system:notice:export"]]])]),_:1}),n(X,{showSearch:l(k),"onUpdate:showSearch":t[2]||(t[2]=e=>q(k)?k.value=e:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),f((c(),g(ne,{data:l(B),onSelectionChange:H},{default:o(()=>[n(r,{type:"selection",width:"55",align:"center"}),n(r,{label:"序号",align:"center",prop:"noticeId",width:"100"}),n(r,{label:"公告标题",prop:"noticeTitle","show-overflow-tooltip":!0},{default:o(({row:e})=>[n(ee,{type:"primary",onClick:D=>A(e)},{default:o(()=>[d(s(e.noticeTitle),1)]),_:2},1032,["onClick"])]),_:1}),n(r,{label:"公告类型",align:"center",prop:"noticeType",width:"100"},{default:o(e=>[n(L,{options:l(w).sys_notice_type,value:e.row.noticeType},null,8,["options","value"])]),_:1}),n(r,{label:"状态",align:"center",prop:"status",width:"100"},{default:o(e=>[n(L,{options:l(w).sys_notice_status,value:e.row.status},null,8,["options","value"])]),_:1}),n(r,{label:"显示时间",width:"130"},{default:o(e=>[y("div",null,s(e.row.beginTime),1),y("div",null,s(e.row.endTime),1)]),_:1}),n(r,{label:"发布人",align:"center",prop:"publisher"}),n(r,{label:"是否弹出",align:"center",prop:"popup"},{default:o(e=>[n(te,{options:l(w).popupStatus,value:e.row.popup},null,8,["options","value"])]),_:1}),n(r,{label:"创建者",align:"center",prop:"createBy",width:"100"}),n(r,{label:"创建时间",align:"center",prop:"createTime",width:"100"},{default:o(e=>[y("span",null,s(a.parseTime(e.row.createTime,"YYYY-MM-DD")),1)]),_:1}),n(r,{label:"操作"},{default:o(e=>[f((c(),g(m,{text:"",icon:"bell",onClick:D=>O(e.row)},{default:o(()=>t[8]||(t[8]=[d(" 通知")])),_:2},1032,["onClick"])),[[h,["system:notice:update"]]]),f((c(),g(m,{text:"",icon:"Edit",onClick:D=>K(e.row)},{default:o(()=>t[9]||(t[9]=[d(" 修改")])),_:2},1032,["onClick"])),[[h,["system:notice:update"]]]),f((c(),g(m,{text:"",icon:"Delete",onClick:D=>Y(e.row)},{default:o(()=>t[10]||(t[10]=[d(" 删除")])),_:2},1032,["onClick"])),[[h,["system:notice:delete"]]])]),_:1})]),_:1},8,["data"])),[[ae,l(C)]]),n(oe,{total:l(R),page:l(u).pageNum,"onUpdate:page":t[3]||(t[3]=e=>l(u).pageNum=e),limit:l(u).pageSize,"onUpdate:limit":t[4]||(t[4]=e=>l(u).pageSize=e),onPagination:b},null,8,["total","page","limit"]),n(fe,{ref:"publishRef",options:l(w),onSuccess:t[5]||(t[5]=e=>b())},null,8,["options"]),n(le,{title:"预览",draggable:"",modelValue:l(T),"onUpdate:modelValue":t[7]||(t[7]=e=>q(T)?T.value=e:null),width:"580px"},{footer:o(()=>[n(m,{type:"primary",onClick:t[6]||(t[6]=e=>T.value=!1)},{default:o(()=>[d(s(a.$t("btn.submit")),1)]),_:1})]),default:o(()=>[l(v)?(c(),x(M,{key:0},[y("h2",he,s(l(v).noticeTitle),1),y("div",{innerHTML:l(v).noticeContent},null,8,we),y("div",Te,s(l(v).create_by),1),y("div",ke,s(l(de)(l(v).create_time).format("YYYY-MM-DD HH:mm")),1)],64)):pe("",!0)]),_:1},8,["modelValue"])])}}});export{De as default};
