import{r as d,E as P,a5 as R,x as s,N as U,o as T,c as Y,i as e,l as n,k as o,m as z,D as k,O as D,p as E,j as F,t as j,S as I,s as L,v as M,d as O}from"./index-CX4J5aM5.js";import{l as Q}from"./operlog-DO_rHEd0.js";const $={class:"app-container"},A=L({name:"operlog"}),W=Object.assign(A,{setup(G){const{proxy:c}=M(),m=d(!0),p=d(0),_=d([]),i=d([]),g=P({form:{},queryParams:{pageNum:1,pageSize:20,title:void 0,operName:void 0,businessType:void 0,status:void 0},options:{sys_oper_type:[],sys_common_status:[]}}),{form:H,queryParams:l,options:v}=R(g);var h=[{dictType:"sys_oper_type"},{dictType:"sys_common_status"}];c.getDicts(h).then(r=>{r.data.forEach(t=>{g.options[t.dictType]=t.list})});function f(){m.value=!0,l.value.operName=O().userInfo.userName,Q(c.addDateRange(l.value,i.value)).then(r=>{m.value=!1,r.code==200?(_.value=r.data.result,p.value=r.data.totalNum):(p.value=0,_.value=[])})}function x(){i.value=[],c.resetForm("queryForm"),y()}function y(){l.pageNum=1,f()}return f(),(r,t)=>{const S=s("el-date-picker"),b=s("el-form-item"),N=s("el-button"),C=s("el-form"),u=s("el-table-column"),w=s("dict-tag"),V=s("el-table"),q=s("pagination"),B=U("loading");return T(),Y("div",$,[e(C,{model:o(l),ref:"queryForm",inline:!0},{default:n(()=>[e(b,null,{default:n(()=>[e(S,{modelValue:o(i),"onUpdate:modelValue":t[0]||(t[0]=a=>z(i)?i.value=a:null),style:{width:"240px"},"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(b,null,{default:n(()=>[e(N,{type:"primary",icon:"search",onClick:y},{default:n(()=>t[3]||(t[3]=[k("搜索")])),_:1}),e(N,{icon:"refresh",onClick:x},{default:n(()=>t[4]||(t[4]=[k("重置")])),_:1})]),_:1})]),_:1},8,["model"]),D((T(),E(V,{data:o(_)},{default:n(()=>[e(u,{label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0}),e(u,{prop:"businessType",label:"业务类型",align:"center"},{default:n(a=>[e(w,{options:o(v).sys_oper_type,value:a.row.businessType},null,8,["options","value"])]),_:1}),e(u,{label:"操作地点",align:"center",prop:"operLocation","show-overflow-tooltip":!0}),e(u,{label:"操作状态",align:"center",prop:"status"},{default:n(({row:a})=>[e(w,{options:o(v).sys_common_status,value:a.status},null,8,["options","value"])]),_:1}),e(u,{label:"操作日期",align:"center",prop:"operTime",width:"180"},{default:n(a=>[F("span",null,j(a.row.operTime),1)]),_:1})]),_:1},8,["data"])),[[B,o(m)]]),D(e(q,{total:o(p),page:o(l).pageNum,"onUpdate:page":t[1]||(t[1]=a=>o(l).pageNum=a),limit:o(l).pageSize,"onUpdate:limit":t[2]||(t[2]=a=>o(l).pageSize=a),onPagination:f},null,8,["total","page","limit"]),[[I,o(p)>0]])])}}});export{W as default};
