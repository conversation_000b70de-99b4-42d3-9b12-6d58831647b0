import{ai as O,r as a,E as x,a5 as K,x as u,N as D,o as d,c as W,O as I,S as X,k as e,i as o,l as i,m as N,D as C,t as P,A as Z,p,q as g,s as ee,v as te}from"./index-CX4J5aM5.js";function oe(b){return O({url:"monitor/UserOnlineLog/list",method:"get",params:b})}function le(b){return O({url:"monitor/UserOnlineLog/delete/"+b,method:"delete"})}const ne=ee({name:"useronlinelog"}),ie=Object.assign(ne,{setup(b){const{proxy:c}=te(),$=a([]),T=a(!1),_=a(!0),l=x({pageNum:1,pageSize:10,sort:"Id",sortType:"desc",userId:void 0,userIP:void 0}),m=a([{visible:!1,prop:"id",label:"Id"},{visible:!0,prop:"userId",label:"用户id"},{visible:!0,prop:"onlineTime",label:"在线时长(分)"},{visible:!0,prop:"addTime",label:"结束时间"},{visible:!0,prop:"location",label:"地址位置"},{visible:!0,prop:"userIP",label:"用户IP"},{visible:!0,prop:"remark",label:"备注"}]),S=a(0),V=a([]),q=a(),B=a([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]),h=a([]);function w(){c.addDateRange(l,h.value,"AddTime"),T.value=!0,oe(l).then(n=>{const{code:t,data:f}=n;t==200&&(V.value=f.result,S.value=f.totalNum,T.value=!1)})}function v(){l.pageNum=1,w()}function L(){c.resetForm("queryRef"),v()}function R(n){var t=void 0,f=void 0;n.prop!=null&&n.order!=null&&(t=n.prop,f=n.order),l.sort=t,l.sortType=f,v()}a(),a(""),a(0),a(!1);const z=x({single:!0,multiple:!0,form:{},rules:{id:[{required:!0,message:"Id不能为空",trigger:"blur",type:"number"}],userId:[{required:!0,message:"用户id不能为空",trigger:"blur",type:"number"}],onlineTime:[{required:!0,message:"在线时长(分)不能为空",trigger:"blur",type:"number"}]},options:{}});K(z);function M(n){const t=n.id||$.value;c.$confirm('是否确认删除参数编号为"'+t+'"的数据项？',"警告",{confirmButtonText:c.$t("common.ok"),cancelButtonText:c.$t("common.cancel"),type:"warning"}).then(function(){return le(t)}).then(()=>{w(),c.$modal.msgSuccess("删除成功")})}function Y(){c.$confirm("是否确认导出用户在线时长数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await c.downFile("/monitor/UserOnlineLog/export",{...l})})}return v(),(n,t)=>{const f=u("el-input"),y=u("el-form-item"),A=u("el-date-picker"),k=u("el-button"),E=u("el-form"),Q=u("el-col"),F=u("right-toolbar"),H=u("el-row"),s=u("el-table-column"),j=u("el-table"),G=u("pagination"),U=D("hasPermi"),J=D("loading");return d(),W("div",null,[I(o(E,{model:e(l),"label-position":"right",inline:"",ref_key:"queryRef",ref:q,onSubmit:t[3]||(t[3]=Z(()=>{},["prevent"]))},{default:i(()=>[o(y,{label:"用户id",prop:"userId"},{default:i(()=>[o(f,{modelValue:e(l).userId,"onUpdate:modelValue":t[0]||(t[0]=r=>e(l).userId=r),modelModifiers:{number:!0},placeholder:"请输入用户id"},null,8,["modelValue"])]),_:1}),o(y,{label:"用户IP",prop:"userIP"},{default:i(()=>[o(f,{modelValue:e(l).userIP,"onUpdate:modelValue":t[1]||(t[1]=r=>e(l).userIP=r),placeholder:"请输入用户IP"},null,8,["modelValue"])]),_:1}),o(y,{label:"结束时间"},{default:i(()=>[o(A,{modelValue:e(h),"onUpdate:modelValue":t[2]||(t[2]=r=>N(h)?h.value=r:null),type:"datetimerange","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD HH:mm:ss","default-time":e(B),shortcuts:n.dateOptions},null,8,["modelValue","default-time","shortcuts"])]),_:1}),o(y,null,{default:i(()=>[o(k,{icon:"search",type:"primary",onClick:v},{default:i(()=>[C(P(n.$t("btn.search")),1)]),_:1}),o(k,{icon:"refresh",onClick:L},{default:i(()=>[C(P(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[X,e(_)]]),o(H,{gutter:15,class:"mb10"},{default:i(()=>[o(Q,{span:1.5},{default:i(()=>[I((d(),p(k,{type:"warning",plain:"",icon:"download",onClick:Y},{default:i(()=>[C(P(n.$t("btn.export")),1)]),_:1})),[[U,["useronlinelog:export"]]])]),_:1}),o(F,{showSearch:e(_),"onUpdate:showSearch":t[4]||(t[4]=r=>N(_)?_.value=r:null),onQueryTable:w,columns:e(m)},null,8,["showSearch","columns"])]),_:1}),I((d(),p(j,{data:e(V),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:R},{default:i(()=>[e(m).showColumn("id")?(d(),p(s,{key:0,prop:"id",label:"Id"})):g("",!0),e(m).showColumn("userId")?(d(),p(s,{key:1,prop:"userId",label:"用户id",align:"center"})):g("",!0),o(s,{prop:"nickName",label:"用户昵称",align:"center"}),o(s,{prop:"platform",label:"登录平台",align:"center"}),e(m).showColumn("onlineTime")?(d(),p(s,{key:2,prop:"onlineTime",label:"在线时长(分)",align:"center"})):g("",!0),e(m).showColumn("onlineTime")?(d(),p(s,{key:3,prop:"todayOnlineTime",label:"今日在线时长",align:"center"})):g("",!0),e(m).showColumn("addTime")?(d(),p(s,{key:4,prop:"addTime",label:"结束时间",width:"170","show-overflow-tooltip":!0})):g("",!0),e(m).showColumn("location")?(d(),p(s,{key:5,prop:"location",label:"地址位置",align:"center","show-overflow-tooltip":!0})):g("",!0),e(m).showColumn("userIP")?(d(),p(s,{key:6,prop:"userIP",label:"用户IP",align:"center","show-overflow-tooltip":!0})):g("",!0),e(m).showColumn("remark")?(d(),p(s,{key:7,prop:"remark",label:"备注",align:"center","show-overflow-tooltip":!0})):g("",!0),o(s,{label:"操作",width:"90",align:"center"},{default:i(r=>[I(o(k,{type:"danger",size:"small",icon:"delete",title:"删除",onClick:re=>M(r.row)},null,8,["onClick"]),[[U,["useronlinelog:delete"]]])]),_:1})]),_:1},8,["data"])),[[J,e(T)]]),o(G,{total:e(S),page:e(l).pageNum,"onUpdate:page":t[5]||(t[5]=r=>e(l).pageNum=r),limit:e(l).pageSize,"onUpdate:limit":t[6]||(t[6]=r=>e(l).pageSize=r),onPagination:w},null,8,["total","page","limit"])])}}});export{ie as default};
