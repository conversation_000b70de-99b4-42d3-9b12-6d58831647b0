import{ai as J,r as m,E as Y,a5 as me,x as u,N as E,o as d,c as Q,O as S,S as ce,k as e,i as l,l as t,F as be,K as ge,p,j as D,t as h,m as H,D as w,A as _e,q as _,a6 as ve,s as ye,v as he}from"./index-CX4J5aM5.js";import{U as we}from"./index.es-DgVx0caY.js";import{H as Ve}from"./index-CUndBEIL.js";function Te(N){return J({url:"monitor/SqlDiffLog/list",method:"get",params:N})}function De(N){return J({url:"monitor/SqlDiffLog/"+N,method:"delete"})}const Ne={class:"fl"},ke={class:"fr",style:{color:"var(--el-text-color-secondary)"}},Ce=["innerHTML"],qe=["innerHTML"],Se=["innerHTML"],Le=ye({name:"sqldifflog"}),He=Object.assign(Le,{setup(N){const{proxy:v}=he(),K=m([]),L=m(!1),k=m(!0),s=Y({pageNum:1,pageSize:10,sort:"PId",sortType:"desc",tableName:void 0,diffType:void 0,userName:void 0,addTime:void 0}),b=m([{visible:!0,prop:"pId",label:"主键"},{visible:!0,prop:"tableName",label:"表名"},{visible:!0,prop:"businessData",label:"业务数据内容"},{visible:!0,prop:"diffType",label:"差异类型"},{visible:!0,prop:"sql",label:"执行sql语句"},{visible:!0,prop:"beforeData",label:"变更前数据"},{visible:!0,prop:"afterData",label:"变更后数据"},{visible:!0,prop:"userName",label:"操作用户名"},{visible:!1,prop:"addTime",label:"操作时间"},{visible:!1,prop:"configId",label:"数据库配置id"}]),M=m(0),R=m([]),G=m(),W=m([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]),V=m([]);function C(){v.addDateRange(s,V.value,"AddTime"),L.value=!0,Te(s).then(r=>{const{code:o,data:f}=r;o==200&&(R.value=f.result,M.value=f.totalNum,L.value=!1)})}function q(){s.pageNum=1,C()}function X(){V.value=[],v.resetForm("queryRef"),q()}function Z(r){var o=void 0,f=void 0;r.prop!=null&&r.order!=null&&(o=r.prop,f=r.order),s.sort=o,s.sortType=f,q()}const ee=m(),$=m(""),I=m(0),T=m(!1),le=Y({single:!0,multiple:!0,form:{},options:{diffTypeOptions:[{dictLabel:"insert",dictValue:"insert"},{dictLabel:"update",dictValue:"update",listClass:"success"},{dictLabel:"delete",dictValue:"delete",listClass:"danger"}]}}),{form:n,options:U}=me(le);function O(){T.value=!1,j()}function j(){n.value={pId:null,tableName:null,businessData:null,diffType:null,sql:null,beforeData:null,afterData:null,userName:null,addTime:null,configId:null},v.resetForm("formRef")}function te(r){const o=r.pId||K.value;v.$confirm('是否确认删除参数编号为"'+o+'"的数据项？').then(function(){return De(o)}).then(()=>{C(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(r){j(),T.value=!0,$.value="查看",I.value=3,n.value={...r}}function ae(){v.$confirm("是否确认导出数据差异日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await v.downFile("/monitor/SqlDiffLog/export",{...s})})}function x(r){return Ve.highlightAuto(r||"").value||"&nbsp;"}return q(),(r,o)=>{const f=u("el-input"),i=u("el-form-item"),ne=u("el-option"),re=u("el-select"),B=u("el-date-picker"),y=u("el-button"),P=u("el-form"),c=u("el-col"),se=u("right-toolbar"),z=u("el-row"),g=u("el-table-column"),F=u("dict-tag"),de=u("el-table"),ie=u("pagination"),ue=u("zr-dialog"),A=E("hasPermi"),pe=E("loading");return d(),Q("div",null,[S(l(P,{model:e(s),"label-position":"right",inline:"",ref_key:"queryRef",ref:G,onSubmit:o[4]||(o[4]=_e(()=>{},["prevent"]))},{default:t(()=>[l(i,{label:"表名",prop:"tableName"},{default:t(()=>[l(f,{modelValue:e(s).tableName,"onUpdate:modelValue":o[0]||(o[0]=a=>e(s).tableName=a),placeholder:"请输入表名"},null,8,["modelValue"])]),_:1}),l(i,{label:"差异类型",prop:"diffType"},{default:t(()=>[l(re,{clearable:"",modelValue:e(s).diffType,"onUpdate:modelValue":o[1]||(o[1]=a=>e(s).diffType=a),placeholder:"请选择差异类型"},{default:t(()=>[(d(!0),Q(be,null,ge(e(U).diffTypeOptions,a=>(d(),p(ne,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},{default:t(()=>[D("span",Ne,h(a.dictLabel),1),D("span",ke,h(a.dictValue),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"操作用户名",prop:"userName"},{default:t(()=>[l(f,{modelValue:e(s).userName,"onUpdate:modelValue":o[2]||(o[2]=a=>e(s).userName=a),placeholder:"请输入操作用户名"},null,8,["modelValue"])]),_:1}),l(i,{label:"操作时间"},{default:t(()=>[l(B,{modelValue:e(V),"onUpdate:modelValue":o[3]||(o[3]=a=>H(V)?V.value=a:null),type:"datetimerange","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD HH:mm:ss","default-time":e(W),shortcuts:r.dateOptions},null,8,["modelValue","default-time","shortcuts"])]),_:1}),l(i,null,{default:t(()=>[l(y,{icon:"search",type:"primary",onClick:q},{default:t(()=>[w(h(r.$t("btn.search")),1)]),_:1}),l(y,{icon:"refresh",onClick:X},{default:t(()=>[w(h(r.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ce,e(k)]]),l(z,{gutter:10,class:"mb8"},{default:t(()=>[l(c,{span:1.5},{default:t(()=>[S((d(),p(y,{type:"warning",plain:"",icon:"download",onClick:ae},{default:t(()=>[w(h(r.$t("btn.export")),1)]),_:1})),[[A,["sqldifflog:export"]]])]),_:1}),l(se,{showSearch:e(k),"onUpdate:showSearch":o[5]||(o[5]=a=>H(k)?k.value=a:null),onQueryTable:C,columns:e(b)},null,8,["showSearch","columns"])]),_:1}),S((d(),p(de,{data:e(R),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:Z},{default:t(()=>[e(b).showColumn("pId")?(d(),p(g,{key:0,prop:"pId",label:"主键",align:"center",width:"150"})):_("",!0),e(b).showColumn("tableName")?(d(),p(g,{key:1,prop:"tableName",label:"表名",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("diffType")?(d(),p(g,{key:2,prop:"diffType",label:"操作类型",align:"center"},{default:t(a=>[l(F,{options:e(U).diffTypeOptions,value:a.row.diffType},null,8,["options","value"])]),_:1})):_("",!0),e(b).showColumn("businessData")?(d(),p(g,{key:3,prop:"businessData",label:"业务数据内容",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("sql")?(d(),p(g,{key:4,prop:"sql",label:"执行sql语句",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("beforeData")?(d(),p(g,{key:5,prop:"beforeData",label:"变更前数据",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("afterData")?(d(),p(g,{key:6,prop:"afterData",label:"变更后数据",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("userName")?(d(),p(g,{key:7,prop:"userName",label:"操作用户名",align:"center","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("addTime")?(d(),p(g,{key:8,prop:"addTime",label:"操作时间","show-overflow-tooltip":!0})):_("",!0),e(b).showColumn("configId")?(d(),p(g,{key:9,prop:"configId",label:"数据库配置id",align:"center","show-overflow-tooltip":!0})):_("",!0),l(g,{label:"操作",width:"130"},{default:t(a=>[l(y,{text:"",type:"primary",icon:"view",title:"详情",onClick:fe=>oe(a.row)},{default:t(()=>o[15]||(o[15]=[w("详细")])),_:2},1032,["onClick"]),S((d(),p(y,{type:"danger",icon:"delete",title:"删除",text:"",onClick:fe=>te(a.row)},{default:t(()=>o[16]||(o[16]=[w("删除")])),_:2},1032,["onClick"])),[[A,["sqldifflog:delete"]]])]),_:1})]),_:1},8,["data"])),[[pe,e(L)]]),l(ie,{total:e(M),page:e(s).pageNum,"onUpdate:page":o[6]||(o[6]=a=>e(s).pageNum=a),limit:e(s).pageSize,"onUpdate:limit":o[7]||(o[7]=a=>e(s).pageSize=a),onPagination:C},null,8,["total","page","limit"]),l(ue,{title:e($),"lock-scroll":!1,modelValue:e(T),"onUpdate:modelValue":o[14]||(o[14]=a=>H(T)?T.value=a:null),onClose:O},ve({default:t(()=>[l(P,{ref_key:"formRef",ref:ee,model:e(n),"label-width":"100px"},{default:t(()=>[l(z,{gutter:20},{default:t(()=>[l(c,{lg:12},{default:t(()=>[l(i,{label:"主键",prop:"pId"},{default:t(()=>[l(f,{modelValue:e(n).pId,"onUpdate:modelValue":o[8]||(o[8]=a=>e(n).pId=a),modelModifiers:{number:!0},placeholder:"请输入主键",disabled:e(I)!=1},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(c,{lg:12},{default:t(()=>[l(i,{label:"表名",prop:"tableName"},{default:t(()=>[l(f,{modelValue:e(n).tableName,"onUpdate:modelValue":o[9]||(o[9]=a=>e(n).tableName=a),disabled:"",placeholder:"请输入表名"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{lg:24},{default:t(()=>[l(i,{label:"业务数据内容",prop:"businessData"},{default:t(()=>[l(f,{type:"textarea",modelValue:e(n).businessData,"onUpdate:modelValue":o[10]||(o[10]=a=>e(n).businessData=a),placeholder:"请输入业务数据内容"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{lg:12},{default:t(()=>[l(i,{label:"操作类型",prop:"diffType"},{default:t(()=>[l(F,{options:e(U).diffTypeOptions,value:e(n).diffType},null,8,["options","value"])]),_:1})]),_:1}),l(c,{lg:24},{default:t(()=>[l(i,{label:"执行sql语句",prop:"sql"},{default:t(()=>[D("code",{class:"hljs",innerHTML:x(e(n).sql)},null,8,Ce)]),_:1})]),_:1}),l(c,{lg:24},{default:t(()=>[l(i,{label:"变更前数据",prop:"beforeData"},{default:t(()=>[D("code",{class:"hljs",innerHTML:x(e(n).beforeData)},null,8,qe)]),_:1})]),_:1}),l(c,{lg:24},{default:t(()=>[l(i,{label:"变更后数据"},{default:t(()=>[D("code",{class:"hljs",innerHTML:x(e(n).afterData)},null,8,Se)]),_:1})]),_:1}),l(c,{lg:24},{default:t(()=>[l(e(we),{"old-string":e(n).beforeData,"new-string":e(n).afterData,language:"json","output-format":"side-by-side"},null,8,["old-string","new-string"])]),_:1}),l(c,{lg:12},{default:t(()=>[l(i,{label:"操作用户名",prop:"userName"},{default:t(()=>[l(f,{modelValue:e(n).userName,"onUpdate:modelValue":o[11]||(o[11]=a=>e(n).userName=a),placeholder:"请输入操作用户名",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{lg:12},{default:t(()=>[l(i,{label:"记录时间",prop:"addTime"},{default:t(()=>[l(B,{modelValue:e(n).addTime,"onUpdate:modelValue":o[12]||(o[12]=a=>e(n).addTime=a),disabled:"",type:"datetime",teleported:!1,placeholder:"选择日期时间"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{lg:12},{default:t(()=>[l(i,{label:"数据库配置id",prop:"configId"},{default:t(()=>[l(f,{modelValue:e(n).configId,"onUpdate:modelValue":o[13]||(o[13]=a=>e(n).configId=a),disabled:"",placeholder:"请输入数据库配置id"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:2},[e(I)!=3?{name:"footer",fn:t(()=>[l(y,{text:"",onClick:O},{default:t(()=>[w(h(r.$t("btn.cancel")),1)]),_:1})]),key:"0"}:void 0]),1032,["title","modelValue"])])}}});export{He as default};
