import{_ as r,r as o,M as c,N as a,O as l,o as d,c as m,j as g,B as h}from"./index-CX4J5aM5.js";const u={name:"Swagger",setup(){const i=o("/dev-api/swagger/index.html"),t=o(document.documentElement.clientHeight-94.5+"px;"),n=o(!0);return c(()=>{setTimeout(()=>{n.value=!1},230),window.onresize=function(){t.value=document.documentElement.clientHeight-94.5+"px;"}}),{src:i,height:t,loading:n}}},_=["src"];function f(i,t,n,e,p,v){const s=a("loading");return l((d(),m("div",{style:h("height:"+e.height)},[g("iframe",{src:e.src,frameborder:"no",style:{width:"100%",height:"100%"},scrolling:"auto"},null,8,_)],4)),[[s,e.loading]])}const w=r(u,[["render",f]]);export{w as default};
