import{_ as O,a0 as A,r as p,w as D,e as B,M as Q,x as n,o,c as m,i as d,l as s,F as V,K as b,j as M,D as z,t as S,q as i,p as u,P as j,Q as C}from"./index-CX4J5aM5.js";const L={components:{QuestionFilled:A},props:{initialParameters:{type:String,default:"[]"},programInputParameters:{type:String,required:!0},taskType:{type:Number,default:0}},emits:["update:parameters","file-type-error"],setup(N,{emit:_}){const c=p({}),t=p([]),k=p([]),U=p([]),v=p([]),P=p(null),g=p({}),F=()=>{const e={};return t.value.forEach(l=>{const y=c.value[l.ParametersName];l.ParametersType==="ResourceSelection"&&Array.isArray(y)?e[l.ParametersName]=y.join("|"):e[l.ParametersName]=y}),e};D(c,()=>{const e=F();_("update:parameters",JSON.stringify(e))},{deep:!0});const R=()=>{const e={};t.value.forEach(l=>{l.IsRequired&&(l.ParametersType==="string"||l.ParametersType==="int")&&(e[l.ParametersName]=[{required:!0,message:`请输入${l.ParametersName}`,trigger:"blur"}])}),g.value=e};D(()=>N.programInputParameters,()=>{try{t.value=JSON.parse(N.programInputParameters);const e=N.initialParameters?JSON.parse(N.initialParameters):{};t.value.forEach(l=>{e[l.ParametersName]?c.value[l.ParametersName]=e[l.ParametersName]:l.DefaultValue&&(l.ParametersType==="string"||l.ParametersType==="int")?c.value[l.ParametersName]=l.DefaultValue:c.value[l.ParametersName]=""}),R()}catch(e){console.error("解析参数失败:",e)}},{immediate:!0});const T=(e,l)=>{if(l==="InputFile"){const y=e.name.toLowerCase();if(!(y.endsWith(".xlsx")||y.endsWith(".xls")))return _("file-type-error","InputFile只能上传xlsx或xls格式的文件"),!1}return!0},w=(e,l)=>{e.fileId?(c.value[l]=e.fileId.toString(),_("update:parameters",JSON.stringify(c.value))):console.error("文件上传失败:",e)},f=e=>{console.error("文件上传失败:",e)},h=e=>e,x=B(()=>"/api/FileStorage/uploadForTask"),E=()=>{j.warning("只能上传一个文件，请先删除已上传的文件")},a=async()=>{try{const e=await C.get("/api/ResourcePool");k.value=e.data}catch(e){console.error("获取资源池列表失败:",e)}},I=async()=>{try{const e=await C.get("/api/ResourcePool/machines");U.value=e.data}catch(e){console.error("获取资源机列表失败:",e)}},r=async()=>{try{const e=await C.get("/api/RpaCredential");v.value=e.data}catch(e){console.error("获取RPA账号凭证列表失败:",e)}},J=async()=>{await Promise.all([a(),I(),r()])};return Q(()=>{J()}),{parameters:c,parsedParameters:t,handleFileUpload:w,handleUploadError:f,getFileName:h,uploadUrl:x,beforeFileUpload:T,handleExceed:E,resourcePools:k,resourceMachines:U,rpaCredentials:v,formRef:P,rules:g,validateForm:async()=>{if(!P.value)return!0;try{return await P.value.validate(),!0}catch{return!1}}}}},W={class:"parameter-name"},K={key:0,class:"required-mark"},G={key:0,class:"el-upload__tip uploaded-file"},H={class:"parameter-description"};function X(N,_,c,t,k,U){const v=n("el-input"),P=n("el-form-item"),g=n("el-input-number"),F=n("el-button"),R=n("QuestionFilled"),q=n("el-icon"),T=n("el-tooltip"),w=n("el-upload"),f=n("el-option"),h=n("el-select"),x=n("el-option-group"),E=n("el-form");return o(),m("div",null,[d(E,{model:t.parameters,"label-width":"120px",ref:"formRef",rules:t.rules},{default:s(()=>[(o(!0),m(V,null,b(t.parsedParameters,(a,I)=>(o(),m("div",{key:I,class:"parameter-row"},[M("span",W,[z(S(a.ParametersName)+" ",1),a.IsRequired?(o(),m("span",K,"*")):i("",!0)]),a.ParametersType==="string"?(o(),u(P,{key:0,prop:a.ParametersName},{default:s(()=>[d(v,{modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,class:"parameter-value",placeholder:a.IsRequired?"请输入"+a.ParametersName:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["prop"])):i("",!0),a.ParametersType==="int"?(o(),u(P,{key:1,prop:a.ParametersName},{default:s(()=>[d(g,{modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,class:"parameter-value"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])):i("",!0),a.ParametersType==="bool"?(o(),u(v,{key:2,modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,class:"parameter-value"},null,8,["modelValue","onUpdate:modelValue"])):i("",!0),a.ParametersType==="file"?(o(),u(w,{key:3,action:t.uploadUrl,"on-success":r=>t.handleFileUpload(r,a.ParametersName),"on-error":t.handleUploadError,"before-upload":r=>t.beforeFileUpload(r,a.ParametersName),limit:1,"on-exceed":t.handleExceed,class:"parameter-value upload-compact"},{tip:s(()=>[t.parameters[a.ParametersName]?(o(),m("div",G," 已上传: "+S(t.getFileName(t.parameters[a.ParametersName])),1)):i("",!0),a.ParametersName==="InputFile"?(o(),u(T,{key:1,content:"只能上传 xlsx/xls 格式文件",placement:"top"},{default:s(()=>[d(q,null,{default:s(()=>[d(R)]),_:1})]),_:1})):i("",!0)]),default:s(()=>[d(F,{size:"small",type:"primary"},{default:s(()=>_[0]||(_[0]=[z("上传文件")])),_:1})]),_:2},1032,["action","on-success","on-error","before-upload","on-exceed"])):i("",!0),a.ParametersType==="select"?(o(),u(h,{key:4,modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,multiple:a.ParametersOptions==="2",class:"parameter-value"},{default:s(()=>[(o(!0),m(V,null,b(a.ParametersSelectValue.split("|"),r=>(o(),u(f,{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","multiple"])):i("",!0),a.ParametersType==="ResourceSelection"?(o(),u(h,{key:5,modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,multiple:"",filterable:"",placeholder:"请选择资源",class:"parameter-value"},{default:s(()=>[d(x,{label:"资源池"},{default:s(()=>[(o(!0),m(V,null,b(t.resourcePools,r=>(o(),u(f,{key:"pool_"+r.id,label:r.poolName,value:r.poolName},null,8,["label","value"]))),128))]),_:1}),d(x,{label:"资源机"},{default:s(()=>[(o(!0),m(V,null,b(t.resourceMachines,r=>(o(),u(f,{key:"machine_"+r.id,label:r.machineName,value:r.machineName},null,8,["label","value"]))),128))]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])):i("",!0),a.ParametersType==="RpaCredentials"?(o(),u(h,{key:6,modelValue:t.parameters[a.ParametersName],"onUpdate:modelValue":r=>t.parameters[a.ParametersName]=r,filterable:"",placeholder:"请选择RPA账号",class:"parameter-value"},{default:s(()=>[(o(!0),m(V,null,b(t.rpaCredentials,r=>(o(),u(f,{key:r.id,label:r.username,value:r.username},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):i("",!0),M("span",H,S(a.ParametersDescription),1)]))),128))]),_:1},8,["model","rules"])])}const ee=O(L,[["render",X],["__scopeId","data-v-58be75a9"]]);export{ee as default};
