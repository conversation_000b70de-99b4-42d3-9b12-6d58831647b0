import{s as Ee,e as ve,r as tt,w as qt,o as F,c as Z,q as ye,k as z,j as T,t as V,i as je,av as ot,p as Oe,B as Zt,F as be,K as qe,V as we}from"./index-CX4J5aM5.js";(function(){try{if(typeof document<"u"){var e=document.createElement("style");e.appendChild(document.createTextNode(".code-diff-view[theme=light]{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));--color-canvas-default-transparent: rgba(255,255,255,0);--color-page-header-bg: #f6f8fa;--color-marketing-icon-primary: #218bff;--color-marketing-icon-secondary: #54aeff;--color-diff-blob-addition-num-text: #1F2328;--color-diff-blob-addition-fg: #1F2328;--color-diff-blob-addition-num-bg: #ccffd8;--color-diff-blob-addition-line-bg: #e6ffec;--color-diff-blob-addition-word-bg: #abf2bc;--color-diff-blob-deletion-num-text: #1F2328;--color-diff-blob-deletion-fg: #1F2328;--color-diff-blob-deletion-num-bg: #ffd7d5;--color-diff-blob-deletion-line-bg: #ffebe9;--color-diff-blob-deletion-word-bg: rgba(255,129,130,.4);--color-diff-blob-hunk-num-bg: rgba(84,174,255,.4);--color-diff-blob-expander-icon: #656d76;--color-diff-blob-selected-line-highlight-mix-blend-mode: multiply;--color-diffstat-deletion-border: rgba(31,35,40,.15);--color-diffstat-addition-border: rgba(31,35,40,.15);--color-diffstat-addition-bg: #1f883d;--color-search-keyword-hl: #fff8c5;--color-prettylights-syntax-comment: #6e7781;--color-prettylights-syntax-constant: #0550ae;--color-prettylights-syntax-entity: #6639ba;--color-prettylights-syntax-storage-modifier-import: #24292f;--color-prettylights-syntax-entity-tag: #116329;--color-prettylights-syntax-keyword: #cf222e;--color-prettylights-syntax-string: #0a3069;--color-prettylights-syntax-variable: #953800;--color-prettylights-syntax-brackethighlighter-unmatched: #82071e;--color-prettylights-syntax-invalid-illegal-text: #f6f8fa;--color-prettylights-syntax-invalid-illegal-bg: #82071e;--color-prettylights-syntax-carriage-return-text: #f6f8fa;--color-prettylights-syntax-carriage-return-bg: #cf222e;--color-prettylights-syntax-string-regexp: #116329;--color-prettylights-syntax-markup-list: #3b2300;--color-prettylights-syntax-markup-heading: #0550ae;--color-prettylights-syntax-markup-italic: #24292f;--color-prettylights-syntax-markup-bold: #24292f;--color-prettylights-syntax-markup-deleted-text: #82071e;--color-prettylights-syntax-markup-deleted-bg: #ffebe9;--color-prettylights-syntax-markup-inserted-text: #116329;--color-prettylights-syntax-markup-inserted-bg: #dafbe1;--color-prettylights-syntax-markup-changed-text: #953800;--color-prettylights-syntax-markup-changed-bg: #ffd8b5;--color-prettylights-syntax-markup-ignored-text: #eaeef2;--color-prettylights-syntax-markup-ignored-bg: #0550ae;--color-prettylights-syntax-meta-diff-range: #8250df;--color-prettylights-syntax-brackethighlighter-angle: #57606a;--color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;--color-prettylights-syntax-constant-other-reference-link: #0a3069;--color-codemirror-text: #1F2328;--color-codemirror-bg: #ffffff;--color-codemirror-gutters-bg: #ffffff;--color-codemirror-guttermarker-text: #ffffff;--color-codemirror-guttermarker-subtle-text: #6e7781;--color-codemirror-linenumber-text: #656d76;--color-codemirror-cursor: #1F2328;--color-codemirror-selection-bg: rgba(84,174,255,.4);--color-codemirror-activeline-bg: rgba(234,238,242,.5);--color-codemirror-matchingbracket-text: #1F2328;--color-codemirror-lines-bg: #ffffff;--color-codemirror-syntax-comment: #24292f;--color-codemirror-syntax-constant: #0550ae;--color-codemirror-syntax-entity: #8250df;--color-codemirror-syntax-keyword: #cf222e;--color-codemirror-syntax-storage: #cf222e;--color-codemirror-syntax-string: #0a3069;--color-codemirror-syntax-support: #0550ae;--color-codemirror-syntax-variable: #953800;--color-checks-bg: #24292f;--color-checks-run-border-width: 0px;--color-checks-container-border-width: 0px;--color-checks-text-primary: #f6f8fa;--color-checks-text-secondary: #8c959f;--color-checks-text-link: #54aeff;--color-checks-btn-icon: #afb8c1;--color-checks-btn-hover-icon: #f6f8fa;--color-checks-btn-hover-bg: rgba(255,255,255,.125);--color-checks-input-text: #eaeef2;--color-checks-input-placeholder-text: #8c959f;--color-checks-input-focus-text: #8c959f;--color-checks-input-bg: #32383f;--color-checks-input-shadow: none;--color-checks-donut-error: #fa4549;--color-checks-donut-pending: #bf8700;--color-checks-donut-success: #1f883d;--color-checks-donut-neutral: #afb8c1;--color-checks-dropdown-text: #afb8c1;--color-checks-dropdown-bg: #32383f;--color-checks-dropdown-border: #424a53;--color-checks-dropdown-shadow: rgba(31,35,40,.3);--color-checks-dropdown-hover-text: #f6f8fa;--color-checks-dropdown-hover-bg: #424a53;--color-checks-dropdown-btn-hover-text: #f6f8fa;--color-checks-dropdown-btn-hover-bg: #32383f;--color-checks-scrollbar-thumb-bg: #57606a;--color-checks-header-label-text: #d0d7de;--color-checks-header-label-open-text: #f6f8fa;--color-checks-header-border: #32383f;--color-checks-header-icon: #8c959f;--color-checks-line-text: #d0d7de;--color-checks-line-num-text: rgba(140,149,159,.75);--color-checks-line-timestamp-text: #8c959f;--color-checks-line-hover-bg: #32383f;--color-checks-line-selected-bg: rgba(33,139,255,.15);--color-checks-line-selected-num-text: #54aeff;--color-checks-line-dt-fm-text: #24292f;--color-checks-line-dt-fm-bg: #9a6700;--color-checks-gate-bg: rgba(125,78,0,.15);--color-checks-gate-text: #d0d7de;--color-checks-gate-waiting-text: #d4a72c;--color-checks-step-header-open-bg: #32383f;--color-checks-step-error-text: #ff8182;--color-checks-step-warning-text: #d4a72c;--color-checks-logline-text: #8c959f;--color-checks-logline-num-text: rgba(140,149,159,.75);--color-checks-logline-debug-text: #c297ff;--color-checks-logline-error-text: #d0d7de;--color-checks-logline-error-num-text: #ff8182;--color-checks-logline-error-bg: rgba(164,14,38,.15);--color-checks-logline-warning-text: #d0d7de;--color-checks-logline-warning-num-text: #d4a72c;--color-checks-logline-warning-bg: rgba(125,78,0,.15);--color-checks-logline-command-text: #54aeff;--color-checks-logline-section-text: #4ac26b;--color-checks-ansi-black: #24292f;--color-checks-ansi-black-bright: #32383f;--color-checks-ansi-white: #d0d7de;--color-checks-ansi-white-bright: #d0d7de;--color-checks-ansi-gray: #8c959f;--color-checks-ansi-red: #ff8182;--color-checks-ansi-red-bright: #ffaba8;--color-checks-ansi-green: #4ac26b;--color-checks-ansi-green-bright: #6fdd8b;--color-checks-ansi-yellow: #d4a72c;--color-checks-ansi-yellow-bright: #eac54f;--color-checks-ansi-blue: #54aeff;--color-checks-ansi-blue-bright: #80ccff;--color-checks-ansi-magenta: #c297ff;--color-checks-ansi-magenta-bright: #d8b9ff;--color-checks-ansi-cyan: #76e3ea;--color-checks-ansi-cyan-bright: #b3f0ff;--color-project-header-bg: #24292f;--color-project-sidebar-bg: #ffffff;--color-project-gradient-in: #ffffff;--color-project-gradient-out: rgba(255,255,255,0);--color-mktg-btn-bg: #1b1f23;--color-mktg-btn-shadow-outline: rgb(0 0 0 / 15%) 0 0 0 1px inset;--color-mktg-btn-shadow-focus: rgb(0 0 0 / 15%) 0 0 0 4px;--color-mktg-btn-shadow-hover: 0 3px 2px rgba(0, 0, 0, .07), 0 7px 5px rgba(0, 0, 0, .04), 0 12px 10px rgba(0, 0, 0, .03), 0 22px 18px rgba(0, 0, 0, .03), 0 42px 33px rgba(0, 0, 0, .02), 0 100px 80px rgba(0, 0, 0, .02);--color-mktg-btn-shadow-hover-muted: rgb(0 0 0 / 70%) 0 0 0 2px inset;--color-control-border-color-emphasis: #858F99;--color-avatar-bg: #ffffff;--color-avatar-border: rgba(31,35,40,.15);--color-avatar-stack-fade: #afb8c1;--color-avatar-stack-fade-more: #d0d7de;--color-avatar-child-shadow: 0 0 0 2px rgba(255,255,255,.8);--color-topic-tag-border: rgba(0,0,0,0);--color-counter-border: rgba(0,0,0,0);--color-select-menu-backdrop-border: rgba(0,0,0,0);--color-select-menu-tap-highlight: rgba(175,184,193,.5);--color-select-menu-tap-focus-bg: #b6e3ff;--color-overlay-shadow: 0 1px 3px rgba(31,35,40,.12), 0 8px 24px rgba(66,74,83,.12);--color-overlay-backdrop: rgba(140,149,159,.2);--color-header-text: rgba(255,255,255,.7);--color-header-bg: #24292f;--color-header-divider: #57606a;--color-header-logo: #ffffff;--color-header-search-bg: #24292f;--color-header-search-border: #57606a;--color-sidenav-selected-bg: #ffffff;--color-menu-bg-active: rgba(0,0,0,0);--color-input-disabled-bg: rgba(175,184,193,.2);--color-timeline-badge-bg: #eaeef2;--color-ansi-black: #24292f;--color-ansi-black-bright: #57606a;--color-ansi-white: #6e7781;--color-ansi-white-bright: #8c959f;--color-ansi-gray: #6e7781;--color-ansi-red: #cf222e;--color-ansi-red-bright: #a40e26;--color-ansi-green: #116329;--color-ansi-green-bright: #1a7f37;--color-ansi-yellow: #4d2d00;--color-ansi-yellow-bright: #633c01;--color-ansi-blue: #0969da;--color-ansi-blue-bright: #218bff;--color-ansi-magenta: #8250df;--color-ansi-magenta-bright: #a475f9;--color-ansi-cyan: #1b7c83;--color-ansi-cyan-bright: #3192aa;--color-btn-text: #24292f;--color-btn-bg: #f6f8fa;--color-btn-border: rgba(31,35,40,.15);--color-btn-shadow: 0 1px 0 rgba(31,35,40,.04);--color-btn-inset-shadow: inset 0 1px 0 rgba(255,255,255,.25);--color-btn-hover-bg: #f3f4f6;--color-btn-hover-border: rgba(31,35,40,.15);--color-btn-active-bg: hsla(220,14%,93%,1);--color-btn-active-border: rgba(31,35,40,.15);--color-btn-selected-bg: hsla(220,14%,94%,1);--color-btn-counter-bg: rgba(31,35,40,.08);--color-btn-primary-text: #ffffff;--color-btn-primary-bg: #1f883d;--color-btn-primary-border: rgba(31,35,40,.15);--color-btn-primary-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-primary-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-primary-hover-bg: #1a7f37;--color-btn-primary-hover-border: rgba(31,35,40,.15);--color-btn-primary-selected-bg: hsla(137,66%,28%,1);--color-btn-primary-selected-shadow: inset 0 1px 0 rgba(0,45,17,.2);--color-btn-primary-disabled-text: rgba(255,255,255,.8);--color-btn-primary-disabled-bg: #94d3a2;--color-btn-primary-disabled-border: rgba(31,35,40,.15);--color-btn-primary-icon: rgba(255,255,255,.8);--color-btn-primary-counter-bg: rgba(0,45,17,.2);--color-btn-outline-text: #0969da;--color-btn-outline-hover-text: #ffffff;--color-btn-outline-hover-bg: #0969da;--color-btn-outline-hover-border: rgba(31,35,40,.15);--color-btn-outline-hover-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-outline-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-outline-hover-counter-bg: rgba(255,255,255,.2);--color-btn-outline-selected-text: #ffffff;--color-btn-outline-selected-bg: hsla(212,92%,42%,1);--color-btn-outline-selected-border: rgba(31,35,40,.15);--color-btn-outline-selected-shadow: inset 0 1px 0 rgba(0,33,85,.2);--color-btn-outline-disabled-text: rgba(9,105,218,.5);--color-btn-outline-disabled-bg: #f6f8fa;--color-btn-outline-disabled-counter-bg: rgba(9,105,218,.05);--color-btn-outline-counter-bg: #0969da1a;--color-btn-outline-counter-fg: #0550ae;--color-btn-outline-hover-counter-fg: #ffffff;--color-btn-outline-disabled-counter-fg: rgba(9,105,218,.5);--color-btn-danger-text: #cf222e;--color-btn-danger-hover-text: #ffffff;--color-btn-danger-hover-bg: #a40e26;--color-btn-danger-hover-border: rgba(31,35,40,.15);--color-btn-danger-hover-shadow: 0 1px 0 rgba(31,35,40,.1);--color-btn-danger-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-danger-hover-counter-bg: rgba(255,255,255,.2);--color-btn-danger-selected-text: #ffffff;--color-btn-danger-selected-bg: hsla(356,72%,44%,1);--color-btn-danger-selected-border: rgba(31,35,40,.15);--color-btn-danger-selected-shadow: inset 0 1px 0 rgba(76,0,20,.2);--color-btn-danger-disabled-text: rgba(207,34,46,.5);--color-btn-danger-disabled-bg: #f6f8fa;--color-btn-danger-disabled-counter-bg: rgba(207,34,46,.05);--color-btn-danger-counter-bg: rgba(207,34,46,.1);--color-btn-danger-icon: #cf222e;--color-btn-danger-hover-icon: #ffffff;--color-btn-danger-counter-fg: #a40e26;--color-btn-danger-hover-counter-fg: #ffffff;--color-btn-danger-disabled-counter-fg: rgba(207,34,46,.5);--color-underlinenav-icon: #6e7781;--color-underlinenav-border-hover: rgba(175,184,193,.2);--color-action-list-item-inline-divider: rgba(208,215,222,.48);--color-action-list-item-default-hover-bg: rgba(208,215,222,.32);--color-action-list-item-default-hover-border: rgba(0,0,0,0);--color-action-list-item-default-active-bg: rgba(208,215,222,.48);--color-action-list-item-default-active-border: rgba(0,0,0,0);--color-action-list-item-default-selected-bg: rgba(208,215,222,.24);--color-action-list-item-danger-hover-bg: rgba(255,235,233,.64);--color-action-list-item-danger-active-bg: #ffebe9;--color-action-list-item-danger-hover-text: #d1242f;--color-switch-track-bg: #eaeef2;--color-switch-track-hover-bg: hsla(210,24%,90%,1);--color-switch-track-active-bg: hsla(210,24%,88%,1);--color-switch-track-disabled-bg: #8c959f;--color-switch-track-fg: #656d76;--color-switch-track-disabled-fg: #ffffff;--color-switch-track-border: rgba(0,0,0,0);--color-switch-track-checked-bg: #0969da;--color-switch-track-checked-hover-bg: #0860CA;--color-switch-track-checked-active-bg: #0757BA;--color-switch-track-checked-fg: #ffffff;--color-switch-track-checked-disabled-fg: #ffffff;--color-switch-track-checked-border: rgba(0,0,0,0);--color-switch-knob-bg: #ffffff;--color-switch-knob-disabled-bg: #f6f8fa;--color-switch-knob-border: #858F99;--color-switch-knob-checked-bg: #ffffff;--color-switch-knob-checked-disabled-bg: #f6f8fa;--color-switch-knob-checked-border: #0969da;--color-segmented-control-bg: #eaeef2;--color-segmented-control-button-bg: #ffffff;--color-segmented-control-button-hover-bg: rgba(175,184,193,.2);--color-segmented-control-button-active-bg: rgba(175,184,193,.4);--color-segmented-control-button-selected-border: #8c959f;--color-tree-view-item-chevron-hover-bg: rgba(208,215,222,.32);--color-tree-view-item-directory-fill: #54aeff;--color-fg-default: #1F2328;--color-fg-muted: #656d76;--color-fg-subtle: #6e7781;--color-fg-on-emphasis: #ffffff;--color-canvas-default: #ffffff;--color-canvas-overlay: #ffffff;--color-canvas-inset: #f6f8fa;--color-canvas-subtle: #f6f8fa;--color-border-default: #d0d7de;--color-border-muted: hsla(210,18%,87%,1);--color-border-subtle: rgba(31,35,40,.15);--color-shadow-small: 0 1px 0 rgba(31,35,40,.04);--color-shadow-medium: 0 3px 6px rgba(140,149,159,.15);--color-shadow-large: 0 8px 24px rgba(140,149,159,.2);--color-shadow-extra-large: 0 12px 28px rgba(140,149,159,.3);--color-neutral-emphasis-plus: #24292f;--color-neutral-emphasis: #6e7781;--color-neutral-muted: rgba(175,184,193,.2);--color-neutral-subtle: rgba(234,238,242,.5);--color-accent-fg: #0969da;--color-accent-emphasis: #0969da;--color-accent-muted: rgba(84,174,255,.4);--color-accent-subtle: #ddf4ff;--color-success-fg: #1a7f37;--color-success-emphasis: #1f883d;--color-success-muted: rgba(74,194,107,.4);--color-success-subtle: #dafbe1;--color-attention-fg: #9a6700;--color-attention-emphasis: #9a6700;--color-attention-muted: rgba(212,167,44,.4);--color-attention-subtle: #fff8c5;--color-severe-fg: #bc4c00;--color-severe-emphasis: #bc4c00;--color-severe-muted: rgba(251,143,68,.4);--color-severe-subtle: #fff1e5;--color-danger-fg: #d1242f;--color-danger-emphasis: #cf222e;--color-danger-muted: rgba(255,129,130,.4);--color-danger-subtle: #ffebe9;--color-open-fg: #1a7f37;--color-open-emphasis: #1f883d;--color-open-muted: rgba(74,194,107,.4);--color-open-subtle: #dafbe1;--color-closed-fg: #d1242f;--color-closed-emphasis: #cf222e;--color-closed-muted: rgba(255,129,130,.4);--color-closed-subtle: #ffebe9;--color-done-fg: #8250df;--color-done-emphasis: #8250df;--color-done-muted: rgba(194,151,255,.4);--color-done-subtle: #fbefff;--color-sponsors-fg: #bf3989;--color-sponsors-emphasis: #bf3989;--color-sponsors-muted: rgba(255,128,200,.4);--color-sponsors-subtle: #ffeff7;--color-primer-fg-disabled: #8c959f;--color-primer-canvas-backdrop: rgba(31,35,40,.5);--color-primer-canvas-sticky: rgba(255,255,255,.95);--color-primer-border-active: #fd8c73;--color-primer-border-contrast: rgba(31,35,40,.1);--color-primer-shadow-highlight: inset 0 1px 0 rgba(255,255,255,.25);--color-primer-shadow-inset: inset 0 1px 0 rgba(208,215,222,.2);--color-scale-black: #1F2328;--color-scale-white: #ffffff;--color-scale-gray-0: #f6f8fa;--color-scale-gray-1: #eaeef2;--color-scale-gray-2: #d0d7de;--color-scale-gray-3: #afb8c1;--color-scale-gray-4: #8c959f;--color-scale-gray-5: #6e7781;--color-scale-gray-6: #57606a;--color-scale-gray-7: #424a53;--color-scale-gray-8: #32383f;--color-scale-gray-9: #24292f;--color-scale-blue-0: #ddf4ff;--color-scale-blue-1: #b6e3ff;--color-scale-blue-2: #80ccff;--color-scale-blue-3: #54aeff;--color-scale-blue-4: #218bff;--color-scale-blue-5: #0969da;--color-scale-blue-6: #0550ae;--color-scale-blue-7: #033d8b;--color-scale-blue-8: #0a3069;--color-scale-blue-9: #002155;--color-scale-green-0: #dafbe1;--color-scale-green-1: #aceebb;--color-scale-green-2: #6fdd8b;--color-scale-green-3: #4ac26b;--color-scale-green-4: #2da44e;--color-scale-green-5: #1a7f37;--color-scale-green-6: #116329;--color-scale-green-7: #044f1e;--color-scale-green-8: #003d16;--color-scale-green-9: #002d11;--color-scale-yellow-0: #fff8c5;--color-scale-yellow-1: #fae17d;--color-scale-yellow-2: #eac54f;--color-scale-yellow-3: #d4a72c;--color-scale-yellow-4: #bf8700;--color-scale-yellow-5: #9a6700;--color-scale-yellow-6: #7d4e00;--color-scale-yellow-7: #633c01;--color-scale-yellow-8: #4d2d00;--color-scale-yellow-9: #3b2300;--color-scale-orange-0: #fff1e5;--color-scale-orange-1: #ffd8b5;--color-scale-orange-2: #ffb77c;--color-scale-orange-3: #fb8f44;--color-scale-orange-4: #e16f24;--color-scale-orange-5: #bc4c00;--color-scale-orange-6: #953800;--color-scale-orange-7: #762c00;--color-scale-orange-8: #5c2200;--color-scale-orange-9: #471700;--color-scale-red-0: #ffebe9;--color-scale-red-1: #ffcecb;--color-scale-red-2: #ffaba8;--color-scale-red-3: #ff8182;--color-scale-red-4: #fa4549;--color-scale-red-5: #cf222e;--color-scale-red-6: #a40e26;--color-scale-red-7: #82071e;--color-scale-red-8: #660018;--color-scale-red-9: #4c0014;--color-scale-purple-0: #fbefff;--color-scale-purple-1: #ecd8ff;--color-scale-purple-2: #d8b9ff;--color-scale-purple-3: #c297ff;--color-scale-purple-4: #a475f9;--color-scale-purple-5: #8250df;--color-scale-purple-6: #6639ba;--color-scale-purple-7: #512a97;--color-scale-purple-8: #3e1f79;--color-scale-purple-9: #2e1461;--color-scale-pink-0: #ffeff7;--color-scale-pink-1: #ffd3eb;--color-scale-pink-2: #ffadda;--color-scale-pink-3: #ff80c8;--color-scale-pink-4: #e85aad;--color-scale-pink-5: #bf3989;--color-scale-pink-6: #99286e;--color-scale-pink-7: #772057;--color-scale-pink-8: #611347;--color-scale-pink-9: #4d0336;--color-scale-coral-0: #fff0eb;--color-scale-coral-1: #ffd6cc;--color-scale-coral-2: #ffb4a1;--color-scale-coral-3: #fd8c73;--color-scale-coral-4: #ec6547;--color-scale-coral-5: #c4432b;--color-scale-coral-6: #9e2f1c;--color-scale-coral-7: #801f0f;--color-scale-coral-8: #691105;--color-scale-coral-9: #510901}.code-diff-view[theme=light] pre code.hljs{display:block;overflow-x:auto;padding:1em}.code-diff-view[theme=light] code.hljs{padding:3px 5px}.code-diff-view[theme=light] .hljs{color:#24292e;background:#fff}.code-diff-view[theme=light] .hljs-doctag,.code-diff-view[theme=light] .hljs-keyword,.code-diff-view[theme=light] .hljs-meta .hljs-keyword,.code-diff-view[theme=light] .hljs-template-tag,.code-diff-view[theme=light] .hljs-template-variable,.code-diff-view[theme=light] .hljs-type,.code-diff-view[theme=light] .hljs-variable.language_{color:#d73a49}.code-diff-view[theme=light] .hljs-title,.code-diff-view[theme=light] .hljs-title.class_,.code-diff-view[theme=light] .hljs-title.class_.inherited__,.code-diff-view[theme=light] .hljs-title.function_{color:#6f42c1}.code-diff-view[theme=light] .hljs-attr,.code-diff-view[theme=light] .hljs-attribute,.code-diff-view[theme=light] .hljs-literal,.code-diff-view[theme=light] .hljs-meta,.code-diff-view[theme=light] .hljs-number,.code-diff-view[theme=light] .hljs-operator,.code-diff-view[theme=light] .hljs-variable,.code-diff-view[theme=light] .hljs-selector-attr,.code-diff-view[theme=light] .hljs-selector-class,.code-diff-view[theme=light] .hljs-selector-id{color:#005cc5}.code-diff-view[theme=light] .hljs-regexp,.code-diff-view[theme=light] .hljs-string,.code-diff-view[theme=light] .hljs-meta .hljs-string{color:#032f62}.code-diff-view[theme=light] .hljs-built_in,.code-diff-view[theme=light] .hljs-symbol{color:#e36209}.code-diff-view[theme=light] .hljs-comment,.code-diff-view[theme=light] .hljs-code,.code-diff-view[theme=light] .hljs-formula{color:#6a737d}.code-diff-view[theme=light] .hljs-name,.code-diff-view[theme=light] .hljs-quote,.code-diff-view[theme=light] .hljs-selector-tag,.code-diff-view[theme=light] .hljs-selector-pseudo{color:#22863a}.code-diff-view[theme=light] .hljs-subst{color:#24292e}.code-diff-view[theme=light] .hljs-section{color:#005cc5;font-weight:700}.code-diff-view[theme=light] .hljs-bullet{color:#735c0f}.code-diff-view[theme=light] .hljs-emphasis{color:#24292e;font-style:italic}.code-diff-view[theme=light] .hljs-strong{color:#24292e;font-weight:700}.code-diff-view[theme=light] .hljs-addition{color:#22863a;background-color:#f0fff4}.code-diff-view[theme=light] .hljs-deletion{color:#b31d28;background-color:#ffeef0}.code-diff-view[theme=dark]{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));--color-canvas-default-transparent: rgba(13,17,23,0);--color-page-header-bg: #0d1117;--color-marketing-icon-primary: #79c0ff;--color-marketing-icon-secondary: #1f6feb;--color-diff-blob-addition-num-text: #e6edf3;--color-diff-blob-addition-fg: #e6edf3;--color-diff-blob-addition-num-bg: rgba(63,185,80,.3);--color-diff-blob-addition-line-bg: rgba(46,160,67,.15);--color-diff-blob-addition-word-bg: rgba(46,160,67,.4);--color-diff-blob-deletion-num-text: #e6edf3;--color-diff-blob-deletion-fg: #e6edf3;--color-diff-blob-deletion-num-bg: rgba(248,81,73,.3);--color-diff-blob-deletion-line-bg: rgba(248,81,73,.1);--color-diff-blob-deletion-word-bg: rgba(248,81,73,.4);--color-diff-blob-hunk-num-bg: rgba(56,139,253,.4);--color-diff-blob-expander-icon: #7d8590;--color-diff-blob-selected-line-highlight-mix-blend-mode: screen;--color-diffstat-deletion-border: rgba(240,246,252,.1);--color-diffstat-addition-border: rgba(240,246,252,.1);--color-diffstat-addition-bg: #3fb950;--color-search-keyword-hl: rgba(210,153,34,.4);--color-prettylights-syntax-comment: #8b949e;--color-prettylights-syntax-constant: #79c0ff;--color-prettylights-syntax-entity: #d2a8ff;--color-prettylights-syntax-storage-modifier-import: #c9d1d9;--color-prettylights-syntax-entity-tag: #7ee787;--color-prettylights-syntax-keyword: #ff7b72;--color-prettylights-syntax-string: #a5d6ff;--color-prettylights-syntax-variable: #ffa657;--color-prettylights-syntax-brackethighlighter-unmatched: #f85149;--color-prettylights-syntax-invalid-illegal-text: #f0f6fc;--color-prettylights-syntax-invalid-illegal-bg: #8e1519;--color-prettylights-syntax-carriage-return-text: #f0f6fc;--color-prettylights-syntax-carriage-return-bg: #b62324;--color-prettylights-syntax-string-regexp: #7ee787;--color-prettylights-syntax-markup-list: #f2cc60;--color-prettylights-syntax-markup-heading: #1f6feb;--color-prettylights-syntax-markup-italic: #c9d1d9;--color-prettylights-syntax-markup-bold: #c9d1d9;--color-prettylights-syntax-markup-deleted-text: #ffdcd7;--color-prettylights-syntax-markup-deleted-bg: #67060c;--color-prettylights-syntax-markup-inserted-text: #aff5b4;--color-prettylights-syntax-markup-inserted-bg: #033a16;--color-prettylights-syntax-markup-changed-text: #ffdfb6;--color-prettylights-syntax-markup-changed-bg: #5a1e02;--color-prettylights-syntax-markup-ignored-text: #c9d1d9;--color-prettylights-syntax-markup-ignored-bg: #1158c7;--color-prettylights-syntax-meta-diff-range: #d2a8ff;--color-prettylights-syntax-brackethighlighter-angle: #8b949e;--color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;--color-prettylights-syntax-constant-other-reference-link: #a5d6ff;--color-codemirror-text: #e6edf3;--color-codemirror-bg: #0d1117;--color-codemirror-gutters-bg: #0d1117;--color-codemirror-guttermarker-text: #0d1117;--color-codemirror-guttermarker-subtle-text: #6e7681;--color-codemirror-linenumber-text: #7d8590;--color-codemirror-cursor: #e6edf3;--color-codemirror-selection-bg: rgba(56,139,253,.4);--color-codemirror-activeline-bg: rgba(110,118,129,.1);--color-codemirror-matchingbracket-text: #e6edf3;--color-codemirror-lines-bg: #0d1117;--color-codemirror-syntax-comment: #8b949e;--color-codemirror-syntax-constant: #79c0ff;--color-codemirror-syntax-entity: #d2a8ff;--color-codemirror-syntax-keyword: #ff7b72;--color-codemirror-syntax-storage: #ff7b72;--color-codemirror-syntax-string: #a5d6ff;--color-codemirror-syntax-support: #79c0ff;--color-codemirror-syntax-variable: #ffa657;--color-checks-bg: #010409;--color-checks-run-border-width: 1px;--color-checks-container-border-width: 1px;--color-checks-text-primary: #e6edf3;--color-checks-text-secondary: #7d8590;--color-checks-text-link: #2f81f7;--color-checks-btn-icon: #7d8590;--color-checks-btn-hover-icon: #e6edf3;--color-checks-btn-hover-bg: rgba(110,118,129,.1);--color-checks-input-text: #7d8590;--color-checks-input-placeholder-text: #6e7681;--color-checks-input-focus-text: #e6edf3;--color-checks-input-bg: #161b22;--color-checks-donut-error: #f85149;--color-checks-donut-pending: #d29922;--color-checks-donut-success: #2ea043;--color-checks-donut-neutral: #8b949e;--color-checks-dropdown-text: #e6edf3;--color-checks-dropdown-bg: #161b22;--color-checks-dropdown-border: #30363d;--color-checks-dropdown-shadow: rgba(1,4,9,.3);--color-checks-dropdown-hover-text: #e6edf3;--color-checks-dropdown-hover-bg: rgba(110,118,129,.1);--color-checks-dropdown-btn-hover-text: #e6edf3;--color-checks-dropdown-btn-hover-bg: rgba(110,118,129,.1);--color-checks-scrollbar-thumb-bg: rgba(110,118,129,.4);--color-checks-header-label-text: #7d8590;--color-checks-header-label-open-text: #e6edf3;--color-checks-header-border: #21262d;--color-checks-header-icon: #7d8590;--color-checks-line-text: #7d8590;--color-checks-line-num-text: #6e7681;--color-checks-line-timestamp-text: #6e7681;--color-checks-line-hover-bg: rgba(110,118,129,.1);--color-checks-line-selected-bg: rgba(56,139,253,.1);--color-checks-line-selected-num-text: #2f81f7;--color-checks-line-dt-fm-text: #ffffff;--color-checks-line-dt-fm-bg: #9e6a03;--color-checks-gate-bg: rgba(187,128,9,.15);--color-checks-gate-text: #7d8590;--color-checks-gate-waiting-text: #d29922;--color-checks-step-header-open-bg: #161b22;--color-checks-step-error-text: #f85149;--color-checks-step-warning-text: #d29922;--color-checks-logline-text: #7d8590;--color-checks-logline-num-text: #6e7681;--color-checks-logline-debug-text: #a371f7;--color-checks-logline-error-text: #7d8590;--color-checks-logline-error-num-text: #6e7681;--color-checks-logline-error-bg: rgba(248,81,73,.1);--color-checks-logline-warning-text: #7d8590;--color-checks-logline-warning-num-text: #d29922;--color-checks-logline-warning-bg: rgba(187,128,9,.15);--color-checks-logline-command-text: #2f81f7;--color-checks-logline-section-text: #3fb950;--color-checks-ansi-black: #0d1117;--color-checks-ansi-black-bright: #161b22;--color-checks-ansi-white: #b1bac4;--color-checks-ansi-white-bright: #b1bac4;--color-checks-ansi-gray: #6e7681;--color-checks-ansi-red: #ff7b72;--color-checks-ansi-red-bright: #ffa198;--color-checks-ansi-green: #3fb950;--color-checks-ansi-green-bright: #56d364;--color-checks-ansi-yellow: #d29922;--color-checks-ansi-yellow-bright: #e3b341;--color-checks-ansi-blue: #58a6ff;--color-checks-ansi-blue-bright: #79c0ff;--color-checks-ansi-magenta: #bc8cff;--color-checks-ansi-magenta-bright: #d2a8ff;--color-checks-ansi-cyan: #76e3ea;--color-checks-ansi-cyan-bright: #b3f0ff;--color-project-header-bg: #0d1117;--color-project-sidebar-bg: #161b22;--color-project-gradient-in: #161b22;--color-project-gradient-out: rgba(22,27,34,0);--color-mktg-btn-bg: #f6f8fa;--color-mktg-btn-shadow-outline: rgb(255 255 255 / 25%) 0 0 0 1px inset;--color-mktg-btn-shadow-focus: rgb(255 255 255 / 25%) 0 0 0 4px;--color-mktg-btn-shadow-hover: 0 4px 7px rgba(0, 0, 0, .15), 0 100px 80px rgba(255, 255, 255, .02), 0 42px 33px rgba(255, 255, 255, .024), 0 22px 18px rgba(255, 255, 255, .028), 0 12px 10px rgba(255, 255, 255, .034), 0 7px 5px rgba(255, 255, 255, .04), 0 3px 2px rgba(255, 255, 255, .07);--color-mktg-btn-shadow-hover-muted: rgb(255 255 255) 0 0 0 2px inset;--color-control-border-color-emphasis: #606771;--color-avatar-bg: rgba(255,255,255,.1);--color-avatar-border: rgba(240,246,252,.1);--color-avatar-stack-fade: #30363d;--color-avatar-stack-fade-more: #21262d;--color-avatar-child-shadow: 0 0 0 2px #0d1117;--color-topic-tag-border: rgba(0,0,0,0);--color-counter-border: rgba(0,0,0,0);--color-select-menu-backdrop-border: #484f58;--color-select-menu-tap-highlight: rgba(48,54,61,.5);--color-select-menu-tap-focus-bg: #0c2d6b;--color-overlay-shadow: 0 0 0 1px #30363d, 0 16px 32px rgba(1,4,9,.85);--color-overlay-backdrop: rgba(22,27,34,.4);--color-header-text: rgba(255,255,255,.7);--color-header-bg: #161b22;--color-header-divider: #8b949e;--color-header-logo: #f0f6fc;--color-header-search-bg: #0d1117;--color-header-search-border: #30363d;--color-sidenav-selected-bg: #21262d;--color-menu-bg-active: #161b22;--color-input-disabled-bg: rgba(110,118,129,0);--color-timeline-badge-bg: #21262d;--color-ansi-black: #484f58;--color-ansi-black-bright: #6e7681;--color-ansi-white: #b1bac4;--color-ansi-white-bright: #ffffff;--color-ansi-gray: #6e7681;--color-ansi-red: #ff7b72;--color-ansi-red-bright: #ffa198;--color-ansi-green: #3fb950;--color-ansi-green-bright: #56d364;--color-ansi-yellow: #d29922;--color-ansi-yellow-bright: #e3b341;--color-ansi-blue: #58a6ff;--color-ansi-blue-bright: #79c0ff;--color-ansi-magenta: #bc8cff;--color-ansi-magenta-bright: #d2a8ff;--color-ansi-cyan: #39c5cf;--color-ansi-cyan-bright: #56d4dd;--color-btn-text: #c9d1d9;--color-btn-bg: #21262d;--color-btn-border: rgba(240,246,252,.1);--color-btn-shadow: 0 0 transparent;--color-btn-inset-shadow: 0 0 transparent;--color-btn-hover-bg: #30363d;--color-btn-hover-border: #8b949e;--color-btn-active-bg: hsla(212,12%,18%,1);--color-btn-active-border: #6e7681;--color-btn-selected-bg: #161b22;--color-btn-counter-bg: #30363d;--color-btn-primary-text: #ffffff;--color-btn-primary-bg: #238636;--color-btn-primary-border: rgba(240,246,252,.1);--color-btn-primary-shadow: 0 0 transparent;--color-btn-primary-inset-shadow: 0 0 transparent;--color-btn-primary-hover-bg: #2ea043;--color-btn-primary-hover-border: rgba(240,246,252,.1);--color-btn-primary-selected-bg: #238636;--color-btn-primary-selected-shadow: 0 0 transparent;--color-btn-primary-disabled-text: rgba(255,255,255,.5);--color-btn-primary-disabled-bg: rgba(35,134,54,.6);--color-btn-primary-disabled-border: rgba(240,246,252,.1);--color-btn-primary-icon: #ffffff;--color-btn-primary-counter-bg: rgba(4,38,15,.2);--color-btn-outline-text: #388bfd;--color-btn-outline-hover-text: #58a6ff;--color-btn-outline-hover-bg: #30363d;--color-btn-outline-hover-border: rgba(240,246,252,.1);--color-btn-outline-hover-shadow: 0 1px 0 rgba(1,4,9,.1);--color-btn-outline-hover-inset-shadow: inset 0 1px 0 rgba(255,255,255,.03);--color-btn-outline-hover-counter-bg: rgba(5,29,77,.2);--color-btn-outline-selected-text: #ffffff;--color-btn-outline-selected-bg: #0d419d;--color-btn-outline-selected-border: rgba(240,246,252,.1);--color-btn-outline-selected-shadow: 0 0 transparent;--color-btn-outline-disabled-text: rgba(88,166,255,.5);--color-btn-outline-disabled-bg: #0d1117;--color-btn-outline-disabled-counter-bg: rgba(31,111,235,.05);--color-btn-outline-counter-bg: rgba(5,29,77,.2);--color-btn-outline-hover-counter-fg: #58a6ff;--color-btn-outline-disabled-counter-fg: rgba(47,129,247,.5);--color-btn-outline-counter-fg: #388bfd;--color-btn-danger-text: #f85149;--color-btn-danger-hover-text: #ffffff;--color-btn-danger-hover-bg: #da3633;--color-btn-danger-hover-border: #f85149;--color-btn-danger-hover-shadow: 0 0 transparent;--color-btn-danger-hover-inset-shadow: 0 0 transparent;--color-btn-danger-hover-icon: #ffffff;--color-btn-danger-hover-counter-bg: rgba(255,255,255,.2);--color-btn-danger-selected-text: #ffffff;--color-btn-danger-selected-bg: #b62324;--color-btn-danger-selected-border: #ff7b72;--color-btn-danger-selected-shadow: 0 0 transparent;--color-btn-danger-disabled-text: rgba(248,81,73,.5);--color-btn-danger-disabled-bg: #0d1117;--color-btn-danger-disabled-counter-bg: rgba(218,54,51,.05);--color-btn-danger-counter-bg: rgba(73,2,2,.2);--color-btn-danger-icon: #f85149;--color-btn-danger-counter-fg: #f85149;--color-btn-danger-disabled-counter-fg: rgba(248,81,73,.5);--color-btn-danger-hover-counter-fg: #ffffff;--color-underlinenav-icon: #6e7681;--color-underlinenav-border-hover: rgba(110,118,129,.4);--color-action-list-item-inline-divider: rgba(48,54,61,.48);--color-action-list-item-default-hover-bg: rgba(177,186,196,.12);--color-action-list-item-default-hover-border: rgba(0,0,0,0);--color-action-list-item-default-active-bg: rgba(177,186,196,.2);--color-action-list-item-default-active-border: rgba(0,0,0,0);--color-action-list-item-default-selected-bg: rgba(177,186,196,.08);--color-action-list-item-danger-hover-bg: rgba(248,81,73,.16);--color-action-list-item-danger-active-bg: rgba(248,81,73,.24);--color-action-list-item-danger-hover-text: #ff7b72;--color-switch-track-bg: rgba(110,118,129,.1);--color-switch-track-hover-bg: hsla(215,8%,72%,.1);--color-switch-track-active-bg: rgba(110,118,129,.4);--color-switch-track-disabled-bg: #21262d;--color-switch-track-fg: #7d8590;--color-switch-track-disabled-fg: #010409;--color-switch-track-border: rgba(0,0,0,0);--color-switch-track-checked-bg: rgba(31,111,235,.35);--color-switch-track-checked-hover-bg: rgba(31,111,235,.5);--color-switch-track-checked-active-bg: rgba(31,111,235,.65);--color-switch-track-checked-fg: #ffffff;--color-switch-track-checked-disabled-fg: #010409;--color-switch-track-checked-border: rgba(0,0,0,0);--color-switch-knob-bg: #0d1117;--color-switch-knob-border: #606771;--color-switch-knob-disabled-bg: #161b22;--color-switch-knob-checked-bg: #0d1117;--color-switch-knob-checked-disabled-bg: #161b22;--color-switch-knob-checked-border: rgba(31,111,235,.35);--color-segmented-control-bg: rgba(110,118,129,.1);--color-segmented-control-button-bg: #0d1117;--color-segmented-control-button-hover-bg: #30363d;--color-segmented-control-button-active-bg: #21262d;--color-segmented-control-button-selected-border: #6e7681;--color-tree-view-item-chevron-hover-bg: rgba(177,186,196,.12);--color-tree-view-item-directory-fill: #7d8590;--color-fg-default: #e6edf3;--color-fg-muted: #7d8590;--color-fg-subtle: #6e7681;--color-fg-on-emphasis: #ffffff;--color-canvas-default: #0d1117;--color-canvas-overlay: #161b22;--color-canvas-inset: #010409;--color-canvas-subtle: #161b22;--color-border-default: #30363d;--color-border-muted: #21262d;--color-border-subtle: rgba(240,246,252,.1);--color-shadow-small: 0 0 transparent;--color-shadow-medium: 0 3px 6px #010409;--color-shadow-large: 0 8px 24px #010409;--color-shadow-extra-large: 0 12px 48px #010409;--color-neutral-emphasis-plus: #6e7681;--color-neutral-emphasis: #6e7681;--color-neutral-muted: rgba(110,118,129,.4);--color-neutral-subtle: rgba(110,118,129,.1);--color-accent-fg: #2f81f7;--color-accent-emphasis: #1f6feb;--color-accent-muted: rgba(56,139,253,.4);--color-accent-subtle: rgba(56,139,253,.1);--color-success-fg: #3fb950;--color-success-emphasis: #238636;--color-success-muted: rgba(46,160,67,.4);--color-success-subtle: rgba(46,160,67,.15);--color-attention-fg: #d29922;--color-attention-emphasis: #9e6a03;--color-attention-muted: rgba(187,128,9,.4);--color-attention-subtle: rgba(187,128,9,.15);--color-severe-fg: #db6d28;--color-severe-emphasis: #bd561d;--color-severe-muted: rgba(219,109,40,.4);--color-severe-subtle: rgba(219,109,40,.1);--color-danger-fg: #f85149;--color-danger-emphasis: #da3633;--color-danger-muted: rgba(248,81,73,.4);--color-danger-subtle: rgba(248,81,73,.1);--color-open-fg: #3fb950;--color-open-emphasis: #238636;--color-open-muted: rgba(46,160,67,.4);--color-open-subtle: rgba(46,160,67,.15);--color-closed-fg: #f85149;--color-closed-emphasis: #da3633;--color-closed-muted: rgba(248,81,73,.4);--color-closed-subtle: rgba(248,81,73,.15);--color-done-fg: #a371f7;--color-done-emphasis: #8957e5;--color-done-muted: rgba(163,113,247,.4);--color-done-subtle: rgba(163,113,247,.1);--color-sponsors-fg: #db61a2;--color-sponsors-emphasis: #bf4b8a;--color-sponsors-muted: rgba(219,97,162,.4);--color-sponsors-subtle: rgba(219,97,162,.1);--color-primer-fg-disabled: #484f58;--color-primer-canvas-backdrop: rgba(1,4,9,.8);--color-primer-canvas-sticky: rgba(13,17,23,.95);--color-primer-border-active: #f78166;--color-primer-border-contrast: rgba(255,255,255,.2);--color-primer-shadow-highlight: 0 0 transparent;--color-primer-shadow-inset: 0 0 transparent;--color-scale-black: #010409;--color-scale-white: #ffffff;--color-scale-gray-0: #f0f6fc;--color-scale-gray-1: #c9d1d9;--color-scale-gray-2: #b1bac4;--color-scale-gray-3: #8b949e;--color-scale-gray-4: #6e7681;--color-scale-gray-5: #484f58;--color-scale-gray-6: #30363d;--color-scale-gray-7: #21262d;--color-scale-gray-8: #161b22;--color-scale-gray-9: #0d1117;--color-scale-blue-0: #cae8ff;--color-scale-blue-1: #a5d6ff;--color-scale-blue-2: #79c0ff;--color-scale-blue-3: #58a6ff;--color-scale-blue-4: #388bfd;--color-scale-blue-5: #1f6feb;--color-scale-blue-6: #1158c7;--color-scale-blue-7: #0d419d;--color-scale-blue-8: #0c2d6b;--color-scale-blue-9: #051d4d;--color-scale-green-0: #aff5b4;--color-scale-green-1: #7ee787;--color-scale-green-2: #56d364;--color-scale-green-3: #3fb950;--color-scale-green-4: #2ea043;--color-scale-green-5: #238636;--color-scale-green-6: #196c2e;--color-scale-green-7: #0f5323;--color-scale-green-8: #033a16;--color-scale-green-9: #04260f;--color-scale-yellow-0: #f8e3a1;--color-scale-yellow-1: #f2cc60;--color-scale-yellow-2: #e3b341;--color-scale-yellow-3: #d29922;--color-scale-yellow-4: #bb8009;--color-scale-yellow-5: #9e6a03;--color-scale-yellow-6: #845306;--color-scale-yellow-7: #693e00;--color-scale-yellow-8: #4b2900;--color-scale-yellow-9: #341a00;--color-scale-orange-0: #ffdfb6;--color-scale-orange-1: #ffc680;--color-scale-orange-2: #ffa657;--color-scale-orange-3: #f0883e;--color-scale-orange-4: #db6d28;--color-scale-orange-5: #bd561d;--color-scale-orange-6: #9b4215;--color-scale-orange-7: #762d0a;--color-scale-orange-8: #5a1e02;--color-scale-orange-9: #3d1300;--color-scale-red-0: #ffdcd7;--color-scale-red-1: #ffc1ba;--color-scale-red-2: #ffa198;--color-scale-red-3: #ff7b72;--color-scale-red-4: #f85149;--color-scale-red-5: #da3633;--color-scale-red-6: #b62324;--color-scale-red-7: #8e1519;--color-scale-red-8: #67060c;--color-scale-red-9: #490202;--color-scale-purple-0: #eddeff;--color-scale-purple-1: #e2c5ff;--color-scale-purple-2: #d2a8ff;--color-scale-purple-3: #bc8cff;--color-scale-purple-4: #a371f7;--color-scale-purple-5: #8957e5;--color-scale-purple-6: #6e40c9;--color-scale-purple-7: #553098;--color-scale-purple-8: #3c1e70;--color-scale-purple-9: #271052;--color-scale-pink-0: #ffdaec;--color-scale-pink-1: #ffbedd;--color-scale-pink-2: #ff9bce;--color-scale-pink-3: #f778ba;--color-scale-pink-4: #db61a2;--color-scale-pink-5: #bf4b8a;--color-scale-pink-6: #9e3670;--color-scale-pink-7: #7d2457;--color-scale-pink-8: #5e103e;--color-scale-pink-9: #42062a;--color-scale-coral-0: #ffddd2;--color-scale-coral-1: #ffc2b2;--color-scale-coral-2: #ffa28b;--color-scale-coral-3: #f78166;--color-scale-coral-4: #ea6045;--color-scale-coral-5: #cf462d;--color-scale-coral-6: #ac3220;--color-scale-coral-7: #872012;--color-scale-coral-8: #640d04;--color-scale-coral-9: #460701}.code-diff-view[theme=dark] pre code.hljs{display:block;overflow-x:auto;padding:1em}.code-diff-view[theme=dark] code.hljs{padding:3px 5px}.code-diff-view[theme=dark] .hljs{color:#c9d1d9;background:#0d1117}.code-diff-view[theme=dark] .hljs-doctag,.code-diff-view[theme=dark] .hljs-keyword,.code-diff-view[theme=dark] .hljs-meta .hljs-keyword,.code-diff-view[theme=dark] .hljs-template-tag,.code-diff-view[theme=dark] .hljs-template-variable,.code-diff-view[theme=dark] .hljs-type,.code-diff-view[theme=dark] .hljs-variable.language_{color:#ff7b72}.code-diff-view[theme=dark] .hljs-title,.code-diff-view[theme=dark] .hljs-title.class_,.code-diff-view[theme=dark] .hljs-title.class_.inherited__,.code-diff-view[theme=dark] .hljs-title.function_{color:#d2a8ff}.code-diff-view[theme=dark] .hljs-attr,.code-diff-view[theme=dark] .hljs-attribute,.code-diff-view[theme=dark] .hljs-literal,.code-diff-view[theme=dark] .hljs-meta,.code-diff-view[theme=dark] .hljs-number,.code-diff-view[theme=dark] .hljs-operator,.code-diff-view[theme=dark] .hljs-variable,.code-diff-view[theme=dark] .hljs-selector-attr,.code-diff-view[theme=dark] .hljs-selector-class,.code-diff-view[theme=dark] .hljs-selector-id{color:#79c0ff}.code-diff-view[theme=dark] .hljs-regexp,.code-diff-view[theme=dark] .hljs-string,.code-diff-view[theme=dark] .hljs-meta .hljs-string{color:#a5d6ff}.code-diff-view[theme=dark] .hljs-built_in,.code-diff-view[theme=dark] .hljs-symbol{color:#ffa657}.code-diff-view[theme=dark] .hljs-comment,.code-diff-view[theme=dark] .hljs-code,.code-diff-view[theme=dark] .hljs-formula{color:#8b949e}.code-diff-view[theme=dark] .hljs-name,.code-diff-view[theme=dark] .hljs-quote,.code-diff-view[theme=dark] .hljs-selector-tag,.code-diff-view[theme=dark] .hljs-selector-pseudo{color:#7ee787}.code-diff-view[theme=dark] .hljs-subst{color:#c9d1d9}.code-diff-view[theme=dark] .hljs-section{color:#1f6feb;font-weight:700}.code-diff-view[theme=dark] .hljs-bullet{color:#f2cc60}.code-diff-view[theme=dark] .hljs-emphasis{color:#c9d1d9;font-style:italic}.code-diff-view[theme=dark] .hljs-strong{color:#c9d1d9;font-weight:700}.code-diff-view[theme=dark] .hljs-addition{color:#aff5b4;background-color:#033a16}.code-diff-view[theme=dark] .hljs-deletion{color:#ffdcd7;background-color:#67060c}.code-diff-view{position:relative;margin-top:16px;margin-bottom:16px;border:1px solid var(--color-border-default, #ddd);border-radius:6px;overflow-y:auto}.code-diff-view *{position:static;box-sizing:border-box}.code-diff-view .file-header{position:sticky;top:0;z-index:1;background-color:var(--color-canvas-subtle);border-bottom:1px solid var(--color-border-default);padding:8px 16px;font-size:12px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace}.code-diff-view .file-header .file-info{display:flex;justify-content:space-between;align-items:center;margin-left:8px;height:24px}.code-diff-view .file-header .file-info .info-left{color:var(--color-fg-default)}.code-diff-view .file-header .file-info .info-right{display:flex;justify-content:space-between;align-items:center;width:50%}.code-diff-view .file-header .file-info .diff-stat{display:inline-flex;align-items:center;gap:8px}.code-diff-view .file-header .file-info .diff-stat .diff-stat-added{color:var(--color-diffstat-addition-bg)}.code-diff-view .file-header .file-info .diff-stat .diff-stat-deleted{color:var(--color-danger-emphasis)}.code-diff-view .file-header .file-info .diff-stat .diff-stat-ignored{color:var(--color-fg-subtle)}.code-diff-view .file-header .file-info .diff-commandbar{margin-left:auto;margin-right:1rem}.code-diff-view .file-header .file-info .diff-commandbar .command-item-button{background-color:transparent;color:var(--color-fg-subtle);border:none}.code-diff-view .file-header .file-info .diff-commandbar .command-item-button svg{fill:var(--color-fg-subtle)}.code-diff-view .file-header .file-info .diff-commandbar .command-item-button:hover{background-color:var(--color-btn-outline-hover-border)}.code-diff-view table{border-spacing:0}.code-diff-view .diff-table{width:100%}.code-diff-view .diff-table .blob-num{position:relative;width:1%;min-width:50px;padding-right:10px;padding-left:10px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;line-height:20px;color:var(--color-fg-subtle);text-align:right;white-space:nowrap;vertical-align:top;cursor:pointer;-webkit-user-select:none;user-select:none}.code-diff-view .diff-table .blob-num-deletion{color:var(--color-diff-blob-deletion-num-text);background-color:var(--color-diff-blob-deletion-num-bg);border-color:var(--color-danger-emphasis)}.code-diff-view .diff-table .blob-num-addition{color:var(--color-diff-blob-addition-num-text);background-color:var(--color-diff-blob-addition-num-bg);border-color:var(--color-success-emphasis)}.code-diff-view .diff-table .blob-code{position:relative;padding-right:10px;padding-left:10px;line-height:20px;vertical-align:top}.code-diff-view .diff-table .blob-code .blob-code-inner{display:table-cell;overflow:visible;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;color:var(--color-fg-default);word-wrap:anywhere;white-space:pre-wrap}.code-diff-view .diff-table .blob-code-deletion{background-color:var(--color-diff-blob-deletion-line-bg);outline:1px dashed transparent}.code-diff-view .diff-table .blob-code-deletion .x{color:var(--color-diff-blob-deletion-fg);background-color:var(--color-diff-blob-deletion-word-bg)}.code-diff-view .diff-table .blob-code-addition{background-color:var(--color-diff-blob-addition-line-bg);outline:1px dotted transparent}.code-diff-view .diff-table .blob-code-addition .x{color:var(--color-diff-blob-addition-fg);background-color:var(--color-diff-blob-addition-word-bg)}.code-diff-view .diff-table .current-diff{border:1px solid var(--color-border-muted)}.code-diff-view .diff-table .blob-code-context,.code-diff-view .diff-table .blob-code-addition,.code-diff-view .diff-table .blob-code-deletion{padding-left:22px!important}.code-diff-view .diff-table .blob-code-marker:before{position:absolute;top:1px;left:8px;padding-right:8px;content:attr(data-code-marker)}.code-diff-view .diff-table .blob-num-hunk{background-color:var(--color-diff-blob-hunk-num-bg)}.code-diff-view .diff-table .blob-code-hunk{background-color:var(--color-accent-subtle)}.code-diff-view .file-diff-split{table-layout:fixed}.code-diff-view .file-diff-split .blob-code+.blob-num{border-left:1px solid var(--color-border-muted)}.code-diff-view .file-diff-split .no-select{user-select:none}.code-diff-view .empty-cell{cursor:default;background-color:var(--color-neutral-subtle);border-right-color:var(--color-border-muted)}.code-diff-view[data-v-0c5adc58] td:nth-child(1){display:none}")),document.head.appendChild(e)}}catch(r){console.error("vite-plugin-css-injected-by-js",r)}})();function le(){}le.prototype={diff:function(e,r){var c,f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},g=f.callback;typeof f=="function"&&(g=f,f={}),this.options=f;var t=this;function o(v){return g?(setTimeout(function(){g(void 0,v)},0),!0):v}e=this.castInput(e),r=this.castInput(r),e=this.removeEmpty(this.tokenize(e)),r=this.removeEmpty(this.tokenize(r));var n=r.length,a=e.length,i=1,l=n+a;f.maxEditLength&&(l=Math.min(l,f.maxEditLength));var s=(c=f.timeout)!==null&&c!==void 0?c:1/0,d=Date.now()+s,b=[{oldPos:-1,lastComponent:void 0}],h=this.extractCommon(b[0],r,e,0);if(b[0].oldPos+1>=a&&h+1>=n)return o([{value:this.join(r),count:r.length}]);var u=-1/0,m=1/0;function y(){for(var v=Math.max(u,-i);v<=Math.min(m,i);v+=2){var k=void 0,N=b[v-1],j=b[v+1];N&&(b[v-1]=void 0);var E=!1;if(j){var S=j.oldPos-v;E=j&&0<=S&&S<n}var D=N&&N.oldPos+1<a;if(!E&&!D){b[v]=void 0;continue}if(!D||E&&N.oldPos+1<j.oldPos?k=t.addToPath(j,!0,void 0,0):k=t.addToPath(N,void 0,!0,1),h=t.extractCommon(k,r,e,v),k.oldPos+1>=a&&h+1>=n)return o(Gt(t,k.lastComponent,r,e,t.useLongestToken));b[v]=k,k.oldPos+1>=a&&(m=Math.min(m,v-1)),h+1>=n&&(u=Math.max(u,v+1))}i++}if(g)(function v(){setTimeout(function(){if(i>l||Date.now()>d)return g();y()||v()},0)})();else for(;i<=l&&Date.now()<=d;){var w=y();if(w)return w}},addToPath:function(e,r,c,f){var g=e.lastComponent;return g&&g.added===r&&g.removed===c?{oldPos:e.oldPos+f,lastComponent:{count:g.count+1,added:r,removed:c,previousComponent:g.previousComponent}}:{oldPos:e.oldPos+f,lastComponent:{count:1,added:r,removed:c,previousComponent:g}}},extractCommon:function(e,r,c,f){for(var g=r.length,t=c.length,o=e.oldPos,n=o-f,a=0;n+1<g&&o+1<t&&this.equals(r[n+1],c[o+1]);)n++,o++,a++;return a&&(e.lastComponent={count:a,previousComponent:e.lastComponent}),e.oldPos=o,n},equals:function(e,r){return this.options.comparator?this.options.comparator(e,r):e===r||this.options.ignoreCase&&e.toLowerCase()===r.toLowerCase()},removeEmpty:function(e){for(var r=[],c=0;c<e.length;c++)e[c]&&r.push(e[c]);return r},castInput:function(e){return e},tokenize:function(e){return e.split("")},join:function(e){return e.join("")}};function Gt(e,r,c,f,g){for(var t=[],o;r;)t.push(r),o=r.previousComponent,delete r.previousComponent,r=o;t.reverse();for(var n=0,a=t.length,i=0,l=0;n<a;n++){var s=t[n];if(s.removed){if(s.value=e.join(f.slice(l,l+s.count)),l+=s.count,n&&t[n-1].added){var d=t[n-1];t[n-1]=t[n],t[n]=d}}else{if(!s.added&&g){var b=c.slice(i,i+s.count);b=b.map(function(u,m){var y=f[l+m];return y.length>u.length?y:u}),s.value=e.join(b)}else s.value=e.join(c.slice(i,i+s.count));i+=s.count,s.added||(l+=s.count)}}var h=t[a-1];return a>1&&typeof h.value=="string"&&(h.added||h.removed)&&e.equals("",h.value)&&(t[a-2].value+=h.value,t.pop()),t}var Kt=new le;function Qt(e,r,c){return Kt.diff(e,r,c)}function Xt(e,r){if(typeof e=="function")r.callback=e;else if(e)for(var c in e)e.hasOwnProperty(c)&&(r[c]=e[c]);return r}var rt=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,nt=/\S/,Ze=new le;Ze.equals=function(e,r){return this.options.ignoreCase&&(e=e.toLowerCase(),r=r.toLowerCase()),e===r||this.options.ignoreWhitespace&&!nt.test(e)&&!nt.test(r)};Ze.tokenize=function(e){for(var r=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),c=0;c<r.length-1;c++)!r[c+1]&&r[c+2]&&rt.test(r[c])&&rt.test(r[c+2])&&(r[c]+=r[c+2],r.splice(c+1,2),c--);return r};function Wt(e,r,c){return c=Xt(c,{ignoreWhitespace:!0}),Ze.diff(e,r,c)}var pt=new le;pt.tokenize=function(e){this.options.stripTrailingCr&&(e=e.replace(/\r\n/g,`
`));var r=[],c=e.split(/(\n|\r\n)/);c[c.length-1]||c.pop();for(var f=0;f<c.length;f++){var g=c[f];f%2&&!this.options.newlineIsToken?r[r.length-1]+=g:(this.options.ignoreWhitespace&&(g=g.trim()),r.push(g))}return r};var Jt=new le;Jt.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)};var Vt=new le;Vt.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};function Ce(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ce=function(r){return typeof r}:Ce=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Ce(e)}var Yt=Object.prototype.toString,xe=new le;xe.useLongestToken=!0;xe.tokenize=pt.tokenize;xe.castInput=function(e){var r=this.options,c=r.undefinedReplacement,f=r.stringifyReplacer,g=f===void 0?function(t,o){return typeof o>"u"?c:o}:f;return typeof e=="string"?e:JSON.stringify(Fe(e,null,null,g),g,"  ")};xe.equals=function(e,r){return le.prototype.equals.call(xe,e.replace(/,([\r\n])/g,"$1"),r.replace(/,([\r\n])/g,"$1"))};function Fe(e,r,c,f,g){r=r||[],c=c||[],f&&(e=f(g,e));var t;for(t=0;t<r.length;t+=1)if(r[t]===e)return c[t];var o;if(Yt.call(e)==="[object Array]"){for(r.push(e),o=new Array(e.length),c.push(o),t=0;t<e.length;t+=1)o[t]=Fe(e[t],r,c,f,g);return r.pop(),c.pop(),o}if(e&&e.toJSON&&(e=e.toJSON()),Ce(e)==="object"&&e!==null){r.push(e),o={},c.push(o);var n=[],a;for(a in e)e.hasOwnProperty(a)&&n.push(a);for(n.sort(),t=0;t<n.length;t+=1)a=n[t],o[a]=Fe(e[a],r,c,f,a);r.pop(),c.pop()}else o=e;return o}var He=new le;He.tokenize=function(e){return e.slice()};He.join=He.removeEmpty=function(e){return e};function eo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var mt={exports:{}};(function(e){var r=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32},c=-1,f=1,g=0;r.Diff=function(t,o){return[t,o]},r.prototype.diff_main=function(t,o,n,a){typeof a>"u"&&(this.Diff_Timeout<=0?a=Number.MAX_VALUE:a=new Date().getTime()+this.Diff_Timeout*1e3);var i=a;if(t==null||o==null)throw new Error("Null input. (diff_main)");if(t==o)return t?[new r.Diff(g,t)]:[];typeof n>"u"&&(n=!0);var l=n,s=this.diff_commonPrefix(t,o),d=t.substring(0,s);t=t.substring(s),o=o.substring(s),s=this.diff_commonSuffix(t,o);var b=t.substring(t.length-s);t=t.substring(0,t.length-s),o=o.substring(0,o.length-s);var h=this.diff_compute_(t,o,l,i);return d&&h.unshift(new r.Diff(g,d)),b&&h.push(new r.Diff(g,b)),this.diff_cleanupMerge(h),h},r.prototype.diff_compute_=function(t,o,n,a){var i;if(!t)return[new r.Diff(f,o)];if(!o)return[new r.Diff(c,t)];var l=t.length>o.length?t:o,s=t.length>o.length?o:t,d=l.indexOf(s);if(d!=-1)return i=[new r.Diff(f,l.substring(0,d)),new r.Diff(g,s),new r.Diff(f,l.substring(d+s.length))],t.length>o.length&&(i[0][0]=i[2][0]=c),i;if(s.length==1)return[new r.Diff(c,t),new r.Diff(f,o)];var b=this.diff_halfMatch_(t,o);if(b){var h=b[0],u=b[1],m=b[2],y=b[3],w=b[4],v=this.diff_main(h,m,n,a),k=this.diff_main(u,y,n,a);return v.concat([new r.Diff(g,w)],k)}return n&&t.length>100&&o.length>100?this.diff_lineMode_(t,o,a):this.diff_bisect_(t,o,a)},r.prototype.diff_lineMode_=function(t,o,n){var a=this.diff_linesToChars_(t,o);t=a.chars1,o=a.chars2;var i=a.lineArray,l=this.diff_main(t,o,!1,n);this.diff_charsToLines_(l,i),this.diff_cleanupSemantic(l),l.push(new r.Diff(g,""));for(var s=0,d=0,b=0,h="",u="";s<l.length;){switch(l[s][0]){case f:b++,u+=l[s][1];break;case c:d++,h+=l[s][1];break;case g:if(d>=1&&b>=1){l.splice(s-d-b,d+b),s=s-d-b;for(var m=this.diff_main(h,u,!1,n),y=m.length-1;y>=0;y--)l.splice(s,0,m[y]);s=s+m.length}b=0,d=0,h="",u="";break}s++}return l.pop(),l},r.prototype.diff_bisect_=function(t,o,n){for(var a=t.length,i=o.length,l=Math.ceil((a+i)/2),s=l,d=2*l,b=new Array(d),h=new Array(d),u=0;u<d;u++)b[u]=-1,h[u]=-1;b[s+1]=0,h[s+1]=0;for(var m=a-i,y=m%2!=0,w=0,v=0,k=0,N=0,j=0;j<l&&!(new Date().getTime()>n);j++){for(var E=-j+w;E<=j-v;E+=2){var S=s+E,D;E==-j||E!=j&&b[S-1]<b[S+1]?D=b[S+1]:D=b[S-1]+1;for(var O=D-E;D<a&&O<i&&t.charAt(D)==o.charAt(O);)D++,O++;if(b[S]=D,D>a)v+=2;else if(O>i)w+=2;else if(y){var $=s+m-E;if($>=0&&$<d&&h[$]!=-1){var I=a-h[$];if(D>=I)return this.diff_bisectSplit_(t,o,D,O,n)}}}for(var q=-j+k;q<=j-N;q+=2){var $=s+q,I;q==-j||q!=j&&h[$-1]<h[$+1]?I=h[$+1]:I=h[$-1]+1;for(var Q=I-q;I<a&&Q<i&&t.charAt(a-I-1)==o.charAt(i-Q-1);)I++,Q++;if(h[$]=I,I>a)N+=2;else if(Q>i)k+=2;else if(!y){var S=s+m-q;if(S>=0&&S<d&&b[S]!=-1){var D=b[S],O=s+D-S;if(I=a-I,D>=I)return this.diff_bisectSplit_(t,o,D,O,n)}}}}return[new r.Diff(c,t),new r.Diff(f,o)]},r.prototype.diff_bisectSplit_=function(t,o,n,a,i){var l=t.substring(0,n),s=o.substring(0,a),d=t.substring(n),b=o.substring(a),h=this.diff_main(l,s,!1,i),u=this.diff_main(d,b,!1,i);return h.concat(u)},r.prototype.diff_linesToChars_=function(t,o){var n=[],a={};n[0]="";function i(b){for(var h="",u=0,m=-1,y=n.length;m<b.length-1;){m=b.indexOf(`
`,u),m==-1&&(m=b.length-1);var w=b.substring(u,m+1);(a.hasOwnProperty?a.hasOwnProperty(w):a[w]!==void 0)?h+=String.fromCharCode(a[w]):(y==l&&(w=b.substring(u),m=b.length),h+=String.fromCharCode(y),a[w]=y,n[y++]=w),u=m+1}return h}var l=4e4,s=i(t);l=65535;var d=i(o);return{chars1:s,chars2:d,lineArray:n}},r.prototype.diff_charsToLines_=function(t,o){for(var n=0;n<t.length;n++){for(var a=t[n][1],i=[],l=0;l<a.length;l++)i[l]=o[a.charCodeAt(l)];t[n][1]=i.join("")}},r.prototype.diff_commonPrefix=function(t,o){if(!t||!o||t.charAt(0)!=o.charAt(0))return 0;for(var n=0,a=Math.min(t.length,o.length),i=a,l=0;n<i;)t.substring(l,i)==o.substring(l,i)?(n=i,l=n):a=i,i=Math.floor((a-n)/2+n);return i},r.prototype.diff_commonSuffix=function(t,o){if(!t||!o||t.charAt(t.length-1)!=o.charAt(o.length-1))return 0;for(var n=0,a=Math.min(t.length,o.length),i=a,l=0;n<i;)t.substring(t.length-i,t.length-l)==o.substring(o.length-i,o.length-l)?(n=i,l=n):a=i,i=Math.floor((a-n)/2+n);return i},r.prototype.diff_commonOverlap_=function(t,o){var n=t.length,a=o.length;if(n==0||a==0)return 0;n>a?t=t.substring(n-a):n<a&&(o=o.substring(0,n));var i=Math.min(n,a);if(t==o)return i;for(var l=0,s=1;;){var d=t.substring(i-s),b=o.indexOf(d);if(b==-1)return l;s+=b,(b==0||t.substring(i-s)==o.substring(0,s))&&(l=s,s++)}},r.prototype.diff_halfMatch_=function(t,o){if(this.Diff_Timeout<=0)return null;var n=t.length>o.length?t:o,a=t.length>o.length?o:t;if(n.length<4||a.length*2<n.length)return null;var i=this;function l(v,k,N){for(var j=v.substring(N,N+Math.floor(v.length/4)),E=-1,S="",D,O,$,I;(E=k.indexOf(j,E+1))!=-1;){var q=i.diff_commonPrefix(v.substring(N),k.substring(E)),Q=i.diff_commonSuffix(v.substring(0,N),k.substring(0,E));S.length<Q+q&&(S=k.substring(E-Q,E)+k.substring(E,E+q),D=v.substring(0,N-Q),O=v.substring(N+q),$=k.substring(0,E-Q),I=k.substring(E+q))}return S.length*2>=v.length?[D,O,$,I,S]:null}var s=l(n,a,Math.ceil(n.length/4)),d=l(n,a,Math.ceil(n.length/2)),b;if(!s&&!d)return null;d?s?b=s[4].length>d[4].length?s:d:b=d:b=s;var h,u,m,y;t.length>o.length?(h=b[0],u=b[1],m=b[2],y=b[3]):(m=b[0],y=b[1],h=b[2],u=b[3]);var w=b[4];return[h,u,m,y,w]},r.prototype.diff_cleanupSemantic=function(t){for(var o=!1,n=[],a=0,i=null,l=0,s=0,d=0,b=0,h=0;l<t.length;)t[l][0]==g?(n[a++]=l,s=b,d=h,b=0,h=0,i=t[l][1]):(t[l][0]==f?b+=t[l][1].length:h+=t[l][1].length,i&&i.length<=Math.max(s,d)&&i.length<=Math.max(b,h)&&(t.splice(n[a-1],0,new r.Diff(c,i)),t[n[a-1]+1][0]=f,a--,a--,l=a>0?n[a-1]:-1,s=0,d=0,b=0,h=0,i=null,o=!0)),l++;for(o&&this.diff_cleanupMerge(t),this.diff_cleanupSemanticLossless(t),l=1;l<t.length;){if(t[l-1][0]==c&&t[l][0]==f){var u=t[l-1][1],m=t[l][1],y=this.diff_commonOverlap_(u,m),w=this.diff_commonOverlap_(m,u);y>=w?(y>=u.length/2||y>=m.length/2)&&(t.splice(l,0,new r.Diff(g,m.substring(0,y))),t[l-1][1]=u.substring(0,u.length-y),t[l+1][1]=m.substring(y),l++):(w>=u.length/2||w>=m.length/2)&&(t.splice(l,0,new r.Diff(g,u.substring(0,w))),t[l-1][0]=f,t[l-1][1]=m.substring(0,m.length-w),t[l+1][0]=c,t[l+1][1]=u.substring(w),l++),l++}l++}},r.prototype.diff_cleanupSemanticLossless=function(t){function o(w,v){if(!w||!v)return 6;var k=w.charAt(w.length-1),N=v.charAt(0),j=k.match(r.nonAlphaNumericRegex_),E=N.match(r.nonAlphaNumericRegex_),S=j&&k.match(r.whitespaceRegex_),D=E&&N.match(r.whitespaceRegex_),O=S&&k.match(r.linebreakRegex_),$=D&&N.match(r.linebreakRegex_),I=O&&w.match(r.blanklineEndRegex_),q=$&&v.match(r.blanklineStartRegex_);return I||q?5:O||$?4:j&&!S&&D?3:S||D?2:j||E?1:0}for(var n=1;n<t.length-1;){if(t[n-1][0]==g&&t[n+1][0]==g){var a=t[n-1][1],i=t[n][1],l=t[n+1][1],s=this.diff_commonSuffix(a,i);if(s){var d=i.substring(i.length-s);a=a.substring(0,a.length-s),i=d+i.substring(0,i.length-s),l=d+l}for(var b=a,h=i,u=l,m=o(a,i)+o(i,l);i.charAt(0)===l.charAt(0);){a+=i.charAt(0),i=i.substring(1)+l.charAt(0),l=l.substring(1);var y=o(a,i)+o(i,l);y>=m&&(m=y,b=a,h=i,u=l)}t[n-1][1]!=b&&(b?t[n-1][1]=b:(t.splice(n-1,1),n--),t[n][1]=h,u?t[n+1][1]=u:(t.splice(n+1,1),n--))}n++}},r.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,r.whitespaceRegex_=/\s/,r.linebreakRegex_=/[\r\n]/,r.blanklineEndRegex_=/\n\r?\n$/,r.blanklineStartRegex_=/^\r?\n\r?\n/,r.prototype.diff_cleanupEfficiency=function(t){for(var o=!1,n=[],a=0,i=null,l=0,s=!1,d=!1,b=!1,h=!1;l<t.length;)t[l][0]==g?(t[l][1].length<this.Diff_EditCost&&(b||h)?(n[a++]=l,s=b,d=h,i=t[l][1]):(a=0,i=null),b=h=!1):(t[l][0]==c?h=!0:b=!0,i&&(s&&d&&b&&h||i.length<this.Diff_EditCost/2&&s+d+b+h==3)&&(t.splice(n[a-1],0,new r.Diff(c,i)),t[n[a-1]+1][0]=f,a--,i=null,s&&d?(b=h=!0,a=0):(a--,l=a>0?n[a-1]:-1,b=h=!1),o=!0)),l++;o&&this.diff_cleanupMerge(t)},r.prototype.diff_cleanupMerge=function(t){t.push(new r.Diff(g,""));for(var o=0,n=0,a=0,i="",l="",s;o<t.length;)switch(t[o][0]){case f:a++,l+=t[o][1],o++;break;case c:n++,i+=t[o][1],o++;break;case g:n+a>1?(n!==0&&a!==0&&(s=this.diff_commonPrefix(l,i),s!==0&&(o-n-a>0&&t[o-n-a-1][0]==g?t[o-n-a-1][1]+=l.substring(0,s):(t.splice(0,0,new r.Diff(g,l.substring(0,s))),o++),l=l.substring(s),i=i.substring(s)),s=this.diff_commonSuffix(l,i),s!==0&&(t[o][1]=l.substring(l.length-s)+t[o][1],l=l.substring(0,l.length-s),i=i.substring(0,i.length-s))),o-=n+a,t.splice(o,n+a),i.length&&(t.splice(o,0,new r.Diff(c,i)),o++),l.length&&(t.splice(o,0,new r.Diff(f,l)),o++),o++):o!==0&&t[o-1][0]==g?(t[o-1][1]+=t[o][1],t.splice(o,1)):o++,a=0,n=0,i="",l="";break}t[t.length-1][1]===""&&t.pop();var d=!1;for(o=1;o<t.length-1;)t[o-1][0]==g&&t[o+1][0]==g&&(t[o][1].substring(t[o][1].length-t[o-1][1].length)==t[o-1][1]?(t[o][1]=t[o-1][1]+t[o][1].substring(0,t[o][1].length-t[o-1][1].length),t[o+1][1]=t[o-1][1]+t[o+1][1],t.splice(o-1,1),d=!0):t[o][1].substring(0,t[o+1][1].length)==t[o+1][1]&&(t[o-1][1]+=t[o+1][1],t[o][1]=t[o][1].substring(t[o+1][1].length)+t[o+1][1],t.splice(o+1,1),d=!0)),o++;d&&this.diff_cleanupMerge(t)},r.prototype.diff_xIndex=function(t,o){var n=0,a=0,i=0,l=0,s;for(s=0;s<t.length&&(t[s][0]!==f&&(n+=t[s][1].length),t[s][0]!==c&&(a+=t[s][1].length),!(n>o));s++)i=n,l=a;return t.length!=s&&t[s][0]===c?l:l+(o-i)},r.prototype.diff_prettyHtml=function(t){for(var o=[],n=/&/g,a=/</g,i=/>/g,l=/\n/g,s=0;s<t.length;s++){var d=t[s][0],b=t[s][1],h=b.replace(n,"&amp;").replace(a,"&lt;").replace(i,"&gt;").replace(l,"&para;<br>");switch(d){case f:o[s]='<ins style="background:#e6ffe6;">'+h+"</ins>";break;case c:o[s]='<del style="background:#ffe6e6;">'+h+"</del>";break;case g:o[s]="<span>"+h+"</span>";break}}return o.join("")},r.prototype.diff_text1=function(t){for(var o=[],n=0;n<t.length;n++)t[n][0]!==f&&(o[n]=t[n][1]);return o.join("")},r.prototype.diff_text2=function(t){for(var o=[],n=0;n<t.length;n++)t[n][0]!==c&&(o[n]=t[n][1]);return o.join("")},r.prototype.diff_levenshtein=function(t){for(var o=0,n=0,a=0,i=0;i<t.length;i++){var l=t[i][0],s=t[i][1];switch(l){case f:n+=s.length;break;case c:a+=s.length;break;case g:o+=Math.max(n,a),n=0,a=0;break}}return o+=Math.max(n,a),o},r.prototype.diff_toDelta=function(t){for(var o=[],n=0;n<t.length;n++)switch(t[n][0]){case f:o[n]="+"+encodeURI(t[n][1]);break;case c:o[n]="-"+t[n][1].length;break;case g:o[n]="="+t[n][1].length;break}return o.join("	").replace(/%20/g," ")},r.prototype.diff_fromDelta=function(t,o){for(var n=[],a=0,i=0,l=o.split(/\t/g),s=0;s<l.length;s++){var d=l[s].substring(1);switch(l[s].charAt(0)){case"+":try{n[a++]=new r.Diff(f,decodeURI(d))}catch{throw new Error("Illegal escape in diff_fromDelta: "+d)}break;case"-":case"=":var b=parseInt(d,10);if(isNaN(b)||b<0)throw new Error("Invalid number in diff_fromDelta: "+d);var h=t.substring(i,i+=b);l[s].charAt(0)=="="?n[a++]=new r.Diff(g,h):n[a++]=new r.Diff(c,h);break;default:if(l[s])throw new Error("Invalid diff operation in diff_fromDelta: "+l[s])}}if(i!=t.length)throw new Error("Delta length ("+i+") does not equal source text length ("+t.length+").");return n},r.prototype.match_main=function(t,o,n){if(t==null||o==null||n==null)throw new Error("Null input. (match_main)");return n=Math.max(0,Math.min(n,t.length)),t==o?0:t.length?t.substring(n,n+o.length)==o?n:this.match_bitap_(t,o,n):-1},r.prototype.match_bitap_=function(t,o,n){if(o.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var a=this.match_alphabet_(o),i=this;function l(D,O){var $=D/o.length,I=Math.abs(n-O);return i.Match_Distance?$+I/i.Match_Distance:I?1:$}var s=this.Match_Threshold,d=t.indexOf(o,n);d!=-1&&(s=Math.min(l(0,d),s),d=t.lastIndexOf(o,n+o.length),d!=-1&&(s=Math.min(l(0,d),s)));var b=1<<o.length-1;d=-1;for(var h,u,m=o.length+t.length,y,w=0;w<o.length;w++){for(h=0,u=m;h<u;)l(w,n+u)<=s?h=u:m=u,u=Math.floor((m-h)/2+h);m=u;var v=Math.max(1,n-u+1),k=Math.min(n+u,t.length)+o.length,N=Array(k+2);N[k+1]=(1<<w)-1;for(var j=k;j>=v;j--){var E=a[t.charAt(j-1)];if(w===0?N[j]=(N[j+1]<<1|1)&E:N[j]=(N[j+1]<<1|1)&E|((y[j+1]|y[j])<<1|1)|y[j+1],N[j]&b){var S=l(w,j-1);if(S<=s)if(s=S,d=j-1,d>n)v=Math.max(1,2*n-d);else break}}if(l(w+1,n)>s)break;y=N}return d},r.prototype.match_alphabet_=function(t){for(var o={},n=0;n<t.length;n++)o[t.charAt(n)]=0;for(var n=0;n<t.length;n++)o[t.charAt(n)]|=1<<t.length-n-1;return o},r.prototype.patch_addContext_=function(t,o){if(o.length!=0){if(t.start2===null)throw Error("patch not initialized");for(var n=o.substring(t.start2,t.start2+t.length1),a=0;o.indexOf(n)!=o.lastIndexOf(n)&&n.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)a+=this.Patch_Margin,n=o.substring(t.start2-a,t.start2+t.length1+a);a+=this.Patch_Margin;var i=o.substring(t.start2-a,t.start2);i&&t.diffs.unshift(new r.Diff(g,i));var l=o.substring(t.start2+t.length1,t.start2+t.length1+a);l&&t.diffs.push(new r.Diff(g,l)),t.start1-=i.length,t.start2-=i.length,t.length1+=i.length+l.length,t.length2+=i.length+l.length}},r.prototype.patch_make=function(t,o,n){var a,i;if(typeof t=="string"&&typeof o=="string"&&typeof n>"u")a=t,i=this.diff_main(a,o,!0),i.length>2&&(this.diff_cleanupSemantic(i),this.diff_cleanupEfficiency(i));else if(t&&typeof t=="object"&&typeof o>"u"&&typeof n>"u")i=t,a=this.diff_text1(i);else if(typeof t=="string"&&o&&typeof o=="object"&&typeof n>"u")a=t,i=o;else if(typeof t=="string"&&typeof o=="string"&&n&&typeof n=="object")a=t,i=n;else throw new Error("Unknown call format to patch_make.");if(i.length===0)return[];for(var l=[],s=new r.patch_obj,d=0,b=0,h=0,u=a,m=a,y=0;y<i.length;y++){var w=i[y][0],v=i[y][1];switch(!d&&w!==g&&(s.start1=b,s.start2=h),w){case f:s.diffs[d++]=i[y],s.length2+=v.length,m=m.substring(0,h)+v+m.substring(h);break;case c:s.length1+=v.length,s.diffs[d++]=i[y],m=m.substring(0,h)+m.substring(h+v.length);break;case g:v.length<=2*this.Patch_Margin&&d&&i.length!=y+1?(s.diffs[d++]=i[y],s.length1+=v.length,s.length2+=v.length):v.length>=2*this.Patch_Margin&&d&&(this.patch_addContext_(s,u),l.push(s),s=new r.patch_obj,d=0,u=m,b=h);break}w!==f&&(b+=v.length),w!==c&&(h+=v.length)}return d&&(this.patch_addContext_(s,u),l.push(s)),l},r.prototype.patch_deepCopy=function(t){for(var o=[],n=0;n<t.length;n++){var a=t[n],i=new r.patch_obj;i.diffs=[];for(var l=0;l<a.diffs.length;l++)i.diffs[l]=new r.Diff(a.diffs[l][0],a.diffs[l][1]);i.start1=a.start1,i.start2=a.start2,i.length1=a.length1,i.length2=a.length2,o[n]=i}return o},r.prototype.patch_apply=function(t,o){if(t.length==0)return[o,[]];t=this.patch_deepCopy(t);var n=this.patch_addPadding(t);o=n+o+n,this.patch_splitMax(t);for(var a=0,i=[],l=0;l<t.length;l++){var s=t[l].start2+a,d=this.diff_text1(t[l].diffs),b,h=-1;if(d.length>this.Match_MaxBits?(b=this.match_main(o,d.substring(0,this.Match_MaxBits),s),b!=-1&&(h=this.match_main(o,d.substring(d.length-this.Match_MaxBits),s+d.length-this.Match_MaxBits),(h==-1||b>=h)&&(b=-1))):b=this.match_main(o,d,s),b==-1)i[l]=!1,a-=t[l].length2-t[l].length1;else{i[l]=!0,a=b-s;var u;if(h==-1?u=o.substring(b,b+d.length):u=o.substring(b,h+this.Match_MaxBits),d==u)o=o.substring(0,b)+this.diff_text2(t[l].diffs)+o.substring(b+d.length);else{var m=this.diff_main(d,u,!1);if(d.length>this.Match_MaxBits&&this.diff_levenshtein(m)/d.length>this.Patch_DeleteThreshold)i[l]=!1;else{this.diff_cleanupSemanticLossless(m);for(var y=0,w,v=0;v<t[l].diffs.length;v++){var k=t[l].diffs[v];k[0]!==g&&(w=this.diff_xIndex(m,y)),k[0]===f?o=o.substring(0,b+w)+k[1]+o.substring(b+w):k[0]===c&&(o=o.substring(0,b+w)+o.substring(b+this.diff_xIndex(m,y+k[1].length))),k[0]!==c&&(y+=k[1].length)}}}}}return o=o.substring(n.length,o.length-n.length),[o,i]},r.prototype.patch_addPadding=function(t){for(var o=this.Patch_Margin,n="",a=1;a<=o;a++)n+=String.fromCharCode(a);for(var a=0;a<t.length;a++)t[a].start1+=o,t[a].start2+=o;var i=t[0],l=i.diffs;if(l.length==0||l[0][0]!=g)l.unshift(new r.Diff(g,n)),i.start1-=o,i.start2-=o,i.length1+=o,i.length2+=o;else if(o>l[0][1].length){var s=o-l[0][1].length;l[0][1]=n.substring(l[0][1].length)+l[0][1],i.start1-=s,i.start2-=s,i.length1+=s,i.length2+=s}if(i=t[t.length-1],l=i.diffs,l.length==0||l[l.length-1][0]!=g)l.push(new r.Diff(g,n)),i.length1+=o,i.length2+=o;else if(o>l[l.length-1][1].length){var s=o-l[l.length-1][1].length;l[l.length-1][1]+=n.substring(0,s),i.length1+=s,i.length2+=s}return n},r.prototype.patch_splitMax=function(t){for(var o=this.Match_MaxBits,n=0;n<t.length;n++)if(!(t[n].length1<=o)){var a=t[n];t.splice(n--,1);for(var i=a.start1,l=a.start2,s="";a.diffs.length!==0;){var d=new r.patch_obj,b=!0;for(d.start1=i-s.length,d.start2=l-s.length,s!==""&&(d.length1=d.length2=s.length,d.diffs.push(new r.Diff(g,s)));a.diffs.length!==0&&d.length1<o-this.Patch_Margin;){var h=a.diffs[0][0],u=a.diffs[0][1];h===f?(d.length2+=u.length,l+=u.length,d.diffs.push(a.diffs.shift()),b=!1):h===c&&d.diffs.length==1&&d.diffs[0][0]==g&&u.length>2*o?(d.length1+=u.length,i+=u.length,b=!1,d.diffs.push(new r.Diff(h,u)),a.diffs.shift()):(u=u.substring(0,o-d.length1-this.Patch_Margin),d.length1+=u.length,i+=u.length,h===g?(d.length2+=u.length,l+=u.length):b=!1,d.diffs.push(new r.Diff(h,u)),u==a.diffs[0][1]?a.diffs.shift():a.diffs[0][1]=a.diffs[0][1].substring(u.length))}s=this.diff_text2(d.diffs),s=s.substring(s.length-this.Patch_Margin);var m=this.diff_text1(a.diffs).substring(0,this.Patch_Margin);m!==""&&(d.length1+=m.length,d.length2+=m.length,d.diffs.length!==0&&d.diffs[d.diffs.length-1][0]===g?d.diffs[d.diffs.length-1][1]+=m:d.diffs.push(new r.Diff(g,m))),b||t.splice(++n,0,d)}}},r.prototype.patch_toText=function(t){for(var o=[],n=0;n<t.length;n++)o[n]=t[n];return o.join("")},r.prototype.patch_fromText=function(t){var o=[];if(!t)return o;for(var n=t.split(`
`),a=0,i=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;a<n.length;){var l=n[a].match(i);if(!l)throw new Error("Invalid patch string: "+n[a]);var s=new r.patch_obj;for(o.push(s),s.start1=parseInt(l[1],10),l[2]===""?(s.start1--,s.length1=1):l[2]=="0"?s.length1=0:(s.start1--,s.length1=parseInt(l[2],10)),s.start2=parseInt(l[3],10),l[4]===""?(s.start2--,s.length2=1):l[4]=="0"?s.length2=0:(s.start2--,s.length2=parseInt(l[4],10)),a++;a<n.length;){var d=n[a].charAt(0);try{var b=decodeURI(n[a].substring(1))}catch{throw new Error("Illegal escape in patch_fromText: "+b)}if(d=="-")s.diffs.push(new r.Diff(c,b));else if(d=="+")s.diffs.push(new r.Diff(f,b));else if(d==" ")s.diffs.push(new r.Diff(g,b));else{if(d=="@")break;if(d!=="")throw new Error('Invalid patch mode "'+d+'" in: '+b)}a++}}return o},r.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},r.patch_obj.prototype.toString=function(){var t,o;this.length1===0?t=this.start1+",0":this.length1==1?t=this.start1+1:t=this.start1+1+","+this.length1,this.length2===0?o=this.start2+",0":this.length2==1?o=this.start2+1:o=this.start2+1+","+this.length2;for(var n=["@@ -"+t+" +"+o+` @@
`],a,i=0;i<this.diffs.length;i++){switch(this.diffs[i][0]){case f:a="+";break;case c:a="-";break;case g:a=" ";break}n[i+1]=a+encodeURI(this.diffs[i][1])+`
`}return n.join("").replace(/%20/g," ")},e.exports=r,e.exports.diff_match_patch=r,e.exports.DIFF_DELETE=c,e.exports.DIFF_INSERT=f,e.exports.DIFF_EQUAL=g})(mt);var ze=mt.exports;function vt(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(r=>{const c=e[r],f=typeof c;(f==="object"||f==="function")&&!Object.isFrozen(c)&&vt(c)}),e}class at{constructor(r){r.data===void 0&&(r.data={}),this.data=r.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function yt(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function ie(e,...r){const c=Object.create(null);for(const f in e)c[f]=e[f];return r.forEach(function(f){for(const g in f)c[g]=f[g]}),c}const to="</span>",lt=e=>!!e.scope,oo=(e,{prefix:r})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const c=e.split(".");return[`${r}${c.shift()}`,...c.map((f,g)=>`${f}${"_".repeat(g+1)}`)].join(" ")}return`${r}${e}`};class ro{constructor(r,c){this.buffer="",this.classPrefix=c.classPrefix,r.walk(this)}addText(r){this.buffer+=yt(r)}openNode(r){if(!lt(r))return;const c=oo(r.scope,{prefix:this.classPrefix});this.span(c)}closeNode(r){lt(r)&&(this.buffer+=to)}value(){return this.buffer}span(r){this.buffer+=`<span class="${r}">`}}const ct=(e={})=>{const r={children:[]};return Object.assign(r,e),r};class Ge{constructor(){this.rootNode=ct(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(r){this.top.children.push(r)}openNode(r){const c=ct({scope:r});this.add(c),this.stack.push(c)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(r){return this.constructor._walk(r,this.rootNode)}static _walk(r,c){return typeof c=="string"?r.addText(c):c.children&&(r.openNode(c),c.children.forEach(f=>this._walk(r,f)),r.closeNode(c)),r}static _collapse(r){typeof r!="string"&&r.children&&(r.children.every(c=>typeof c=="string")?r.children=[r.children.join("")]:r.children.forEach(c=>{Ge._collapse(c)}))}}class no extends Ge{constructor(r){super(),this.options=r}addText(r){r!==""&&this.add(r)}startScope(r){this.openNode(r)}endScope(){this.closeNode()}__addSublanguage(r,c){const f=r.root;c&&(f.scope=`language:${c}`),this.add(f)}toHTML(){return new ro(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function ke(e){return e?typeof e=="string"?e:e.source:null}function wt(e){return fe("(?=",e,")")}function ao(e){return fe("(?:",e,")*")}function lo(e){return fe("(?:",e,")?")}function fe(...e){return e.map(r=>ke(r)).join("")}function co(e){const r=e[e.length-1];return typeof r=="object"&&r.constructor===Object?(e.splice(e.length-1,1),r):{}}function Ke(...e){return"("+(co(e).capture?"":"?:")+e.map(r=>ke(r)).join("|")+")"}function xt(e){return new RegExp(e.toString()+"|").exec("").length-1}function io(e,r){const c=e&&e.exec(r);return c&&c.index===0}const so=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Qe(e,{joinWith:r}){let c=0;return e.map(f=>{c+=1;const g=c;let t=ke(f),o="";for(;t.length>0;){const n=so.exec(t);if(!n){o+=t;break}o+=t.substring(0,n.index),t=t.substring(n.index+n[0].length),n[0][0]==="\\"&&n[1]?o+="\\"+String(Number(n[1])+g):(o+=n[0],n[0]==="("&&c++)}return o}).map(f=>`(${f})`).join(r)}const fo=/\b\B/,kt="[a-zA-Z]\\w*",Xe="[a-zA-Z_]\\w*",_t="\\b\\d+(\\.\\d+)?",Et="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",At="\\b(0b[01]+)",go="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",ho=(e={})=>{const r=/^#![ ]*\//;return e.binary&&(e.begin=fe(r,/.*\b/,e.binary,/\b.*/)),ie({scope:"meta",begin:r,end:/$/,relevance:0,"on:begin":(c,f)=>{c.index!==0&&f.ignoreMatch()}},e)},_e={begin:"\\\\[\\s\\S]",relevance:0},bo={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[_e]},uo={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[_e]},po={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Ie=function(e,r,c={}){const f=ie({scope:"comment",begin:e,end:r,contains:[]},c);f.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const g=Ke("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return f.contains.push({begin:fe(/[ ]+/,"(",g,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),f},mo=Ie("//","$"),vo=Ie("/\\*","\\*/"),yo=Ie("#","$"),wo={scope:"number",begin:_t,relevance:0},xo={scope:"number",begin:Et,relevance:0},ko={scope:"number",begin:At,relevance:0},_o={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[_e,{begin:/\[/,end:/\]/,relevance:0,contains:[_e]}]},Eo={scope:"title",begin:kt,relevance:0},Ao={scope:"title",begin:Xe,relevance:0},No={begin:"\\.\\s*"+Xe,relevance:0},Mo=function(e){return Object.assign(e,{"on:begin":(r,c)=>{c.data._beginMatch=r[1]},"on:end":(r,c)=>{c.data._beginMatch!==r[1]&&c.ignoreMatch()}})};var De=Object.freeze({__proto__:null,APOS_STRING_MODE:bo,BACKSLASH_ESCAPE:_e,BINARY_NUMBER_MODE:ko,BINARY_NUMBER_RE:At,COMMENT:Ie,C_BLOCK_COMMENT_MODE:vo,C_LINE_COMMENT_MODE:mo,C_NUMBER_MODE:xo,C_NUMBER_RE:Et,END_SAME_AS_BEGIN:Mo,HASH_COMMENT_MODE:yo,IDENT_RE:kt,MATCH_NOTHING_RE:fo,METHOD_GUARD:No,NUMBER_MODE:wo,NUMBER_RE:_t,PHRASAL_WORDS_MODE:po,QUOTE_STRING_MODE:uo,REGEXP_MODE:_o,RE_STARTERS_RE:go,SHEBANG:ho,TITLE_MODE:Eo,UNDERSCORE_IDENT_RE:Xe,UNDERSCORE_TITLE_MODE:Ao});function jo(e,r){e.input[e.index-1]==="."&&r.ignoreMatch()}function Do(e,r){e.className!==void 0&&(e.scope=e.className,delete e.className)}function So(e,r){r&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=jo,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function Lo(e,r){Array.isArray(e.illegal)&&(e.illegal=Ke(...e.illegal))}function Co(e,r){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Oo(e,r){e.relevance===void 0&&(e.relevance=1)}const Ro=(e,r)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const c=Object.assign({},e);Object.keys(e).forEach(f=>{delete e[f]}),e.keywords=c.keywords,e.begin=fe(c.beforeMatch,wt(c.begin)),e.starts={relevance:0,contains:[Object.assign(c,{endsParent:!0})]},e.relevance=0,delete c.beforeMatch},To=["of","and","for","in","not","or","if","then","parent","list","value"],Io="keyword";function Nt(e,r,c=Io){const f=Object.create(null);return typeof e=="string"?g(c,e.split(" ")):Array.isArray(e)?g(c,e):Object.keys(e).forEach(function(t){Object.assign(f,Nt(e[t],r,t))}),f;function g(t,o){r&&(o=o.map(n=>n.toLowerCase())),o.forEach(function(n){const a=n.split("|");f[a[0]]=[t,$o(a[0],a[1])]})}}function $o(e,r){return r?Number(r):Bo(e)?0:1}function Bo(e){return To.includes(e.toLowerCase())}const it={},de=e=>{console.error(e)},st=(e,...r)=>{console.log(`WARN: ${e}`,...r)},ge=(e,r)=>{it[`${e}/${r}`]||(console.log(`Deprecated as of ${e}. ${r}`),it[`${e}/${r}`]=!0)},Re=new Error;function Mt(e,r,{key:c}){let f=0;const g=e[c],t={},o={};for(let n=1;n<=r.length;n++)o[n+f]=g[n],t[n+f]=!0,f+=xt(r[n-1]);e[c]=o,e[c]._emit=t,e[c]._multi=!0}function Po(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw de("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Re;if(typeof e.beginScope!="object"||e.beginScope===null)throw de("beginScope must be object"),Re;Mt(e,e.begin,{key:"beginScope"}),e.begin=Qe(e.begin,{joinWith:""})}}function zo(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw de("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Re;if(typeof e.endScope!="object"||e.endScope===null)throw de("endScope must be object"),Re;Mt(e,e.end,{key:"endScope"}),e.end=Qe(e.end,{joinWith:""})}}function Uo(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function Fo(e){Uo(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Po(e),zo(e)}function Ho(e){function r(o,n){return new RegExp(ke(o),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class c{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(n,a){a.position=this.position++,this.matchIndexes[this.matchAt]=a,this.regexes.push([a,n]),this.matchAt+=xt(n)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const n=this.regexes.map(a=>a[1]);this.matcherRe=r(Qe(n,{joinWith:"|"}),!0),this.lastIndex=0}exec(n){this.matcherRe.lastIndex=this.lastIndex;const a=this.matcherRe.exec(n);if(!a)return null;const i=a.findIndex((s,d)=>d>0&&s!==void 0),l=this.matchIndexes[i];return a.splice(0,i),Object.assign(a,l)}}class f{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(n){if(this.multiRegexes[n])return this.multiRegexes[n];const a=new c;return this.rules.slice(n).forEach(([i,l])=>a.addRule(i,l)),a.compile(),this.multiRegexes[n]=a,a}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(n,a){this.rules.push([n,a]),a.type==="begin"&&this.count++}exec(n){const a=this.getMatcher(this.regexIndex);a.lastIndex=this.lastIndex;let i=a.exec(n);if(this.resumingScanAtSamePosition()&&!(i&&i.index===this.lastIndex)){const l=this.getMatcher(0);l.lastIndex=this.lastIndex+1,i=l.exec(n)}return i&&(this.regexIndex+=i.position+1,this.regexIndex===this.count&&this.considerAll()),i}}function g(o){const n=new f;return o.contains.forEach(a=>n.addRule(a.begin,{rule:a,type:"begin"})),o.terminatorEnd&&n.addRule(o.terminatorEnd,{type:"end"}),o.illegal&&n.addRule(o.illegal,{type:"illegal"}),n}function t(o,n){const a=o;if(o.isCompiled)return a;[Do,Co,Fo,Ro].forEach(l=>l(o,n)),e.compilerExtensions.forEach(l=>l(o,n)),o.__beforeBegin=null,[So,Lo,Oo].forEach(l=>l(o,n)),o.isCompiled=!0;let i=null;return typeof o.keywords=="object"&&o.keywords.$pattern&&(o.keywords=Object.assign({},o.keywords),i=o.keywords.$pattern,delete o.keywords.$pattern),i=i||/\w+/,o.keywords&&(o.keywords=Nt(o.keywords,e.case_insensitive)),a.keywordPatternRe=r(i,!0),n&&(o.begin||(o.begin=/\B|\b/),a.beginRe=r(a.begin),!o.end&&!o.endsWithParent&&(o.end=/\B|\b/),o.end&&(a.endRe=r(a.end)),a.terminatorEnd=ke(a.end)||"",o.endsWithParent&&n.terminatorEnd&&(a.terminatorEnd+=(o.end?"|":"")+n.terminatorEnd)),o.illegal&&(a.illegalRe=r(o.illegal)),o.contains||(o.contains=[]),o.contains=[].concat(...o.contains.map(function(l){return qo(l==="self"?o:l)})),o.contains.forEach(function(l){t(l,a)}),o.starts&&t(o.starts,n),a.matcher=g(a),a}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=ie(e.classNameAliases||{}),t(e)}function jt(e){return e?e.endsWithParent||jt(e.starts):!1}function qo(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(r){return ie(e,{variants:null},r)})),e.cachedVariants?e.cachedVariants:jt(e)?ie(e,{starts:e.starts?ie(e.starts):null}):Object.isFrozen(e)?ie(e):e}var Zo="11.10.0";class Go extends Error{constructor(r,c){super(r),this.name="HTMLInjectionError",this.html=c}}const Ue=yt,dt=ie,ft=Symbol("nomatch"),Ko=7,Dt=function(e){const r=Object.create(null),c=Object.create(null),f=[];let g=!0;const t="Could not find the language '{}', did you forget to load/include a language module?",o={disableAutodetect:!0,name:"Plain text",contains:[]};let n={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:no};function a(p){return n.noHighlightRe.test(p)}function i(p){let A=p.className+" ";A+=p.parentNode?p.parentNode.className:"";const L=n.languageDetectRe.exec(A);if(L){const B=D(L[1]);return B||(st(t.replace("{}",L[1])),st("Falling back to no-highlight mode for this block.",p)),B?L[1]:"no-highlight"}return A.split(/\s+/).find(B=>a(B)||D(B))}function l(p,A,L){let B="",H="";typeof A=="object"?(B=p,L=A.ignoreIllegals,H=A.language):(ge("10.7.0","highlight(lang, code, ...args) has been deprecated."),ge("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),H=p,B=A),L===void 0&&(L=!0);const X={code:B,language:H};oe("before:highlight",X);const ce=X.result?X.result:s(X.language,X.code,L);return ce.code=X.code,oe("after:highlight",ce),ce}function s(p,A,L,B){const H=Object.create(null);function X(x,M){return x.keywords[M]}function ce(){if(!C.keywords){G.addText(U);return}let x=0;C.keywordPatternRe.lastIndex=0;let M=C.keywordPatternRe.exec(U),R="";for(;M;){R+=U.substring(x,M.index);const P=te.case_insensitive?M[0].toLowerCase():M[0],K=X(C,P);if(K){const[re,Ft]=K;if(G.addText(R),R="",H[P]=(H[P]||0)+1,H[P]<=Ko&&(Me+=Ft),re.startsWith("_"))R+=M[0];else{const Ht=te.classNameAliases[re]||re;ee(M[0],Ht)}}else R+=M[0];x=C.keywordPatternRe.lastIndex,M=C.keywordPatternRe.exec(U)}R+=U.substring(x),G.addText(R)}function Ae(){if(U==="")return;let x=null;if(typeof C.subLanguage=="string"){if(!r[C.subLanguage]){G.addText(U);return}x=s(C.subLanguage,U,!0,et[C.subLanguage]),et[C.subLanguage]=x._top}else x=b(U,C.subLanguage.length?C.subLanguage:null);C.relevance>0&&(Me+=x.relevance),G.__addSublanguage(x._emitter,x.language)}function W(){C.subLanguage!=null?Ae():ce(),U=""}function ee(x,M){x!==""&&(G.startScope(M),G.addText(x),G.endScope())}function We(x,M){let R=1;const P=M.length-1;for(;R<=P;){if(!x._emit[R]){R++;continue}const K=te.classNameAliases[x[R]]||x[R],re=M[R];K?ee(re,K):(U=re,ce(),U=""),R++}}function Je(x,M){return x.scope&&typeof x.scope=="string"&&G.openNode(te.classNameAliases[x.scope]||x.scope),x.beginScope&&(x.beginScope._wrap?(ee(U,te.classNameAliases[x.beginScope._wrap]||x.beginScope._wrap),U=""):x.beginScope._multi&&(We(x.beginScope,M),U="")),C=Object.create(x,{parent:{value:C}}),C}function Ve(x,M,R){let P=io(x.endRe,R);if(P){if(x["on:end"]){const K=new at(x);x["on:end"](M,K),K.isMatchIgnored&&(P=!1)}if(P){for(;x.endsParent&&x.parent;)x=x.parent;return x}}if(x.endsWithParent)return Ve(x.parent,M,R)}function $t(x){return C.matcher.regexIndex===0?(U+=x[0],1):(Pe=!0,0)}function Bt(x){const M=x[0],R=x.rule,P=new at(R),K=[R.__beforeBegin,R["on:begin"]];for(const re of K)if(re&&(re(x,P),P.isMatchIgnored))return $t(M);return R.skip?U+=M:(R.excludeBegin&&(U+=M),W(),!R.returnBegin&&!R.excludeBegin&&(U=M)),Je(R,x),R.returnBegin?0:M.length}function Pt(x){const M=x[0],R=A.substring(x.index),P=Ve(C,x,R);if(!P)return ft;const K=C;C.endScope&&C.endScope._wrap?(W(),ee(M,C.endScope._wrap)):C.endScope&&C.endScope._multi?(W(),We(C.endScope,x)):K.skip?U+=M:(K.returnEnd||K.excludeEnd||(U+=M),W(),K.excludeEnd&&(U=M));do C.scope&&G.closeNode(),!C.skip&&!C.subLanguage&&(Me+=C.relevance),C=C.parent;while(C!==P.parent);return P.starts&&Je(P.starts,x),K.returnEnd?0:M.length}function zt(){const x=[];for(let M=C;M!==te;M=M.parent)M.scope&&x.unshift(M.scope);x.forEach(M=>G.openNode(M))}let Ne={};function Ye(x,M){const R=M&&M[0];if(U+=x,R==null)return W(),0;if(Ne.type==="begin"&&M.type==="end"&&Ne.index===M.index&&R===""){if(U+=A.slice(M.index,M.index+1),!g){const P=new Error(`0 width match regex (${p})`);throw P.languageName=p,P.badRule=Ne.rule,P}return 1}if(Ne=M,M.type==="begin")return Bt(M);if(M.type==="illegal"&&!L){const P=new Error('Illegal lexeme "'+R+'" for mode "'+(C.scope||"<unnamed>")+'"');throw P.mode=C,P}else if(M.type==="end"){const P=Pt(M);if(P!==ft)return P}if(M.type==="illegal"&&R==="")return 1;if(Be>1e5&&Be>M.index*3)throw new Error("potential infinite loop, way more iterations than matches");return U+=R,R.length}const te=D(p);if(!te)throw de(t.replace("{}",p)),new Error('Unknown language: "'+p+'"');const Ut=Ho(te);let $e="",C=B||Ut;const et={},G=new n.__emitter(n);zt();let U="",Me=0,se=0,Be=0,Pe=!1;try{if(te.__emitTokens)te.__emitTokens(A,G);else{for(C.matcher.considerAll();;){Be++,Pe?Pe=!1:C.matcher.considerAll(),C.matcher.lastIndex=se;const x=C.matcher.exec(A);if(!x)break;const M=A.substring(se,x.index),R=Ye(M,x);se=x.index+R}Ye(A.substring(se))}return G.finalize(),$e=G.toHTML(),{language:p,value:$e,relevance:Me,illegal:!1,_emitter:G,_top:C}}catch(x){if(x.message&&x.message.includes("Illegal"))return{language:p,value:Ue(A),illegal:!0,relevance:0,_illegalBy:{message:x.message,index:se,context:A.slice(se-100,se+100),mode:x.mode,resultSoFar:$e},_emitter:G};if(g)return{language:p,value:Ue(A),illegal:!1,relevance:0,errorRaised:x,_emitter:G,_top:C};throw x}}function d(p){const A={value:Ue(p),illegal:!1,relevance:0,_top:o,_emitter:new n.__emitter(n)};return A._emitter.addText(p),A}function b(p,A){A=A||n.languages||Object.keys(r);const L=d(p),B=A.filter(D).filter($).map(W=>s(W,p,!1));B.unshift(L);const H=B.sort((W,ee)=>{if(W.relevance!==ee.relevance)return ee.relevance-W.relevance;if(W.language&&ee.language){if(D(W.language).supersetOf===ee.language)return 1;if(D(ee.language).supersetOf===W.language)return-1}return 0}),[X,ce]=H,Ae=X;return Ae.secondBest=ce,Ae}function h(p,A,L){const B=A&&c[A]||L;p.classList.add("hljs"),p.classList.add(`language-${B}`)}function u(p){let A=null;const L=i(p);if(a(L))return;if(oe("before:highlightElement",{el:p,language:L}),p.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",p);return}if(p.children.length>0&&(n.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(p)),n.throwUnescapedHTML))throw new Go("One of your code blocks includes unescaped HTML.",p.innerHTML);A=p;const B=A.textContent,H=L?l(B,{language:L,ignoreIllegals:!0}):b(B);p.innerHTML=H.value,p.dataset.highlighted="yes",h(p,L,H.language),p.result={language:H.language,re:H.relevance,relevance:H.relevance},H.secondBest&&(p.secondBest={language:H.secondBest.language,relevance:H.secondBest.relevance}),oe("after:highlightElement",{el:p,result:H,text:B})}function m(p){n=dt(n,p)}const y=()=>{k(),ge("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function w(){k(),ge("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let v=!1;function k(){if(document.readyState==="loading"){v=!0;return}document.querySelectorAll(n.cssSelector).forEach(u)}function N(){v&&k()}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",N,!1);function j(p,A){let L=null;try{L=A(e)}catch(B){if(de("Language definition for '{}' could not be registered.".replace("{}",p)),g)de(B);else throw B;L=o}L.name||(L.name=p),r[p]=L,L.rawDefinition=A.bind(null,e),L.aliases&&O(L.aliases,{languageName:p})}function E(p){delete r[p];for(const A of Object.keys(c))c[A]===p&&delete c[A]}function S(){return Object.keys(r)}function D(p){return p=(p||"").toLowerCase(),r[p]||r[c[p]]}function O(p,{languageName:A}){typeof p=="string"&&(p=[p]),p.forEach(L=>{c[L.toLowerCase()]=A})}function $(p){const A=D(p);return A&&!A.disableAutodetect}function I(p){p["before:highlightBlock"]&&!p["before:highlightElement"]&&(p["before:highlightElement"]=A=>{p["before:highlightBlock"](Object.assign({block:A.el},A))}),p["after:highlightBlock"]&&!p["after:highlightElement"]&&(p["after:highlightElement"]=A=>{p["after:highlightBlock"](Object.assign({block:A.el},A))})}function q(p){I(p),f.push(p)}function Q(p){const A=f.indexOf(p);A!==-1&&f.splice(A,1)}function oe(p,A){const L=p;f.forEach(function(B){B[L]&&B[L](A)})}function me(p){return ge("10.7.0","highlightBlock will be removed entirely in v12.0"),ge("10.7.0","Please use highlightElement now."),u(p)}Object.assign(e,{highlight:l,highlightAuto:b,highlightAll:k,highlightElement:u,highlightBlock:me,configure:m,initHighlighting:y,initHighlightingOnLoad:w,registerLanguage:j,unregisterLanguage:E,listLanguages:S,getLanguage:D,registerAliases:O,autoDetection:$,inherit:dt,addPlugin:q,removePlugin:Q}),e.debugMode=function(){g=!1},e.safeMode=function(){g=!0},e.versionString=Zo,e.regex={concat:fe,lookahead:wt,either:Ke,optional:lo,anyNumberOfTimes:ao};for(const p in De)typeof De[p]=="object"&&vt(De[p]);return Object.assign(e,De),e},ue=Dt({});ue.newInstance=()=>Dt({});var Qo=ue;ue.HighlightJS=ue;ue.default=ue;const Y=eo(Qo);function Xo(e){const r=e.regex,c=r.concat(/[\p{L}_]/u,r.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),f=/[\p{L}0-9._:-]+/u,g={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},t={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},o=e.inherit(t,{begin:/\(/,end:/\)/}),n=e.inherit(e.APOS_STRING_MODE,{className:"string"}),a=e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),i={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:f,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[g]},{begin:/'/,end:/'/,contains:[g]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[t,a,n,o,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[t,o,a,n]}]}]},e.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},g,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[a]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[i],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[i],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:r.concat(/</,r.lookahead(r.concat(c,r.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:c,relevance:0,starts:i}]},{className:"tag",begin:r.concat(/<\//,r.lookahead(r.concat(c,/>/))),contains:[{className:"name",begin:c,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}const gt="[A-Za-z$_][0-9A-Za-z$_]*",Wo=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],Jo=["true","false","null","undefined","NaN","Infinity"],St=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],Lt=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],Ct=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],Vo=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],Yo=[].concat(Ct,St,Lt);function er(e){const r=e.regex,c=(p,{after:A})=>{const L="</"+p[0].slice(1);return p.input.indexOf(L,A)!==-1},f=gt,g={begin:"<>",end:"</>"},t=/<[A-Za-z0-9\\._:-]+\s*\/>/,o={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(p,A)=>{const L=p[0].length+p.index,B=p.input[L];if(B==="<"||B===","){A.ignoreMatch();return}B===">"&&(c(p,{after:L})||A.ignoreMatch());let H;const X=p.input.substring(L);if(H=X.match(/^\s*=/)){A.ignoreMatch();return}if((H=X.match(/^\s+extends\s+/))&&H.index===0){A.ignoreMatch();return}}},n={$pattern:gt,keyword:Wo,literal:Jo,built_in:Yo,"variable.language":Vo},a="[0-9](_?[0-9])*",i=`\\.(${a})`,l="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",s={className:"number",variants:[{begin:`(\\b(${l})((${i})|\\.)?|(${i}))[eE][+-]?(${a})\\b`},{begin:`\\b(${l})\\b((${i})\\b|\\.)?|(${i})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},d={className:"subst",begin:"\\$\\{",end:"\\}",keywords:n,contains:[]},b={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,d],subLanguage:"xml"}},h={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,d],subLanguage:"css"}},u={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,d],subLanguage:"graphql"}},m={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,d]},y={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:f+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},w=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,b,h,u,m,{match:/\$\d+/},s];d.contains=w.concat({begin:/\{/,end:/\}/,keywords:n,contains:["self"].concat(w)});const v=[].concat(y,d.contains),k=v.concat([{begin:/(\s*)\(/,end:/\)/,keywords:n,contains:["self"].concat(v)}]),N={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:n,contains:k},j={variants:[{match:[/class/,/\s+/,f,/\s+/,/extends/,/\s+/,r.concat(f,"(",r.concat(/\./,f),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,f],scope:{1:"keyword",3:"title.class"}}]},E={relevance:0,match:r.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...St,...Lt]}},S={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},D={variants:[{match:[/function/,/\s+/,f,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[N],illegal:/%/},O={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function $(p){return r.concat("(?!",p.join("|"),")")}const I={match:r.concat(/\b/,$([...Ct,"super","import"].map(p=>`${p}\\s*\\(`)),f,r.lookahead(/\s*\(/)),className:"title.function",relevance:0},q={begin:r.concat(/\./,r.lookahead(r.concat(f,/(?![0-9A-Za-z$_(])/))),end:f,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},Q={match:[/get|set/,/\s+/,f,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},N]},oe="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",me={match:[/const|var|let/,/\s+/,f,/\s*/,/=\s*/,/(async\s*)?/,r.lookahead(oe)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[N]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:n,exports:{PARAMS_CONTAINS:k,CLASS_REFERENCE:E},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),S,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,b,h,u,m,y,{match:/\$\d+/},s,E,{className:"attr",begin:f+r.lookahead(":"),relevance:0},me,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[y,e.REGEXP_MODE,{className:"function",begin:oe,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:n,contains:k}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:g.begin,end:g.end},{match:t},{begin:o.begin,"on:begin":o.isTrulyOpeningTag,end:o.end}],subLanguage:"xml",contains:[{begin:o.begin,end:o.end,skip:!0,contains:["self"]}]}]},D,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[N,e.inherit(e.TITLE_MODE,{begin:f,className:"title.function"})]},{match:/\.\.\./,relevance:0},q,{match:"\\$"+f,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[N]},I,O,j,Q,{match:/\$[(.]/}]}}function tr(e){const r={className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},c={match:/[{}[\],:]/,className:"punctuation",relevance:0},f=["true","false","null"],g={scope:"literal",beginKeywords:f.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:f},contains:[r,c,e.QUOTE_STRING_MODE,g,e.C_NUMBER_MODE,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}function or(e){const r="true false yes no null",c="[\\w#;/?:@&=+$,.~*'()[\\]]+",f={className:"attr",variants:[{begin:/\w[\w :()\./-]*:(?=[ \t]|$)/},{begin:/"\w[\w :()\./-]*":(?=[ \t]|$)/},{begin:/'\w[\w :()\./-]*':(?=[ \t]|$)/}]},g={className:"template-variable",variants:[{begin:/\{\{/,end:/\}\}/},{begin:/%\{/,end:/\}/}]},t={className:"string",relevance:0,variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/\S+/}],contains:[e.BACKSLASH_ESCAPE,g]},o=e.inherit(t,{variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/[^\s,{}[\]]+/}]}),n={className:"number",begin:"\\b[0-9]{4}(-[0-9][0-9]){0,2}([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?(\\.[0-9]*)?([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?\\b"},a={end:",",endsWithParent:!0,excludeEnd:!0,keywords:r,relevance:0},i={begin:/\{/,end:/\}/,contains:[a],illegal:"\\n",relevance:0},l={begin:"\\[",end:"\\]",contains:[a],illegal:"\\n",relevance:0},s=[f,{className:"meta",begin:"^---\\s*$",relevance:10},{className:"string",begin:"[\\|>]([1-9]?[+-])?[ ]*\\n( +)[^ ][^\\n]*\\n(\\2[^\\n]+\\n?)*"},{begin:"<%[%=-]?",end:"[%-]?%>",subLanguage:"ruby",excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:"!\\w+!"+c},{className:"type",begin:"!<"+c+">"},{className:"type",begin:"!"+c},{className:"type",begin:"!!"+c},{className:"meta",begin:"&"+e.UNDERSCORE_IDENT_RE+"$"},{className:"meta",begin:"\\*"+e.UNDERSCORE_IDENT_RE+"$"},{className:"bullet",begin:"-(?=[ ]|$)",relevance:0},e.HASH_COMMENT_MODE,{beginKeywords:r,keywords:{literal:r}},n,{className:"number",begin:e.C_NUMBER_RE+"\\b",relevance:0},i,l,t],d=[...s];return d.pop(),d.push(o),a.contains=d,{name:"YAML",case_insensitive:!0,aliases:["yml"],contains:s}}function rr(e){return{name:"Plain text",aliases:["text","txt"],disableAutodetect:!0}}function nr(e){const r=e.regex,c=new RegExp("[\\p{XID_Start}_]\\p{XID_Continue}*","u"),f=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],g={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:f,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},t={className:"meta",begin:/^(>>>|\.\.\.) /},o={className:"subst",begin:/\{/,end:/\}/,keywords:g,illegal:/#/},n={begin:/\{\{/,relevance:0},a={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,t],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,t],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,t,n,o]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,t,n,o]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[e.BACKSLASH_ESCAPE,n,o]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,n,o]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},i="[0-9](_?[0-9])*",l=`(\\b(${i}))?\\.(${i})|\\b(${i})\\.`,s=`\\b|${f.join("|")}`,d={className:"number",relevance:0,variants:[{begin:`(\\b(${i})|(${l}))[eE][+-]?(${i})[jJ]?(?=${s})`},{begin:`(${l})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${s})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${s})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${s})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${s})`},{begin:`\\b(${i})[jJ](?=${s})`}]},b={className:"comment",begin:r.lookahead(/# type:/),end:/$/,keywords:g,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},h={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:g,contains:["self",t,d,a,e.HASH_COMMENT_MODE]}]};return o.contains=[a,d,t],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:g,illegal:/(<\/|\?)|=>/,contains:[t,d,{scope:"variable.language",match:/\bself\b/},{beginKeywords:"if",relevance:0},{match:/\bor\b/,scope:"keyword"},a,b,e.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,c],scope:{1:"keyword",3:"title.function"},contains:[h]},{variants:[{match:[/\bclass/,/\s+/,c,/\s*/,/\(\s*/,c,/\s*\)/]},{match:[/\bclass/,/\s+/,c]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[d,h,a]}]}}var he="[0-9](_*[0-9])*",Se=`\\.(${he})`,Le="[0-9a-fA-F](_*[0-9a-fA-F])*",ht={className:"number",variants:[{begin:`(\\b(${he})((${Se})|\\.)?|(${Se}))[eE][+-]?(${he})[fFdD]?\\b`},{begin:`\\b(${he})((${Se})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${Se})[fFdD]?\\b`},{begin:`\\b(${he})[fFdD]\\b`},{begin:`\\b0[xX]((${Le})\\.?|(${Le})?\\.(${Le}))[pP][+-]?(${he})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${Le})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};function Ot(e,r,c){return c===-1?"":e.replace(r,f=>Ot(e,r,c-1))}function ar(e){const r=e.regex,c="[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",f=c+Ot("(?:<"+c+"~~~(?:\\s*,\\s*"+c+"~~~)*>)?",/~~~/g,2),g={keyword:["synchronized","abstract","private","var","static","if","const ","for","while","strictfp","finally","protected","import","native","final","void","enum","else","break","transient","catch","instanceof","volatile","case","assert","package","default","public","try","switch","continue","throws","protected","public","private","module","requires","exports","do","sealed","yield","permits","goto"],literal:["false","true","null"],type:["char","boolean","long","float","int","byte","short","double"],built_in:["super","this"]},t={className:"meta",begin:"@"+c,contains:[{begin:/\(/,end:/\)/,contains:["self"]}]},o={className:"params",begin:/\(/,end:/\)/,keywords:g,relevance:0,contains:[e.C_BLOCK_COMMENT_MODE],endsParent:!0};return{name:"Java",aliases:["jsp"],keywords:g,illegal:/<\/|#/,contains:[e.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),{begin:/import java\.[a-z]+\./,keywords:"import",relevance:2},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{begin:/"""/,end:/"""/,className:"string",contains:[e.BACKSLASH_ESCAPE]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,{match:[/\b(?:class|interface|enum|extends|implements|new)/,/\s+/,c],className:{1:"keyword",3:"title.class"}},{match:/non-sealed/,scope:"keyword"},{begin:[r.concat(/(?!else)/,c),/\s+/,c,/\s+/,/=(?!=)/],className:{1:"type",3:"variable",5:"operator"}},{begin:[/record/,/\s+/,c],className:{1:"keyword",3:"title.class"},contains:[o,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{beginKeywords:"new throw return else",relevance:0},{begin:["(?:"+f+"\\s+)",e.UNDERSCORE_IDENT_RE,/\s*(?=\()/],className:{2:"title.function"},keywords:g,contains:[{className:"params",begin:/\(/,end:/\)/,keywords:g,relevance:0,contains:[t,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,ht,e.C_BLOCK_COMMENT_MODE]},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},ht,t]}}function lr(e){const r=e.regex,c={},f={begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[c]}]};Object.assign(c,{className:"variable",variants:[{begin:r.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},f]});const g={className:"subst",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]},t=e.inherit(e.COMMENT(),{match:[/(^|\s)/,/#.*$/],scope:{2:"comment"}}),o={begin:/<<-?\s*(?=\w+)/,starts:{contains:[e.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},n={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,c,g]};g.contains.push(n);const a={match:/\\"/},i={className:"string",begin:/'/,end:/'/},l={match:/\\'/},s={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},e.NUMBER_MODE,c]},d=["fish","bash","zsh","sh","csh","ksh","tcsh","dash","scsh"],b=e.SHEBANG({binary:`(${d.join("|")})`,relevance:10}),h={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0},u=["if","then","else","elif","fi","for","while","until","in","do","done","case","esac","function","select"],m=["true","false"],y={match:/(\/[a-z._-]+)+/},w=["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset"],v=["alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","sudo","type","typeset","ulimit","unalias"],k=["autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp"],N=["chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"];return{name:"Bash",aliases:["sh","zsh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:u,literal:m,built_in:[...w,...v,"set","shopt",...k,...N]},contains:[b,e.SHEBANG(),h,s,t,o,y,n,a,i,l,c]}}function cr(e){const r=e.regex,c=e.COMMENT("--","$"),f={className:"string",variants:[{begin:/'/,end:/'/,contains:[{begin:/''/}]}]},g={begin:/"/,end:/"/,contains:[{begin:/""/}]},t=["true","false","unknown"],o=["double precision","large object","with timezone","without timezone"],n=["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],a=["add","asc","collation","desc","final","first","last","view"],i=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year"],l=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],s=["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"],d=["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"],b=l,h=[...i,...a].filter(v=>!l.includes(v)),u={className:"variable",begin:/@[a-z0-9][a-z0-9_]*/},m={className:"operator",begin:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0},y={begin:r.concat(/\b/,r.either(...b),/\s*\(/),relevance:0,keywords:{built_in:b}};function w(v,{exceptions:k,when:N}={}){const j=N;return k=k||[],v.map(E=>E.match(/\|\d+$/)||k.includes(E)?E:j(E)?`${E}|0`:E)}return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:w(h,{when:v=>v.length<3}),literal:t,type:n,built_in:s},contains:[{begin:r.either(...d),relevance:0,keywords:{$pattern:/[\w\.]+/,keyword:h.concat(d),literal:t,type:n}},{className:"type",begin:r.either(...o)},y,u,f,g,e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE,c,m]}}Y.registerLanguage("xml",Xo);Y.registerLanguage("javascript",er);Y.registerLanguage("json",tr);Y.registerLanguage("yaml",or);Y.registerLanguage("plaintext",rr);Y.registerLanguage("python",nr);Y.registerLanguage("java",ar);Y.registerLanguage("bash",lr);Y.registerLanguage("sql",cr);var _=(e=>(e.EQUAL="equal",e.DELETE="removed",e.ADD="added",e.EMPTY="empty",e))(_||{});const ne="<code-diff-modified>",ae="</code-diff-modified>",ir=ne.replace("<","&lt;").replace(">","&gt;"),sr=ae.replace("<","&lt;").replace(">","&gt;");function pe(e){return e===void 0?_.EQUAL:e.added?_.ADD:e.removed?_.DELETE:_.EQUAL}function Te(e,r,c="word"){return typeof e>"u"?r:typeof r>"u"?e:(c==="char"?Qt:Wt)(e,r).filter(f=>pe(f)!==_.DELETE).map(f=>pe(f)===_.ADD?`${ne}${f.value}${ae}`:f.value).join("")}function Rt(e,r){const c=new ze.diff_match_patch,f=c.diff_linesToChars_(e,r),g=f.chars1,t=f.chars2,o=f.lineArray,n=c.diff_main(g,t,!1);return c.diff_charsToLines_(n,o),n.map(a=>{const[i,l]=a;return{count:l.replace(/\n$/,"").split(`
`).length,value:l,removed:i===ze.DIFF_DELETE,added:i===ze.DIFF_INSERT}})}function J(e,r){if(!r.match(new RegExp(`(${ne}|${ae})`,"g")))return Y.highlight(r,{language:e}).value;let c=r;const f=r.replace(new RegExp(`(${ne}|${ae})`,"g"),""),g=document.createElement("div");g.innerHTML=Y.highlight(f,{language:e}).value;let t=!1;const o=n=>{n.childNodes.forEach(a=>{if(a.nodeType===Node.ELEMENT_NODE&&o(a),a.nodeType===Node.TEXT_NODE){if(!a.textContent)return;let i=a.textContent,l="";for(t&&(l=l+ne);i.length;){if(c.startsWith(ne)){c=c.slice(ne.length),l=l+ne,t=!0;continue}if(c.startsWith(ae)){c=c.slice(ae.length),l=l+ae,t=!1;continue}const s=c.match(new RegExp(`(${ne}|${ae})`)),d=s&&s.index?s.index:c.length,b=Math.min(d,i.length);l=l+c.substring(0,b),c=c.slice(b),i=i.slice(b)}t&&(l=l+ae),a.textContent=l}})};return o(g),g.innerHTML.replace(new RegExp(ir,"g"),'<span class="x">').replace(new RegExp(sr,"g"),"</span>")}function Tt(e,r){const c=(a,i)=>(a.match(new RegExp(i,"g"))||[]).length,f=a=>a.filter(i=>r==null?void 0:r.test(i)).length;let g=0,t=0,o=0,n=0;for(const a of e){if(a.added){const i=f(a.value.trim().split(`
`));g+=c(a.value.trim(),`
`)+1-i,o+=i;continue}if(a.removed){const i=f(a.value.trim().split(`
`));t+=c(a.value.trim(),`
`)+1-i,n+=i;continue}}return{additionsNum:g,deletionsNum:t,ignoreAdditionsNum:o,ignoreDeletionsNum:n}}function dr(e,r,c="plaintext",f="word",g=!1,t=10,o){const n=()=>({type:_.EMPTY}),a=(w,v,k)=>({type:w,num:v,code:k}),i=Rt(e,r),l=o?new RegExp(o):void 0;let s=0,d=0,b=!1;const h=[],u={changes:h,collector:[],stat:Tt(i,l)};for(let w=0;w<i.length;w++){if(b){b=!1;continue}const[v,k]=[i[w],i[w+1]],[N,j]=[pe(v),pe(k)],E=v.value.replace(/\n$/,"").split(`
`);if(k===void 0){for(const D of E){let O=n(),$=n();const I=J(c,D);N===_.EQUAL&&(s++,d++,O=a(_.EQUAL,s,I),$=a(_.EQUAL,d,I)),N===_.DELETE&&(s++,O=a(_.DELETE,s,I),$=n()),N===_.ADD&&(d++,O=n(),$=a(_.ADD,d,I)),h.push({left:O,right:$})}break}if(N===_.EQUAL)for(const D of E){s++,d++;const O=J(c,D);h.push({left:a(_.EQUAL,s,O),right:a(_.EQUAL,d,O)})}const S=k.value.replace(/\n$/,"").split(`
`);if(N===_.DELETE){if(j===_.EQUAL)for(const D of E)s++,h.push({left:a(_.DELETE,s,J(c,D)),right:n()});if(j===_.ADD){b=!0;const D=Math.max(v.count,k.count);for(let O=0;O<D;O++){O<v.count&&s++,O<k.count&&d++;const[$,I]=[E[O],S[O]],q=g||E.length===S.length,Q=q?Te(I,$,f):$,oe=q?Te($,I,f):I,me=l!=null&&l.test($)?_.EQUAL:_.DELETE,p=l!=null&&l.test(I)?_.EQUAL:_.ADD,A=O<v.count?a(me,s,J(c,Q)):n(),L=O<k.count?a(p,d,J(c,oe)):n();h.push({left:A,right:L})}}}if(N===_.ADD)for(const D of E)d++,h.push({left:n(),right:a(_.ADD,d,J(c,D))})}if(e===r){for(let w=0;w<h.length;w++)h[w].fold=!1;return u}for(let w=0;w<h.length;w++){const v=h[w];if(v.left.type===_.DELETE||v.right.type===_.ADD){const[k,N]=[Math.max(w-t,0),Math.min(w+t+1,h.length)];for(let j=k;j<N;j++)h[j].fold=!1}v.fold===void 0&&(v.fold=!0)}const m=[];let y=[];for(let w=0;w<h.length;w++){const v=h[w];if(v.fold===!1){y.length&&(y[0].hideIndex=u.collector.length,u.collector.push({lines:y,fold:!0}),y=[]),m.push(v);continue}v.hide=!0,y.push(v),m.push(v)}return y.length&&(y[0].hideIndex=u.collector.length,u.collector.push({lines:y,fold:!0}),y=[]),u.changes=m,u}function fr(e,r,c="plaintext",f="word",g=!1,t=10,o){const n=Rt(e,r),a=o?new RegExp(o):void 0;let i=0,l=0,s=!1;const d=[],b={changes:d,collector:[],stat:Tt(n,a)};for(let m=0;m<n.length;m++){if(s){s=!1;continue}const[y,w]=[n[m],n[m+1]],[v,k]=[pe(y),pe(w)],N=y.value.replace(/\n$/,"").split(`
`);if(w===void 0){for(const E of N){v===_.EQUAL&&(i++,l++),v===_.DELETE&&i++,v===_.ADD&&l++;const S=J(c,E);d.push({type:v,code:S,addNum:v===_.DELETE?void 0:l,delNum:v===_.ADD?void 0:i})}break}if(v===_.EQUAL)for(const E of N){i++,l++;const S=J(c,E);d.push({type:_.EQUAL,code:S,delNum:i,addNum:l})}const j=w.value.replace(/\n$/,"").split(`
`);if(v===_.DELETE)if(k===_.ADD&&(N.length===j.length||g)){for(let E=0;E<N.length;E++){const S=N[E],D=j[E];i++;const O=J(c,Te(D,S,f));d.push({type:a!=null&&a.test(S)?_.EQUAL:_.DELETE,code:O,delNum:i})}for(let E=0;E<j.length;E++){const S=N[E],D=j[E];l++;const O=J(c,Te(S,D,f));d.push({type:a!=null&&a.test(D)?_.EQUAL:_.ADD,code:O,addNum:l})}s=!0}else for(const E of N){i++;const S=J(c,E);d.push({type:_.DELETE,code:S,delNum:i})}if(v===_.ADD)for(const E of N){l++;const S=J(c,E);d.push({type:_.ADD,code:S,addNum:l})}}for(let m=0;m<d.length;m++){const y=d[m];if(y.type===_.DELETE||y.type===_.ADD){const[w,v]=[Math.max(m-t,0),Math.min(m+t+1,d.length)];for(let k=w;k<v;k++)d[k].fold=!1}y.fold===void 0&&(y.fold=!0)}if(e===r){for(let m=0;m<d.length;m++)d[m].fold=!1;return b}const h=[];let u=[];for(let m=0;m<d.length;m++){const y=d[m];if(y.fold===!1){u.length&&(u[0].hideIndex=b.collector.length,b.collector.push({lines:u,fold:!0}),u=[]),h.push(y);continue}y.type==="equal"&&(y.hide=!0,u.push(y)),h.push(y)}return u.length&&(u[0].hideIndex=b.collector.length,b.collector.push({lines:u,fold:!0}),u=[]),b.changes=h,b}const gr={key:0},hr=T("td",{class:"blob-code blob-code-hunk",align:"left"}," ⋯ ",-1),br={key:1},ur=["data-code-marker","innerHTML"],pr=Ee({__name:"UnifiedLine",props:{line:{}},emits:["expand"],setup(e,{emit:r}){const c=r;function f(g){return g===_.DELETE?"-":g===_.ADD?"+":""}return(g,t)=>g.line.hideIndex!==void 0&&g.line.hide?(F(),Z("tr",gr,[T("td",{class:"blob-num blob-num-hunk text-center",colspan:"2",onClick:t[0]||(t[0]=o=>c("expand",g.line))}," > "),hr])):g.line.hide?ye("",!0):(F(),Z("tr",br,[T("td",{class:we(["blob-num",{"blob-num-deletion":g.line.type===z(_).DELETE,"blob-num-addition":g.line.type===z(_).ADD,"blob-num-context":g.line.type===z(_).EQUAL,"blob-num-hunk":g.line.hide!==void 0}])},V(g.line.delNum),3),T("td",{class:we(["blob-num",{"blob-num-deletion":g.line.type===z(_).DELETE,"blob-num-addition":g.line.type===z(_).ADD,"blob-num-context":g.line.type===z(_).EQUAL,"blob-num-hunk":g.line.hide!==void 0}])},V(g.line.addNum),3),T("td",{class:we(["blob-code",{"blob-code-deletion":g.line.type===z(_).DELETE,"blob-code-addition":g.line.type===z(_).ADD,"blob-code-context":g.line.type===z(_).EQUAL,"blob-code-hunk":g.line.hide!==void 0}])},[T("span",{class:"blob-code-inner blob-code-marker","data-code-marker":f(g.line.type),innerHTML:g.line.code},null,8,ur)],2)]))}}),mr={class:"diff-table"},vr=Ee({__name:"UnifiedViewer",props:{diffChange:{}},setup(e){const r=e;function c({hideIndex:f}){f!==void 0&&r.diffChange.collector[f].lines.forEach(g=>{g.hide=!1,g.fold=!1})}return(f,g)=>{var t;return F(),Z("table",mr,[T("tbody",null,[(F(!0),Z(be,null,qe((t=f.diffChange)==null?void 0:t.changes,(o,n)=>(F(),Oe(pr,{key:n,line:o,onExpand:c},null,8,["line"]))),128))])])}}}),yr={key:0},wr=T("td",{class:"blob-code blob-code-inner blob-code-hunk",colspan:"3",align:"left"}," ⋯ ",-1),xr={key:1},kr=T("td",{class:"blob-num blob-num-empty empty-cell"},null,-1),_r=T("td",{class:"blob-code blob-code-empty empty-cell"},null,-1),Er=["onMousedown"],Ar=["data-code-marker","innerHTML"],Nr=Ee({__name:"SplitLine",props:{splitLine:{}},emits:["expand"],setup(e,{emit:r}){const c=r;function f(t){return t===_.DELETE?"-":t===_.ADD?"+":""}function g(t){const o=document.querySelectorAll(".file-diff-split .split-side-left"),n=document.querySelectorAll(".file-diff-split .split-side-right");for(const a of n)a.classList.toggle("no-select",t==="left");for(const a of o)a.classList.toggle("no-select",t==="right")}return(t,o)=>t.splitLine.hideIndex!==void 0&&t.splitLine.hide?(F(),Z("tr",yr,[T("td",{class:"blob-num blob-num-hunk",colspan:"1",onClick:o[0]||(o[0]=n=>c("expand",t.splitLine))}," > "),wr])):t.splitLine.hide?ye("",!0):(F(),Z("tr",xr,[(F(!0),Z(be,null,qe([t.splitLine.left,t.splitLine.right],(n,a)=>(F(),Z(be,null,[n.type===z(_).EMPTY?(F(),Z(be,{key:0},[kr,_r],64)):(F(),Z(be,{key:1},[T("td",{class:we(["blob-num",{"blob-num-deletion":n.type===z(_).DELETE,"blob-num-addition":n.type===z(_).ADD,"blob-num-context":n.type===z(_).EQUAL,"blob-num-hunk":t.splitLine.hide!==void 0}])},V(n.num),3),T("td",{class:we(["blob-code",{"blob-code-deletion":n.type===z(_).DELETE,"blob-code-addition":n.type===z(_).ADD,"blob-code-context":n.type===z(_).EQUAL,"blob-code-hunk":t.splitLine.hide!==void 0,"split-side-left":a===0,"split-side-right":a===1}]),onMousedown:i=>g(a===0?"left":"right")},[T("span",{class:"blob-code-inner blob-code-marker","data-code-marker":f(n.type),innerHTML:n.code},null,8,Ar)],42,Er)],64))],64))),256))]))}}),Mr={class:"file-diff-split diff-table"},jr=T("colgroup",null,[T("col",{width:"44"}),T("col"),T("col",{width:"44"}),T("col")],-1),Dr=Ee({__name:"SplitViewer",props:{diffChange:{}},setup(e){const r=e;function c({hideIndex:f}){f!==void 0&&r.diffChange.collector[f].lines.forEach(g=>{g.hide=!1,g.fold=!1})}return(f,g)=>{var t;return F(),Z("table",Mr,[jr,T("tbody",null,[(F(!0),Z(be,null,qe((t=f.diffChange)==null?void 0:t.changes,(o,n)=>(F(),Oe(Nr,{key:n,"split-line":o,onExpand:c},null,8,["split-line"]))),128))])])}}}),It=(e,r)=>{const c=e.__vccOpts||e;for(const[f,g]of r)c[f]=g;return c},Sr={name:"DownArrowIcon"},Lr={width:"1rem",viewBox:"0 0 512 512",xmlns:"http://www.w3.org/2000/svg"},Cr=T("path",{d:"M383.6,322.7L278.6,423c-5.8,6-13.7,9-22.4,9c-8.7,0-16.5-3-22.4-9L128.4,322.7c-12.5-11.9-12.5-31.3,0-43.2  c12.5-11.9,32.7-11.9,45.2,0l50.4,48.2v-217c0-16.9,14.3-30.6,32-30.6c17.7,0,32,13.7,32,30.6v217l50.4-48.2  c12.5-11.9,32.7-11.9,45.2,0C396.1,291.4,396.1,310.7,383.6,322.7z"},null,-1),Or=[Cr];function Rr(e,r,c,f,g,t){return F(),Z("svg",Lr,Or)}const bt=It(Sr,[["render",Rr]]),Tr={name:"UpArrowIcon"},Ir={width:"1rem",viewBox:"0 0 512 512",xmlns:"http://www.w3.org/2000/svg"},$r=T("path",{d:"M128.4,189.3L233.4,89c5.8-6,13.7-9,22.4-9c8.7,0,16.5,3,22.4,9l105.4,100.3c12.5,11.9,12.5,31.3,0,43.2  c-12.5,11.9-32.7,11.9-45.2,0L288,184.4v217c0,16.9-14.3,30.6-32,30.6c-17.7,0-32-13.7-32-30.6v-217l-50.4,48.2  c-12.5,11.9-32.7,11.9-45.2,0C115.9,220.6,115.9,201.3,128.4,189.3z"},null,-1),Br=[$r];function Pr(e,r,c,f,g,t){return F(),Z("svg",Ir,Br)}const ut=It(Tr,[["render",Pr]]),zr=["theme"],Ur={key:0,class:"file-header"},Fr={key:0,class:"file-info"},Hr={class:"info-left"},qr={class:"info-left"},Zr={class:"diff-commandbar"},Gr={key:0,class:"diff-stat"},Kr={class:"diff-stat-added"},Qr={class:"diff-stat-deleted"},Xr={key:1,class:"file-info"},Wr={class:"info-left"},Jr={class:"info-right"},Vr={style:{"margin-left":"20px"}},Yr={class:"diff-commandbar"},en={key:0,class:"diff-stat"},tn={class:"diff-stat-added"},on={class:"diff-stat-deleted"},nn=Ee({__name:"CodeDiff",props:{newString:{},oldString:{},language:{default:"plaintext"},context:{default:10},diffStyle:{default:"word"},forceInlineComparison:{type:Boolean,default:!1},outputFormat:{default:"line-by-line"},trim:{type:Boolean,default:!1},noDiffLineFeed:{type:Boolean,default:!1},maxHeight:{default:void 0},filename:{default:void 0},newFilename:{default:void 0},hideHeader:{type:Boolean,default:!1},hideStat:{type:Boolean,default:!1},theme:{default:"light"},ignoreMatchingLines:{default:void 0}},emits:["diff"],setup(e,{emit:r}){const c=e,f=r,g=ve(()=>c.outputFormat==="line-by-line"),t=ve(()=>{let h=c.oldString||"";return h=c.trim?h.trim():h,h=c.noDiffLineFeed?h.replace(/(\r\n)/g,`
`):h,h}),o=ve(()=>{let h=c.newString||"";return h=c.trim?h.trim():h,h=c.noDiffLineFeed?h.replace(/(\r\n)/g,`
`):h,h}),n=ve(()=>g.value?fr(t.value,o.value,c.language,c.diffStyle,c.forceInlineComparison,c.context,c.ignoreMatchingLines):dr(t.value,o.value,c.language,c.diffStyle,c.forceInlineComparison,c.context,c.ignoreMatchingLines)),a=tt(n.value),i=ve(()=>a.value.stat.additionsNum===0&&a.value.stat.deletionsNum===0),l=tt(-1);function s(){const h=document.querySelectorAll(".blob-code-addition");l.value<h.length-1&&(l.value++,b(h))}function d(){const h=document.querySelectorAll(".blob-code-addition");l.value>0&&(l.value--,b(h))}function b(h){h.forEach(m=>m.classList.remove("current-diff"));const u=h[l.value];u&&(u.classList.add("current-diff"),u.scrollIntoView({behavior:"smooth",block:"center"}))}return qt(()=>c,()=>{a.value=n.value,f("diff",{stat:{isChanged:!i.value,addNum:a.value.stat.additionsNum,delNum:a.value.stat.deletionsNum}})},{deep:!0,immediate:!0}),(h,u)=>(F(),Z("div",{class:"code-diff-view",theme:h.theme,style:Zt({maxHeight:h.maxHeight})},[h.hideHeader?ye("",!0):(F(),Z("div",Ur,[z(g)?(F(),Z("div",Fr,[T("span",null,[T("div",Hr,V(h.filename),1),T("div",qr,V(h.newFilename),1)]),T("span",Zr,[T("button",{class:"command-item-button",title:"Next Change",onClick:s},[je(bt)]),T("button",{class:"command-item-button",title:"Previous Change",onClick:d},[je(ut)])]),h.hideStat?ye("",!0):(F(),Z("span",Gr,[ot(h.$slots,"stat",{stat:z(a).stat},()=>[T("span",Kr,"+"+V(z(a).stat.additionsNum)+" additions",1),T("span",Qr,"-"+V(z(a).stat.deletionsNum)+" deletions",1)])]))])):(F(),Z("div",Xr,[T("span",Wr,V(h.filename),1),T("span",Jr,[T("span",Vr,V(h.newFilename),1),T("span",Yr,[T("button",{class:"command-item-button",title:"Next Change",onClick:s},[je(bt)]),T("button",{class:"command-item-button",title:"Previous Change",onClick:d},[je(ut)])]),h.hideStat?ye("",!0):(F(),Z("span",en,[ot(h.$slots,"stat",{stat:z(a).stat},()=>[T("span",tn,"+"+V(z(a).stat.additionsNum)+" additions",1),T("span",on,"-"+V(z(a).stat.deletionsNum)+" deletions",1)])]))])]))])),z(g)?(F(),Oe(vr,{key:1,"diff-change":z(a)},null,8,["diff-change"])):(F(),Oe(Dr,{key:2,"diff-change":z(a)},null,8,["diff-change"]))],12,zr))}});export{nn as U};
