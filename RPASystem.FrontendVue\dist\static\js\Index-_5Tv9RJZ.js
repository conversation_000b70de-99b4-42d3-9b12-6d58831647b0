import{Q as h,_ as te,r as c,E as oe,M as ae,$ as ne,a1 as se,a2 as le,R as re,x as u,N as ie,o as w,c as T,j as d,i as o,l as n,D as s,t as m,k as ue,a3 as pe,O as ce,p as L,F as P,K as B,V as de,a4 as me,q as fe,s as ve,P as C,H as _e}from"./index-CX4J5aM5.js";import ge from"./RemoteDesktop-_Tsd8UXn.js";import ye from"./ResourceMachineConfig-DKEgoK-J.js";import"./SystemConfigManager-BBI_j5TY.js";function be(p){const g={...p};return p.taskStatusFilter&&p.taskStatusFilter.length>0?h.get("/api/resourcemachine",{params:g,paramsSerializer:_=>{const y=[];for(const f in _)Array.isArray(_[f])?_[f].forEach(k=>{y.push(`${encodeURIComponent(f)}=${encodeURIComponent(k)}`)}):y.push(`${encodeURIComponent(f)}=${encodeURIComponent(_[f])}`);return y.join("&")}}):h.get("/api/resourcemachine",{params:g})}function we(p){return h.delete(`/api/resourcemachine/${p}`)}function he(p,g){return h.put(`/api/resourcemachine/${p}/type`,g,{headers:{"Content-Type":"application/json"}})}function ke(){return h.get("/api/update/version")}function Ve(p){return h.post("/api/update/upload",p,{headers:{"Content-Type":"multipart/form-data"}})}const Ce={class:"header-section"},Ue={class:"filter-section"},Se={class:"filter-section"},Te={class:"version-update"},xe={class:"version-text"},Me={class:"clickable-type"},Re={class:"type-options"},$e=["onClick"],De={class:"operation-buttons"},Ie=ve({name:"resourcemachinemanagement"}),ze=Object.assign(Ie,{setup(p){const g=c([]),_=c(""),y=c(""),f=c(null),k=c(!1),x=c(!1),M=c([]),R=c(!1),$=c(!1),D=c(0);let V;const v=oe({pageIndex:1,pageSize:10,searchTerm:"",taskStatusFilter:[],offlineOverSevenDays:!1}),b=async()=>{$.value=!0;try{v.searchTerm=_.value,v.taskStatusFilter=M.value,v.offlineOverSevenDays=R.value;const a=await be(v);let e=a.data.items.map(l=>({...l,diskUsage:l.diskUsage||"{}"}));g.value=e,D.value=a.data.total}catch(a){console.error("获取资源机列表失败:",a),C.error("获取资源机列表失败，请重试")}finally{$.value=!1}},F=async()=>{try{const a=await ke();y.value=a.data.version}catch(a){console.error("获取最新版本失败:",a)}},H=async a=>{try{const e=new FormData;e.append("file",a.file);const l=await Ve(e);C.success("更新包上传成功"),y.value=l.data.version}catch(e){C.error("更新包上传失败："+e.message)}},U=()=>{v.pageIndex=1,b()},E=async a=>{try{await we(a),b()}catch(e){console.error("删除资源机失败:",e)}},O=a=>({0:"空闲",1:"任务运行中",2:"离线"})[a]||"未知状态",A=a=>({0:"success",1:"warning",2:"info"})[a]||"info",j=a=>({0:"在线执行机",1:"服务机",2:"普通机"})[a]||"未知类型",q=a=>{f.value=a,k.value=!0},J=a=>{_e.confirm("确定要删除该资源机吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{E(a)}).catch(()=>{})},K=async(a,e)=>{const l={在线执行机:0,服务机:1,普通机:2};try{a._popoverVisible=!1,await he(a.id,l[e]),a.machineType=l[e],C.success("资源机类型更新成功")}catch{C.error("资源机类型更新失败"),await b()}};return ae(async()=>{await b(),await F(),V=new ne().withUrl("/resourceMachineHub").withHubProtocol(new se).withAutomaticReconnect().configureLogging(le.Information).build(),V.on("RefreshResourceMachines",()=>{b()});try{await V.start()}catch(a){console.error("SignalR Connection Error: ",a)}}),re(()=>{V&&V.stop()}),(a,e)=>{const l=u("el-button"),z=u("el-input"),S=u("el-checkbox-button"),Q=u("el-checkbox-group"),G=u("el-upload"),W=u("el-icon"),r=u("el-table-column"),X=u("el-popover"),N=u("el-tag"),Y=u("el-table"),Z=u("el-dialog"),ee=ie("loading");return w(),T("div",null,[d("div",Ce,[o(z,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=t=>_.value=t),placeholder:"搜索资源机名",onInput:U,style:{width:"300px"}},{append:n(()=>[o(l,{onClick:U},{default:n(()=>e[9]||(e[9]=[s("搜索")])),_:1})]),_:1},8,["modelValue"]),d("div",Ue,[e[13]||(e[13]=d("span",{class:"filter-label"},"任务状态：",-1)),o(Q,{modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=t=>M.value=t),onChange:U},{default:n(()=>[o(S,{label:"0"},{default:n(()=>e[10]||(e[10]=[s("空闲")])),_:1}),o(S,{label:"1"},{default:n(()=>e[11]||(e[11]=[s("任务运行中")])),_:1}),o(S,{label:"2"},{default:n(()=>e[12]||(e[12]=[s("离线")])),_:1})]),_:1},8,["modelValue"])]),d("div",Se,[o(S,{modelValue:R.value,"onUpdate:modelValue":e[2]||(e[2]=t=>R.value=t),onChange:U},{default:n(()=>e[14]||(e[14]=[s("离线超过7天")])),_:1},8,["modelValue"])]),d("div",Te,[d("span",xe,"最新版本："+m(y.value||"未发布"),1),o(G,{class:"upload-demo","http-request":H,"show-file-list":!1,accept:".zip"},{default:n(()=>[o(l,{type:"primary"},{default:n(()=>e[15]||(e[15]=[s("上传新版本")])),_:1})]),_:1}),o(l,{type:"primary",onClick:e[3]||(e[3]=t=>x.value=!0)},{default:n(()=>[o(W,null,{default:n(()=>[o(ue(pe))]),_:1}),e[16]||(e[16]=s(" 配置 "))]),_:1})])]),ce((w(),L(Y,{data:g.value,style:{width:"100%"},border:""},{default:n(()=>[o(r,{prop:"machineName",label:"资源机名"}),o(r,{prop:"computerName",label:"计算机名"}),o(r,{label:"资源机类型",width:"120"},{default:n(t=>[o(X,{placement:"bottom",width:120,trigger:"click",visible:t.row._popoverVisible,"onUpdate:visible":i=>t.row._popoverVisible=i,onShow:i=>t.row._typeTemp=t.row.machineType,onHide:i=>t.row._typeTemp=t.row.machineType},{reference:n(()=>[d("span",Me,m(j(t.row.machineType)),1)]),default:n(()=>[d("div",Re,[(w(),T(P,null,B(["在线执行机","服务机","普通机"],i=>d("div",{key:i,class:de(["type-option",{selected:i===t.row._typeTemp}]),onClick:I=>K(t.row,i)},m(i),11,$e)),64))])]),_:2},1032,["visible","onUpdate:visible","onShow","onHide"])]),_:1}),o(r,{prop:"ipAddress",label:"IP地址"}),o(r,{prop:"clientVersion",label:"客户端版本"}),o(r,{prop:"isLatestVersion",label:"最新版本",width:"100"},{default:n(t=>[o(N,{type:t.row.isLatestVersion?"success":"danger",size:"small"},{default:n(()=>[s(m(t.row.isLatestVersion?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),o(r,{prop:"lastActivityTime",label:"最后活动时间"}),o(r,{prop:"taskStatus",label:"运行状态",width:"120"},{default:n(t=>[o(N,{type:A(t.row.taskStatus),size:"small"},{default:n(()=>[s(m(O(t.row.taskStatus)),1)]),_:2},1032,["type"])]),_:1}),o(r,{prop:"runningTaskCount",label:"运行任务数",width:"100"},{default:n(t=>[s(m(t.row.runningTaskCount),1)]),_:1}),o(r,{prop:"cpuUsage",label:"CPU使用率"},{default:n(t=>[s(m(t.row.cpuUsage)+"% ",1)]),_:1}),o(r,{prop:"memoryUsage",label:"内存使用率"},{default:n(t=>[s(m(t.row.memoryUsage)+"% ",1)]),_:1}),o(r,{label:"磁盘使用率"},{default:n(t=>[(w(!0),T(P,null,B(JSON.parse(t.row.diskUsage),(i,I)=>(w(),T("div",{key:I},m(I)+" "+m(i),1))),128))]),_:1}),o(r,{label:"操作",width:"180"},{default:n(t=>[d("div",De,[o(l,{type:"primary",size:"small",onClick:i=>q(t.row)},{default:n(()=>e[17]||(e[17]=[s("远程桌面")])),_:2},1032,["onClick"]),o(l,{type:"danger",size:"small",onClick:i=>J(t.row.id)},{default:n(()=>e[18]||(e[18]=[s("删除")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ee,$.value]]),o(me,{total:D.value,"onUpdate:total":e[4]||(e[4]=t=>D.value=t),page:v.pageIndex,"onUpdate:page":e[5]||(e[5]=t=>v.pageIndex=t),limit:v.pageSize,"onUpdate:limit":e[6]||(e[6]=t=>v.pageSize=t),onPagination:b},null,8,["total","page","limit"]),f.value?(w(),L(ge,{key:0,visible:k.value,"onUpdate:visible":e[7]||(e[7]=t=>k.value=t),"machine-name":f.value.machineName},null,8,["visible","machine-name"])):fe("",!0),o(Z,{modelValue:x.value,"onUpdate:modelValue":e[8]||(e[8]=t=>x.value=t),title:"资源机配置",width:"800px","destroy-on-close":""},{default:n(()=>[o(ye)]),_:1},8,["modelValue"])])}}}),Fe=te(ze,[["__scopeId","data-v-a296e9c6"]]);export{Fe as default};
