import{i as o}from"./index-BtiuLXK9.js";import{_ as n,r as c,M as i,o as l,c as u}from"./index-CX4J5aM5.js";const f={__name:"simpleGauge",setup(p){const r=c(null),a={tooltip:{formatter:"{a} <br/>{b} : {c}%"},title:{text:"空气指标",textStyle:{color:"#00e4ff"}},series:[{name:"Pressure",type:"gauge",progress:{show:!0},detail:{valueAnimation:!0,formatter:"{value}"},data:[{value:50,name:"SCORE"}]}]};let e=null;const s=()=>{const t=o(r.value);return t.setOption(a),t};return i(()=>{e=s(),window.addEventListener("resize",function(){e&&e.resize()})}),(t,m)=>(l(),u("div",{class:"echarts",ref_key:"chartsRef",ref:r},null,512))}},h=n(f,[["__scopeId","data-v-1c584f59"]]);export{h as default};
