using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RPASystem.Client.Installer
{
    internal static class Program
    {
        private static readonly string[] InstallPaths = {
            @"D:\Program Files\RPASystem.ClientWin\RPASystem.ClientWin.exe",
            @"C:\Program Files\RPASystem.ClientWin\RPASystem.ClientWin.exe"
        };

        private static string defaultServerIP = "*************";

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static async Task Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                await MainAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"安装过程中发生错误：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task MainAsync()
        {
            // 1. 检测已安装程序
            string existingPath = CheckExistingInstallation();
            if (!string.IsNullOrEmpty(existingPath))
            {
                StartClientProgram(existingPath);
                return;
            }

            // 2. 下载安装包
            byte[] zipData = await DownloadInstallPackage();
            if (zipData == null)
            {
                return; // 用户取消或下载失败
            }

            // 3. 安装部署
            string installPath = GetInstallPath();
            InstallProgram(zipData, installPath);

            // 4. 启动程序
            StartClientProgram(installPath);
        }

        /// <summary>
        /// 检测已安装的程序
        /// </summary>
        private static string CheckExistingInstallation()
        {
            foreach (string path in InstallPaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }
            return null;
        }

        /// <summary>
        /// 下载安装包
        /// </summary>
        private static async Task<byte[]> DownloadInstallPackage()
        {
            string serverIP = defaultServerIP;

            while (true)
            {
                try
                {
                    string downloadUrl = $"http://{serverIP}:8888/api/update/download";

                    using (var httpClient = new HttpClient())
                    {
                        //httpClient.Timeout = TimeSpan.FromMinutes(10);
                        httpClient.Timeout = TimeSpan.FromSeconds(10);
                        var response = await httpClient.GetAsync(downloadUrl);

                        if (response.IsSuccessStatusCode)
                        {
                            return await response.Content.ReadAsByteArrayAsync();
                        }
                    }
                }
                catch (Exception)
                {
                    // 下载失败，继续到IP输入对话框
                }

                // 弹出IP输入对话框
                string newIP = ShowIPInputDialog(serverIP);
                if (string.IsNullOrEmpty(newIP))
                {
                    return null; // 用户取消
                }

                serverIP = newIP;
            }
        }

        /// <summary>
        /// 显示IP输入对话框
        /// </summary>
        private static string ShowIPInputDialog(string currentIP)
        {
            Form inputForm = new Form()
            {
                Width = 350,
                Height = 150,
                Text = "RPA大助手 - 服务器设置",
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label label = new Label()
            {
                Left = 20,
                Top = 20,
                Width = 280,
                Text = $"无法连接到服务器 {currentIP}，请输入新的服务器IP地址："
            };

            TextBox textBox = new TextBox()
            {
                Left = 20,
                Top = 50,
                Width = 200,
                Text = currentIP
            };

            Button okButton = new Button()
            {
                Text = "确定",
                Left = 230,
                Top = 48,
                Width = 80,
                DialogResult = DialogResult.OK
            };

            Button cancelButton = new Button()
            {
                Text = "取消",
                Left = 230,
                Top = 78,
                Width = 80,
                DialogResult = DialogResult.Cancel
            };

            inputForm.Controls.Add(label);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(okButton);
            inputForm.Controls.Add(cancelButton);

            inputForm.AcceptButton = okButton;
            inputForm.CancelButton = cancelButton;

            if (inputForm.ShowDialog() == DialogResult.OK)
            {
                return textBox.Text.Trim();
            }

            return null;
        }

        /// <summary>
        /// 获取安装路径
        /// </summary>
        private static string GetInstallPath()
        {
            // 检查D盘是否存在
            if (Directory.Exists(@"D:\"))
            {
                return InstallPaths[0];
            }
            else
            {
                return InstallPaths[1];
            }
        }

        /// <summary>
        /// 安装程序
        /// </summary>
        private static void InstallProgram(byte[] zipData, string targetExePath)
        {
            string installDir = Path.GetDirectoryName(targetExePath);

            try
            {
                // 确保安装目录存在
                Directory.CreateDirectory(installDir);

                // 如果目标目录已存在文件，先清空
                if (Directory.Exists(installDir))
                {
                    foreach (string file in Directory.GetFiles(installDir, "*", SearchOption.AllDirectories))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // 忽略删除失败的文件
                        }
                    }
                }

                // 创建临时zip文件
                string tempZipPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".zip");
                File.WriteAllBytes(tempZipPath, zipData);

                // 解压到安装目录
                ZipFile.ExtractToDirectory(tempZipPath, installDir);

                // 删除临时文件
                File.Delete(tempZipPath);
            }
            catch (Exception ex)
            {
                throw new Exception($"安装失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 启动客户端程序
        /// </summary>
        private static void StartClientProgram(string exePath)
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    //Arguments = "-hide", // 隐藏启动
                    UseShellExecute = false,
                    WorkingDirectory = Path.GetDirectoryName(exePath)
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动程序失败：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
