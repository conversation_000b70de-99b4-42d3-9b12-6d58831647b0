using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Linq;

namespace RPASystem.Client.Installer
{
    internal static class Program
    {
        private static readonly string BaseInstallPath = @"C:\Program Files\RPASystem.ClientWin";
        private static string defaultServerIP = "*************";

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static async Task Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                await MainAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"安装过程中发生错误：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task MainAsync()
        {
            // 1. 检测已安装程序，找版本最大的且有EXE存在的
            string existingPath = CheckExistingInstallation();
            if (!string.IsNullOrEmpty(existingPath))
            {
                // 清理其他版本目录
                CleanupOtherVersions(existingPath);
                StartClientProgram(existingPath);
                return;
            }

            // 2. 下载安装包
            byte[] zipData = await DownloadInstallPackage();
            if (zipData == null)
            {
                return; // 用户取消或下载失败
            }

            // 3. 获取版本号并安装部署
            string version = GetVersionFromZip(zipData);
            string installPath = GetInstallPath(version);
            InstallProgram(zipData, installPath);

            // 4. 启动程序
            StartClientProgram(installPath);
        }

        /// <summary>
        /// 检测已安装的程序，找版本最大的且有EXE存在的
        /// </summary>
        private static string CheckExistingInstallation()
        {
            if (!Directory.Exists(BaseInstallPath))
            {
                return null;
            }

            // 获取所有版本目录
            var versionDirs = Directory.GetDirectories(BaseInstallPath)
                .Select(dir => new DirectoryInfo(dir).Name)
                .Where(name => IsValidVersion(name))
                .OrderByDescending(version => new Version(version))
                .ToList();

            // 找到版本最大的且有EXE存在的
            foreach (string version in versionDirs)
            {
                string exePath = Path.Combine(BaseInstallPath, version, "RPASystem.ClientWin.exe");
                if (File.Exists(exePath))
                {
                    return exePath;
                }
            }

            return null;
        }

        /// <summary>
        /// 验证版本号格式是否有效
        /// </summary>
        private static bool IsValidVersion(string version)
        {
            try
            {
                new Version(version);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 清理其他版本目录，只保留当前版本
        /// </summary>
        private static void CleanupOtherVersions(string currentExePath)
        {
            try
            {
                if (!Directory.Exists(BaseInstallPath))
                    return;

                // 获取当前版本目录
                string currentVersionDir = Path.GetDirectoryName(currentExePath);
                string currentVersion = Path.GetFileName(currentVersionDir);

                // 获取所有版本目录
                var allVersionDirs = Directory.GetDirectories(BaseInstallPath);

                foreach (string versionDir in allVersionDirs)
                {
                    string versionName = Path.GetFileName(versionDir);
                    // 删除除当前版本外的所有目录
                    if (versionName != currentVersion)
                    {
                        try
                        {
                            Directory.Delete(versionDir, true);
                        }
                        catch
                        {
                            // 忽略删除失败的情况
                        }
                    }
                }
            }
            catch
            {
                // 忽略清理过程中的错误
            }
        }

        /// <summary>
        /// 下载安装包
        /// </summary>
        private static async Task<byte[]> DownloadInstallPackage()
        {
            string serverIP = defaultServerIP;

            while (true)
            {
                try
                {
                    string downloadUrl = $"http://{serverIP}:8888/api/update/download";

                    using (var httpClient = new HttpClient())
                    {
                        //httpClient.Timeout = TimeSpan.FromMinutes(10);
                        httpClient.Timeout = TimeSpan.FromSeconds(10);
                        var response = await httpClient.GetAsync(downloadUrl);

                        if (response.IsSuccessStatusCode)
                        {
                            return await response.Content.ReadAsByteArrayAsync();
                        }
                    }
                }
                catch (Exception)
                {
                    // 下载失败，继续到IP输入对话框
                }

                // 弹出IP输入对话框
                string newIP = ShowIPInputDialog(serverIP);
                if (string.IsNullOrEmpty(newIP))
                {
                    return null; // 用户取消
                }

                serverIP = newIP;
            }
        }

        /// <summary>
        /// 显示IP输入对话框
        /// </summary>
        private static string ShowIPInputDialog(string currentIP)
        {
            Form inputForm = new Form()
            {
                Width = 350,
                Height = 150,
                Text = "RPA大助手 - 服务器设置",
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label label = new Label()
            {
                Left = 20,
                Top = 20,
                Width = 280,
                Text = $"无法连接到服务器 {currentIP}，请输入新的服务器IP地址："
            };

            TextBox textBox = new TextBox()
            {
                Left = 20,
                Top = 50,
                Width = 200,
                Text = currentIP
            };

            Button okButton = new Button()
            {
                Text = "确定",
                Left = 230,
                Top = 48,
                Width = 80,
                DialogResult = DialogResult.OK
            };

            Button cancelButton = new Button()
            {
                Text = "取消",
                Left = 230,
                Top = 78,
                Width = 80,
                DialogResult = DialogResult.Cancel
            };

            inputForm.Controls.Add(label);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(okButton);
            inputForm.Controls.Add(cancelButton);

            inputForm.AcceptButton = okButton;
            inputForm.CancelButton = cancelButton;

            if (inputForm.ShowDialog() == DialogResult.OK)
            {
                return textBox.Text.Trim();
            }

            return null;
        }

        /// <summary>
        /// 从ZIP包中获取版本号
        /// </summary>
        private static string GetVersionFromZip(byte[] zipData)
        {
            // 创建临时zip文件
            string tempZipPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".zip");
            try
            {
                
                File.WriteAllBytes(tempZipPath, zipData);

                using (var archive = ZipFile.OpenRead(tempZipPath))
                {
                    // 查找Version.dat文件
                    var versionEntry = archive.Entries.FirstOrDefault(e =>
                        e.Name.Equals("Version.dat", StringComparison.OrdinalIgnoreCase));

                    if (versionEntry != null)
                    {
                        using (var reader = new StreamReader(versionEntry.Open()))
                        {
                            string version = reader.ReadToEnd().Trim();
                            
                            return version;
                        }
                    }
                }

                File.Delete(tempZipPath);
            }
            catch
            {
                // 如果无法获取版本号，使用默认版本
            }
            finally
            {
                File.Delete(tempZipPath);
            }

            return "*******";
        }

        /// <summary>
        /// 获取安装路径
        /// </summary>
        private static string GetInstallPath(string version)
        {
            return Path.Combine(BaseInstallPath, version, "RPASystem.ClientWin.exe");
        }

        /// <summary>
        /// 安装程序
        /// </summary>
        private static void InstallProgram(byte[] zipData, string targetExePath)
        {
            string installDir = Path.GetDirectoryName(targetExePath);

            try
            {
                // 确保安装目录存在
                Directory.CreateDirectory(installDir);

                // 如果目标目录已存在文件，先清空
                if (Directory.Exists(installDir))
                {
                    foreach (string file in Directory.GetFiles(installDir, "*", SearchOption.AllDirectories))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // 忽略删除失败的文件
                        }
                    }
                }

                // 创建临时zip文件
                string tempZipPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".zip");
                File.WriteAllBytes(tempZipPath, zipData);

                // 解压到安装目录
                ZipFile.ExtractToDirectory(tempZipPath, installDir);

                // 删除临时文件
                File.Delete(tempZipPath);
            }
            catch (Exception ex)
            {
                throw new Exception($"安装失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 启动客户端程序
        /// </summary>
        private static void StartClientProgram(string exePath)
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    //Arguments = "-hide", // 隐藏启动
                    UseShellExecute = false,
                    WorkingDirectory = Path.GetDirectoryName(exePath)
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动程序失败：{ex.Message}", "RPA大助手", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
