import{g as ie,_ as ne,b as se,d as oe,r as ut,n as Mt,e as he,w as Ft,f as Z,h as ae,c as qt,i as M,j as ct,t as Zt,k as R,l as Y,m as ue,p as At,q as Rt,F as ce,s as le,a as fe,u as pe,v as ge,x as et,o as rt,y as _t,L as de,z as me,A as ve,B as ye}from"./index-CX4J5aM5.js";import be from"./starBackground-BMTLJT3n.js";import Te from"./oauthLogin-Bj3K6qfP.js";import Se from"./phoneLogin-BOAIalaB.js";import Ee from"./qrLogin-CCXAsBm4.js";import"./qrcode-CxxOwKMC.js";var Lt={exports:{}};/*! For license information please see jsencrypt.min.js.LICENSE.txt */var zt;function we(){return zt||(zt=1,function(it,k){(function(mt,N){it.exports=N()})(window,function(){return(()=>{var mt=[,(K,V,T)=>{function q(i){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(i)}function z(i,t){return i&t}function nt(i,t){return i|t}function W(i,t){return i^t}function st(i,t){return i&~t}function lt(i){if(i==0)return-1;var t=0;return!(65535&i)&&(i>>=16,t+=16),!(255&i)&&(i>>=8,t+=8),!(15&i)&&(i>>=4,t+=4),!(3&i)&&(i>>=2,t+=2),!(1&i)&&++t,t}function ft(i){for(var t=0;i!=0;)i&=i-1,++t;return t}T.d(V,{default:()=>ee});var U,Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function ot(i){var t,e,r="";for(t=0;t+3<=i.length;t+=3)e=parseInt(i.substring(t,t+3),16),r+=Q.charAt(e>>6)+Q.charAt(63&e);for(t+1==i.length?(e=parseInt(i.substring(t,t+1),16),r+=Q.charAt(e<<2)):t+2==i.length&&(e=parseInt(i.substring(t,t+2),16),r+=Q.charAt(e>>2)+Q.charAt((3&e)<<4));(3&r.length)>0;)r+="=";return r}function yt(i){var t,e="",r=0,n=0;for(t=0;t<i.length&&i.charAt(t)!="=";++t){var s=Q.indexOf(i.charAt(t));s<0||(r==0?(e+=q(s>>2),n=3&s,r=1):r==1?(e+=q(n<<2|s>>4),n=15&s,r=2):r==2?(e+=q(n),e+=q(s>>2),n=3&s,r=3):(e+=q(n<<2|s>>4),e+=q(15&s),r=0))}return r==1&&(e+=q(n<<2)),e}var m,x={decode:function(i){var t;if(m===void 0){var e=`= \f
\r	 \u2028\u2029`;for(m=Object.create(null),t=0;t<64;++t)m["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(m["-"]=62,m._=63,t=0;t<e.length;++t)m[e.charAt(t)]=-1}var r=[],n=0,s=0;for(t=0;t<i.length;++t){var o=i.charAt(t);if(o=="=")break;if((o=m[o])!=-1){if(o===void 0)throw new Error("Illegal character at offset "+t);n|=o,++s>=4?(r[r.length]=n>>16,r[r.length]=n>>8&255,r[r.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=n>>10;break;case 3:r[r.length]=n>>16,r[r.length]=n>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(i){var t=x.re.exec(i);if(t)if(t[1])i=t[1];else{if(!t[2])throw new Error("RegExp out of sync");i=t[2]}return x.decode(i)}},O=1e13,j=function(){function i(t){this.buf=[+t||0]}return i.prototype.mulAdd=function(t,e){var r,n,s=this.buf,o=s.length;for(r=0;r<o;++r)(n=s[r]*t+e)<O?e=0:n-=(e=0|n/O)*O,s[r]=n;e>0&&(s[r]=e)},i.prototype.sub=function(t){var e,r,n=this.buf,s=n.length;for(e=0;e<s;++e)(r=n[e]-t)<0?(r+=O,t=1):t=0,n[e]=r;for(;n[n.length-1]===0;)n.pop()},i.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;n>=0;--n)r+=(O+e[n]).toString().substring(1);return r},i.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*O+t[r];return e},i.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},i}(),$=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,bt=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function C(i,t){return i.length>t&&(i=i.substring(0,t)+"…"),i}var H,pt=function(){function i(t,e){this.hexDigits="0123456789ABCDEF",t instanceof i?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}return i.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},i.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},i.prototype.hexDump=function(t,e,r){for(var n="",s=t;s<e;++s)if(n+=this.hexByte(this.get(s)),r!==!0)switch(15&s){case 7:n+="  ";break;case 15:n+=`
`;break;default:n+=" "}return n},i.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0},i.prototype.parseStringISO=function(t,e){for(var r="",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},i.prototype.parseStringUTF=function(t,e){for(var r="",n=t;n<e;){var s=this.get(n++);r+=s<128?String.fromCharCode(s):s>191&&s<224?String.fromCharCode((31&s)<<6|63&this.get(n++)):String.fromCharCode((15&s)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},i.prototype.parseStringBMP=function(t,e){for(var r,n,s="",o=t;o<e;)r=this.get(o++),n=this.get(o++),s+=String.fromCharCode(r<<8|n);return s},i.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),s=(r?$:bt).exec(n);return s?(r&&(s[1]=+s[1],s[1]+=+s[1]<70?2e3:1900),n=s[1]+"-"+s[2]+"-"+s[3]+" "+s[4],s[5]&&(n+=":"+s[5],s[6]&&(n+=":"+s[6],s[7]&&(n+="."+s[7]))),s[8]&&(n+=" UTC",s[8]!="Z"&&(n+=s[8],s[9]&&(n+=":"+s[9]))),n):"Unrecognized time: "+n},i.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),s=n>127,o=s?255:0,h="";n==o&&++t<e;)n=this.get(t);if((r=e-t)==0)return s?-1:0;if(r>4){for(h=n,r<<=3;!(128&(+h^o));)h=+h<<1,--r;h="("+r+` bit)
`}s&&(n-=256);for(var a=new j(n),c=t+1;c<e;++c)a.mulAdd(256,this.get(c));return h+a.toString()},i.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),s="("+((e-t-1<<3)-n)+` bit)
`,o="",h=t+1;h<e;++h){for(var a=this.get(h),c=h==e-1?n:0,l=7;l>=c;--l)o+=a>>l&1?"1":"0";if(o.length>r)return s+C(o,r)}return s+o},i.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return C(this.parseStringISO(t,e),r);var n=e-t,s="("+n+` byte)
`;n>(r/=2)&&(e=t+r);for(var o=t;o<e;++o)s+=this.hexByte(this.get(o));return n>r&&(s+="…"),s},i.prototype.parseOID=function(t,e,r){for(var n="",s=new j,o=0,h=t;h<e;++h){var a=this.get(h);if(s.mulAdd(128,127&a),o+=7,!(128&a)){if(n==="")if((s=s.simplify())instanceof j)s.sub(80),n="2."+s.toString();else{var c=s<80?s<40?0:1:2;n=c+"."+(s-40*c)}else n+="."+s.toString();if(n.length>r)return C(n,r);s=new j,o=0}}return o>0&&(n+=".incomplete"),n},i}(),X=function(){function i(t,e,r,n,s){if(!(n instanceof jt))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=s}return i.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},i.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return this.stream.get(e)===0?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return C(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return C(this.stream.parseStringISO(e,e+r),t);case 30:return C(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,this.tag.tagNumber==23)}return null},i.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},i.prototype.toPrettyString=function(t){t===void 0&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||this.tag.tagNumber!=3&&this.tag.tagNumber!=4||this.sub===null||(e+=" (encapsulates)"),e+=`
`,this.sub!==null){t+="  ";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},i.prototype.posStart=function(){return this.stream.pos},i.prototype.posContent=function(){return this.stream.pos+this.header},i.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},i.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},i.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(r===0)return null;e=0;for(var n=0;n<r;++n)e=256*e+t.get();return e},i.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},i.decode=function(t){var e;e=t instanceof pt?t:new pt(t,0);var r=new pt(e),n=new jt(e),s=i.decodeLength(e),o=e.pos,h=o-r.pos,a=null,c=function(){var v=[];if(s!==null){for(var g=o+s;e.pos<g;)v[v.length]=i.decode(e);if(e.pos!=g)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var S=i.decode(e);if(S.tag.isEOC())break;v[v.length]=S}s=o-e.pos}catch(b){throw new Error("Exception while decoding undefined length content: "+b)}return v};if(n.tagConstructed)a=c();else if(n.isUniversal()&&(n.tagNumber==3||n.tagNumber==4))try{if(n.tagNumber==3&&e.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=c();for(var l=0;l<a.length;++l)if(a[l].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{a=null}if(a===null){if(s===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);e.pos=o+Math.abs(s)}return new i(r,h,s,n,a)},i}(),jt=function(){function i(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=(32&e)!=0,this.tagNumber=31&e,this.tagNumber==31){var r=new j;do e=t.get(),r.mulAdd(128,127&e);while(128&e);this.tagNumber=r.simplify()}}return i.prototype.isUniversal=function(){return this.tagClass===0},i.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},i}(),B=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],$t=(1<<26)/B[B.length-1],p=function(){function i(t,e,r){t!=null&&(typeof t=="number"?this.fromNumber(t,e,r):e==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,e))}return i.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(t==16)e=4;else if(t==8)e=3;else if(t==2)e=1;else if(t==32)e=5;else{if(t!=4)return this.toRadix(t);e=2}var r,n=(1<<e)-1,s=!1,o="",h=this.t,a=this.DB-h*this.DB%e;if(h-- >0)for(a<this.DB&&(r=this[h]>>a)>0&&(s=!0,o=q(r));h>=0;)a<e?(r=(this[h]&(1<<a)-1)<<e-a,r|=this[--h]>>(a+=this.DB-e)):(r=this[h]>>(a-=e)&n,a<=0&&(a+=this.DB,--h)),r>0&&(s=!0),s&&(o+=q(r));return s?o:"0"},i.prototype.negate=function(){var t=d();return i.ZERO.subTo(this,t),t},i.prototype.abs=function(){return this.s<0?this.negate():this},i.prototype.compareTo=function(t){var e=this.s-t.s;if(e!=0)return e;var r=this.t;if((e=r-t.t)!=0)return this.s<0?-e:e;for(;--r>=0;)if((e=this[r]-t[r])!=0)return e;return 0},i.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+St(this[this.t-1]^this.s&this.DM)},i.prototype.mod=function(t){var e=d();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(i.ZERO)>0&&t.subTo(e,e),e},i.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new Ct(e):new Ht(e),this.exp(t,r)},i.prototype.clone=function(){var t=d();return this.copyTo(t),t},i.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},i.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},i.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},i.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},i.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,s=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[s++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),128&r&&(r|=-256),s==0&&(128&this.s)!=(128&r)&&++s,(s>0||r!=this.s)&&(e[s++]=r);return e},i.prototype.equals=function(t){return this.compareTo(t)==0},i.prototype.min=function(t){return this.compareTo(t)<0?this:t},i.prototype.max=function(t){return this.compareTo(t)>0?this:t},i.prototype.and=function(t){var e=d();return this.bitwiseTo(t,z,e),e},i.prototype.or=function(t){var e=d();return this.bitwiseTo(t,nt,e),e},i.prototype.xor=function(t){var e=d();return this.bitwiseTo(t,W,e),e},i.prototype.andNot=function(t){var e=d();return this.bitwiseTo(t,st,e),e},i.prototype.not=function(){for(var t=d(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},i.prototype.shiftLeft=function(t){var e=d();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},i.prototype.shiftRight=function(t){var e=d();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},i.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+lt(this[t]);return this.s<0?this.t*this.DB:-1},i.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=ft(this[r]^e);return t},i.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?this.s!=0:(this[e]&1<<t%this.DB)!=0},i.prototype.setBit=function(t){return this.changeBit(t,nt)},i.prototype.clearBit=function(t){return this.changeBit(t,st)},i.prototype.flipBit=function(t){return this.changeBit(t,W)},i.prototype.add=function(t){var e=d();return this.addTo(t,e),e},i.prototype.subtract=function(t){var e=d();return this.subTo(t,e),e},i.prototype.multiply=function(t){var e=d();return this.multiplyTo(t,e),e},i.prototype.divide=function(t){var e=d();return this.divRemTo(t,e,null),e},i.prototype.remainder=function(t){var e=d();return this.divRemTo(t,null,e),e},i.prototype.divideAndRemainder=function(t){var e=d(),r=d();return this.divRemTo(t,e,r),[e,r]},i.prototype.modPow=function(t,e){var r,n,s=t.bitLength(),o=G(1);if(s<=0)return o;r=s<18?1:s<48?3:s<144?4:s<768?5:6,n=s<8?new Ct(e):e.isEven()?new Jt(e):new Ht(e);var h=[],a=3,c=r-1,l=(1<<r)-1;if(h[1]=n.convert(this),r>1){var v=d();for(n.sqrTo(h[1],v);a<=l;)h[a]=d(),n.mulTo(v,h[a-2],h[a]),a+=2}var g,S,b=t.t-1,y=!0,E=d();for(s=St(t[b])-1;b>=0;){for(s>=c?g=t[b]>>s-c&l:(g=(t[b]&(1<<s+1)-1)<<c-s,b>0&&(g|=t[b-1]>>this.DB+s-c)),a=r;!(1&g);)g>>=1,--a;if((s-=a)<0&&(s+=this.DB,--b),y)h[g].copyTo(o),y=!1;else{for(;a>1;)n.sqrTo(o,E),n.sqrTo(E,o),a-=2;a>0?n.sqrTo(o,E):(S=o,o=E,E=S),n.mulTo(E,h[g],o)}for(;b>=0&&!(t[b]&1<<s);)n.sqrTo(o,E),S=o,o=E,E=S,--s<0&&(s=this.DB-1,--b)}return n.revert(o)},i.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||t.signum()==0)return i.ZERO;for(var r=t.clone(),n=this.clone(),s=G(1),o=G(0),h=G(0),a=G(1);r.signum()!=0;){for(;r.isEven();)r.rShiftTo(1,r),e?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(t,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),e?(h.isEven()&&a.isEven()||(h.addTo(this,h),a.subTo(t,a)),h.rShiftTo(1,h)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);r.compareTo(n)>=0?(r.subTo(n,r),e&&s.subTo(h,s),o.subTo(a,o)):(n.subTo(r,n),e&&h.subTo(s,h),a.subTo(o,a))}return n.compareTo(i.ONE)!=0?i.ZERO:a.compareTo(t)>=0?a.subtract(t):a.signum()<0?(a.addTo(t,a),a.signum()<0?a.add(t):a):a},i.prototype.pow=function(t){return this.exp(t,new Gt)},i.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var s=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(s<o&&(o=s),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(s=e.getLowestSetBit())>0&&e.rShiftTo(s,e),(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},i.prototype.isProbablePrime=function(t){var e,r=this.abs();if(r.t==1&&r[0]<=B[B.length-1]){for(e=0;e<B.length;++e)if(r[0]==B[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<B.length;){for(var n=B[e],s=e+1;s<B.length&&n<$t;)n*=B[s++];for(n=r.modInt(n);e<s;)if(n%B[e++]==0)return!1}return r.millerRabin(t)},i.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},i.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},i.prototype.fromString=function(t,e){var r;if(e==16)r=4;else if(e==8)r=3;else if(e==256)r=8;else if(e==2)r=1;else if(e==32)r=5;else{if(e!=4)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,s=!1,o=0;--n>=0;){var h=r==8?255&+t[n]:Kt(t,n);h<0?t.charAt(n)=="-"&&(s=!0):(s=!1,o==0?this[this.t++]=h:o+r>this.DB?(this[this.t-1]|=(h&(1<<this.DB-o)-1)<<o,this[this.t++]=h>>this.DB-o):this[this.t-1]|=h<<o,(o+=r)>=this.DB&&(o-=this.DB))}r==8&&128&+t[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&i.ZERO.subTo(this,this)},i.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},i.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},i.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},i.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,s=(1<<n)-1,o=Math.floor(t/this.DB),h=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+o+1]=this[a]>>n|h,h=(this[a]&s)<<r;for(a=o-1;a>=0;--a)e[a]=0;e[o]=h,e.t=this.t+o+1,e.s=this.s,e.clamp()},i.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,s=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var h=r+1;h<this.t;++h)e[h-r-1]|=(this[h]&o)<<s,e[h-r]=this[h]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<s),e.t=this.t-r,e.clamp()}},i.prototype.subTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},i.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),s=r.t;for(e.t=s+n.t;--s>=0;)e[s]=0;for(s=0;s<n.t;++s)e[s+r.t]=r.am(0,n[s],e,s,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&i.ZERO.subTo(e,e)},i.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},i.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return e!=null&&e.fromInt(0),void(r!=null&&this.copyTo(r));r==null&&(r=d());var o=d(),h=this.s,a=t.s,c=this.DB-St(n[n.t-1]);c>0?(n.lShiftTo(c,o),s.lShiftTo(c,r)):(n.copyTo(o),s.copyTo(r));var l=o.t,v=o[l-1];if(v!=0){var g=v*(1<<this.F1)+(l>1?o[l-2]>>this.F2:0),S=this.FV/g,b=(1<<this.F1)/g,y=1<<this.F2,E=r.t,F=E-l,A=e??d();for(o.dlShiftTo(F,A),r.compareTo(A)>=0&&(r[r.t++]=1,r.subTo(A,r)),i.ONE.dlShiftTo(l,A),A.subTo(o,o);o.t<l;)o[o.t++]=0;for(;--F>=0;){var I=r[--E]==v?this.DM:Math.floor(r[E]*S+(r[E-1]+y)*b);if((r[E]+=o.am(0,I,r,F,0,l))<I)for(o.dlShiftTo(F,A),r.subTo(A,r);r[E]<--I;)r.subTo(A,r)}e!=null&&(r.drShiftTo(l,e),h!=a&&i.ZERO.subTo(e,e)),r.t=l,r.clamp(),c>0&&r.rShiftTo(c,r),h<0&&i.ZERO.subTo(r,r)}}},i.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},i.prototype.isEven=function(){return(this.t>0?1&this[0]:this.s)==0},i.prototype.exp=function(t,e){if(t>4294967295||t<1)return i.ONE;var r=d(),n=d(),s=e.convert(this),o=St(t)-1;for(s.copyTo(r);--o>=0;)if(e.sqrTo(r,n),(t&1<<o)>0)e.mulTo(n,s,r);else{var h=r;r=n,n=h}return e.revert(r)},i.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},i.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=G(r),s=d(),o=d(),h="";for(this.divRemTo(n,s,o);s.signum()>0;)h=(r+o.intValue()).toString(t).substr(1)+h,s.divRemTo(n,s,o);return o.intValue().toString(t)+h},i.prototype.fromRadix=function(t,e){this.fromInt(0),e==null&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),s=!1,o=0,h=0,a=0;a<t.length;++a){var c=Kt(t,a);c<0?t.charAt(a)=="-"&&this.signum()==0&&(s=!0):(h=e*h+c,++o>=r&&(this.dMultiply(n),this.dAddOffset(h,0),o=0,h=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(h,0)),s&&i.ZERO.subTo(this,this)},i.prototype.fromNumber=function(t,e,r){if(typeof e=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),nt,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(i.ONE.shiftLeft(t-1),this);else{var n=[],s=7&t;n.length=1+(t>>3),e.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},i.prototype.bitwiseTo=function(t,e,r){var n,s,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(s=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],s);r.t=this.t}else{for(s=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(s,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},i.prototype.changeBit=function(t,e){var r=i.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},i.prototype.addTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},i.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},i.prototype.dAddOffset=function(t,e){if(t!=0){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},i.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var s=r.t-this.t;n<s;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(s=Math.min(t.t,e);n<s;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},i.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},i.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(e==0)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},i.prototype.millerRabin=function(t){var e=this.subtract(i.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>B.length&&(t=B.length);for(var s=d(),o=0;o<t;++o){s.fromInt(B[Math.floor(Math.random()*B.length)]);var h=s.modPow(n,this);if(h.compareTo(i.ONE)!=0&&h.compareTo(e)!=0){for(var a=1;a++<r&&h.compareTo(e)!=0;)if((h=h.modPowInt(2,this)).compareTo(i.ONE)==0)return!1;if(h.compareTo(e)!=0)return!1}}return!0},i.prototype.square=function(){var t=d();return this.squareTo(t),t},i.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var s=r;r=n,n=s}var o=r.getLowestSetBit(),h=n.getLowestSetBit();if(h<0)e(r);else{o<h&&(h=o),h>0&&(r.rShiftTo(h,r),n.rShiftTo(h,n));var a=function(){(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(a,0):(h>0&&n.lShiftTo(h,n),setTimeout(function(){e(n)},0))};setTimeout(a,10)}},i.prototype.fromNumberAsync=function(t,e,r,n){if(typeof e=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),nt,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>t&&s.subTo(i.ONE.shiftLeft(t-1),s),s.isProbablePrime(e)?setTimeout(function(){n()},0):setTimeout(o,0)};setTimeout(o,0)}else{var h=[],a=7&t;h.length=1+(t>>3),e.nextBytes(h),a>0?h[0]&=(1<<a)-1:h[0]=0,this.fromString(h,256)}},i}(),Gt=function(){function i(){}return i.prototype.convert=function(t){return t},i.prototype.revert=function(t){return t},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},i.prototype.sqrTo=function(t,e){t.squareTo(e)},i}(),Ct=function(){function i(t){this.m=t}return i.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),Ht=function(){function i(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return i.prototype.convert=function(t){var e=d();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(p.ZERO)>0&&this.m.subTo(e,e),e},i.prototype.revert=function(t){var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),Jt=function(){function i(t){this.m=t,this.r2=d(),this.q3=d(),p.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return i.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}();function d(){return new p(null)}function w(i,t){return new p(i,t)}var kt=typeof navigator<"u";kt&&navigator.appName=="Microsoft Internet Explorer"?(p.prototype.am=function(i,t,e,r,n,s){for(var o=32767&t,h=t>>15;--s>=0;){var a=32767&this[i],c=this[i++]>>15,l=h*a+c*o;n=((a=o*a+((32767&l)<<15)+e[r]+(1073741823&n))>>>30)+(l>>>15)+h*c+(n>>>30),e[r++]=1073741823&a}return n},H=30):kt&&navigator.appName!="Netscape"?(p.prototype.am=function(i,t,e,r,n,s){for(;--s>=0;){var o=t*this[i++]+e[r]+n;n=Math.floor(o/67108864),e[r++]=67108863&o}return n},H=26):(p.prototype.am=function(i,t,e,r,n,s){for(var o=16383&t,h=t>>14;--s>=0;){var a=16383&this[i],c=this[i++]>>14,l=h*a+c*o;n=((a=o*a+((16383&l)<<14)+e[r]+n)>>28)+(l>>14)+h*c,e[r++]=268435455&a}return n},H=28),p.prototype.DB=H,p.prototype.DM=(1<<H)-1,p.prototype.DV=1<<H,p.prototype.FV=Math.pow(2,52),p.prototype.F1=52-H,p.prototype.F2=2*H-52;var ht,P,Tt=[];for(ht=48,P=0;P<=9;++P)Tt[ht++]=P;for(ht=97,P=10;P<36;++P)Tt[ht++]=P;for(ht=65,P=10;P<36;++P)Tt[ht++]=P;function Kt(i,t){var e=Tt[i.charCodeAt(t)];return e??-1}function G(i){var t=d();return t.fromInt(i),t}function St(i){var t,e=1;return(t=i>>>16)!=0&&(i=t,e+=16),(t=i>>8)!=0&&(i=t,e+=8),(t=i>>4)!=0&&(i=t,e+=4),(t=i>>2)!=0&&(i=t,e+=2),(t=i>>1)!=0&&(i=t,e+=1),e}p.ZERO=G(0),p.ONE=G(1);var Et,_,Yt=function(){function i(){this.i=0,this.j=0,this.S=[]}return i.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},i.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},i}(),J=null;if(J==null){J=[],_=0;var wt=void 0;if(window.crypto&&window.crypto.getRandomValues){var Vt=new Uint32Array(256);for(window.crypto.getRandomValues(Vt),wt=0;wt<Vt.length;++wt)J[_++]=255&Vt[wt]}var Ot=0,Dt=function(i){if((Ot=Ot||0)>=256||_>=256)window.removeEventListener?window.removeEventListener("mousemove",Dt,!1):window.detachEvent&&window.detachEvent("onmousemove",Dt);else try{var t=i.x+i.y;J[_++]=255&t,Ot+=1}catch{}};window.addEventListener?window.addEventListener("mousemove",Dt,!1):window.attachEvent&&window.attachEvent("onmousemove",Dt)}function Wt(){if(Et==null){for(Et=new Yt;_<256;){var i=Math.floor(65536*Math.random());J[_++]=255&i}for(Et.init(J),_=0;_<J.length;++_)J[_]=0;_=0}return Et.next()}var It=function(){function i(){}return i.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Wt()},i}(),Xt=function(){function i(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return i.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},i.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},i.prototype.setPublic=function(t,e){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=w(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},i.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(a,c){if(c<a.length+11)return console.error("Message too long for RSA"),null;for(var l=[],v=a.length-1;v>=0&&c>0;){var g=a.charCodeAt(v--);g<128?l[--c]=g:g>127&&g<2048?(l[--c]=63&g|128,l[--c]=g>>6|192):(l[--c]=63&g|128,l[--c]=g>>6&63|128,l[--c]=g>>12|224)}l[--c]=0;for(var S=new It,b=[];c>2;){for(b[0]=0;b[0]==0;)S.nextBytes(b);l[--c]=b[0]}return l[--c]=2,l[--c]=0,new p(l)}(t,e);if(r==null)return null;var n=this.doPublic(r);if(n==null)return null;for(var s=n.toString(16),o=s.length,h=0;h<2*e-o;h++)s="0"+s;return s},i.prototype.setPrivate=function(t,e,r){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=w(t,16),this.e=parseInt(e,16),this.d=w(r,16)):console.error("Invalid RSA private key")},i.prototype.setPrivateEx=function(t,e,r,n,s,o,h,a){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=w(t,16),this.e=parseInt(e,16),this.d=w(r,16),this.p=w(n,16),this.q=w(s,16),this.dmp1=w(o,16),this.dmq1=w(h,16),this.coeff=w(a,16)):console.error("Invalid RSA private key")},i.prototype.generate=function(t,e){var r=new It,n=t>>1;this.e=parseInt(e,16);for(var s=new p(e,16);;){for(;this.p=new p(t-n,1,r),this.p.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.p.isProbablePrime(10););for(;this.q=new p(n,1,r),this.q.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var h=this.p.subtract(p.ONE),a=this.q.subtract(p.ONE),c=h.multiply(a);if(c.gcd(s).compareTo(p.ONE)==0){this.n=this.p.multiply(this.q),this.d=s.modInverse(c),this.dmp1=this.d.mod(h),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},i.prototype.decrypt=function(t){var e=w(t,16),r=this.doPrivate(e);return r==null?null:function(n,s){for(var o=n.toByteArray(),h=0;h<o.length&&o[h]==0;)++h;if(o.length-h!=s-1||o[h]!=2)return null;for(++h;o[h]!=0;)if(++h>=o.length)return null;for(var a="";++h<o.length;){var c=255&o[h];c<128?a+=String.fromCharCode(c):c>191&&c<224?(a+=String.fromCharCode((31&c)<<6|63&o[h+1]),++h):(a+=String.fromCharCode((15&c)<<12|(63&o[h+1])<<6|63&o[h+2]),h+=2)}return a}(r,this.n.bitLength()+7>>3)},i.prototype.generateAsync=function(t,e,r){var n=new It,s=t>>1;this.e=parseInt(e,16);var o=new p(e,16),h=this,a=function(){var c=function(){if(h.p.compareTo(h.q)<=0){var g=h.p;h.p=h.q,h.q=g}var S=h.p.subtract(p.ONE),b=h.q.subtract(p.ONE),y=S.multiply(b);y.gcd(o).compareTo(p.ONE)==0?(h.n=h.p.multiply(h.q),h.d=o.modInverse(y),h.dmp1=h.d.mod(S),h.dmq1=h.d.mod(b),h.coeff=h.q.modInverse(h.p),setTimeout(function(){r()},0)):setTimeout(a,0)},l=function(){h.q=d(),h.q.fromNumberAsync(s,1,n,function(){h.q.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&h.q.isProbablePrime(10)?setTimeout(c,0):setTimeout(l,0)})})},v=function(){h.p=d(),h.p.fromNumberAsync(t-s,1,n,function(){h.p.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&h.p.isProbablePrime(10)?setTimeout(l,0):setTimeout(v,0)})})};setTimeout(v,0)};setTimeout(a,0)},i.prototype.sign=function(t,e,r){var n=function(h,a){if(a<h.length+22)return console.error("Message too long for RSA"),null;for(var c=a-h.length-6,l="",v=0;v<c;v+=2)l+="ff";return w("0001"+l+"00"+h,16)}((xt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(n==null)return null;var s=this.doPrivate(n);if(s==null)return null;var o=s.toString(16);return 1&o.length?"0"+o:o},i.prototype.verify=function(t,e,r){var n=w(e,16),s=this.doPublic(n);return s==null?null:function(o){for(var h in xt)if(xt.hasOwnProperty(h)){var a=xt[h],c=a.length;if(o.substr(0,c)==a)return o.substr(c)}return o}(s.toString(16).replace(/^1f+00/,""))==r(t).toString()},i}(),xt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"},D={};D.lang={extend:function(i,t,e){if(!t||!i)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var r=function(){};if(r.prototype=t.prototype,i.prototype=new r,i.prototype.constructor=i,i.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),e){var n;for(n in e)i.prototype[n]=e[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(h,a){for(n=0;n<o.length;n+=1){var c=o[n],l=a[c];typeof l=="function"&&l!=Object.prototype[c]&&(h[c]=l)}})}catch{}s(i.prototype,e)}}};var u={};u.asn1!==void 0&&u.asn1||(u.asn1={}),u.asn1.ASN1Util=new function(){this.integerToByteHex=function(i){var t=i.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(i){var t=i.toString(16);if(t.substr(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var e=t.substr(1).length;e%2==1?e+=1:t.match(/^[0-7]/)||(e+=2);for(var r="",n=0;n<e;n++)r+="f";t=new p(r,16).xor(i).add(p.ONE).toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(i,t){return hextopem(i,t)},this.newObject=function(i){var t=u.asn1,e=t.DERBoolean,r=t.DERInteger,n=t.DERBitString,s=t.DEROctetString,o=t.DERNull,h=t.DERObjectIdentifier,a=t.DEREnumerated,c=t.DERUTF8String,l=t.DERNumericString,v=t.DERPrintableString,g=t.DERTeletexString,S=t.DERIA5String,b=t.DERUTCTime,y=t.DERGeneralizedTime,E=t.DERSequence,F=t.DERSet,A=t.DERTaggedObject,I=t.ASN1Util.newObject,at=Object.keys(i);if(at.length!=1)throw"key of param shall be only one.";var f=at[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+f+":")==-1)throw"undefined key: "+f;if(f=="bool")return new e(i[f]);if(f=="int")return new r(i[f]);if(f=="bitstr")return new n(i[f]);if(f=="octstr")return new s(i[f]);if(f=="null")return new o(i[f]);if(f=="oid")return new h(i[f]);if(f=="enum")return new a(i[f]);if(f=="utf8str")return new c(i[f]);if(f=="numstr")return new l(i[f]);if(f=="prnstr")return new v(i[f]);if(f=="telstr")return new g(i[f]);if(f=="ia5str")return new S(i[f]);if(f=="utctime")return new b(i[f]);if(f=="gentime")return new y(i[f]);if(f=="seq"){for(var gt=i[f],dt=[],tt=0;tt<gt.length;tt++){var Pt=I(gt[tt]);dt.push(Pt)}return new E({array:dt})}if(f=="set"){for(gt=i[f],dt=[],tt=0;tt<gt.length;tt++)Pt=I(gt[tt]),dt.push(Pt);return new F({array:dt})}if(f=="tag"){var L=i[f];if(Object.prototype.toString.call(L)==="[object Array]"&&L.length==3){var re=I(L[2]);return new A({tag:L[0],explicit:L[1],obj:re})}var Bt={};if(L.explicit!==void 0&&(Bt.explicit=L.explicit),L.tag!==void 0&&(Bt.tag=L.tag),L.obj===void 0)throw"obj shall be specified for 'tag'.";return Bt.obj=I(L.obj),new A(Bt)}},this.jsonToASN1HEX=function(i){return this.newObject(i).getEncodedHex()}},u.asn1.ASN1Util.oidHexToInt=function(i){for(var t="",e=parseInt(i.substr(0,2),16),r=(t=Math.floor(e/40)+"."+e%40,""),n=2;n<i.length;n+=2){var s=("00000000"+parseInt(i.substr(n,2),16).toString(2)).slice(-8);r+=s.substr(1,7),s.substr(0,1)=="0"&&(t=t+"."+new p(r,2).toString(10),r="")}return t},u.asn1.ASN1Util.oidIntToHex=function(i){var t=function(h){var a=h.toString(16);return a.length==1&&(a="0"+a),a},e=function(h){var a="",c=new p(h,10).toString(2),l=7-c.length%7;l==7&&(l=0);for(var v="",g=0;g<l;g++)v+="0";for(c=v+c,g=0;g<c.length-1;g+=7){var S=c.substr(g,7);g!=c.length-7&&(S="1"+S),a+=t(parseInt(S,2))}return a};if(!i.match(/^[0-9.]+$/))throw"malformed oid string: "+i;var r="",n=i.split("."),s=40*parseInt(n[0])+parseInt(n[1]);r+=t(s),n.splice(0,2);for(var o=0;o<n.length;o++)r+=e(n[o]);return r},u.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(this.hV===void 0||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var i=this.hV.length/2,t=i.toString(16);if(t.length%2==1&&(t="0"+t),i<128)return t;var e=t.length/2;if(e>15)throw"ASN.1 length too long to represent by 8x: n = "+i.toString(16);return(128+e).toString(16)+t},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},u.asn1.DERAbstractString=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?this.setString(i):i.str!==void 0?this.setString(i.str):i.hex!==void 0&&this.setStringHex(i.hex))},D.lang.extend(u.asn1.DERAbstractString,u.asn1.ASN1Object),u.asn1.DERAbstractTime=function(i){u.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,s=this.localDateToUTC(t),o=String(s.getFullYear());e=="utc"&&(o=o.substr(2,2));var h=o+n(String(s.getMonth()+1),2)+n(String(s.getDate()),2)+n(String(s.getHours()),2)+n(String(s.getMinutes()),2)+n(String(s.getSeconds()),2);if(r===!0){var a=s.getMilliseconds();if(a!=0){var c=n(String(a),3);h=h+"."+(c=c.replace(/[0]+$/,""))}}return h+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,s,o){var h=new Date(Date.UTC(t,e-1,r,n,s,o,0));this.setByDate(h)},this.getFreshValueHex=function(){return this.hV}},D.lang.extend(u.asn1.DERAbstractTime,u.asn1.ASN1Object),u.asn1.DERAbstractStructured=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,i!==void 0&&i.array!==void 0&&(this.asn1Array=i.array)},D.lang.extend(u.asn1.DERAbstractStructured,u.asn1.ASN1Object),u.asn1.DERBoolean=function(){u.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},D.lang.extend(u.asn1.DERBoolean,u.asn1.ASN1Object),u.asn1.DERInteger=function(i){u.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.bigint!==void 0?this.setByBigInteger(i.bigint):i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},D.lang.extend(u.asn1.DERInteger,u.asn1.ASN1Object),u.asn1.DERBitString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex="00"+t.getEncodedHex()}u.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,r){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+r},this.setByBinaryString=function(e){var r=8-(e=e.replace(/0+$/,"")).length%8;r==8&&(r=0);for(var n=0;n<=r;n++)e+="0";var s="";for(n=0;n<e.length-1;n+=8){var o=e.substr(n,8),h=parseInt(o,2).toString(16);h.length==1&&(h="0"+h),s+=h}this.hTLV=null,this.isModified=!0,this.hV="0"+r+s},this.setByBooleanArray=function(e){for(var r="",n=0;n<e.length;n++)e[n]==1?r+="1":r+="0";this.setByBinaryString(r)},this.newFalseArray=function(e){for(var r=new Array(e),n=0;n<e;n++)r[n]=!1;return r},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"&&i.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(i):i.hex!==void 0?this.setHexValueIncludingUnusedBits(i.hex):i.bin!==void 0?this.setByBinaryString(i.bin):i.array!==void 0&&this.setByBooleanArray(i.array))},D.lang.extend(u.asn1.DERBitString,u.asn1.ASN1Object),u.asn1.DEROctetString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex=t.getEncodedHex()}u.asn1.DEROctetString.superclass.constructor.call(this,i),this.hT="04"},D.lang.extend(u.asn1.DEROctetString,u.asn1.DERAbstractString),u.asn1.DERNull=function(){u.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},D.lang.extend(u.asn1.DERNull,u.asn1.ASN1Object),u.asn1.DERObjectIdentifier=function(i){var t=function(r){var n=r.toString(16);return n.length==1&&(n="0"+n),n},e=function(r){var n="",s=new p(r,10).toString(2),o=7-s.length%7;o==7&&(o=0);for(var h="",a=0;a<o;a++)h+="0";for(s=h+s,a=0;a<s.length-1;a+=7){var c=s.substr(a,7);a!=s.length-7&&(c="1"+c),n+=t(parseInt(c,2))}return n};u.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(r){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=r},this.setValueOidString=function(r){if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var n="",s=r.split("."),o=40*parseInt(s[0])+parseInt(s[1]);n+=t(o),s.splice(0,2);for(var h=0;h<s.length;h++)n+=e(s[h]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(r){var n=u.asn1.x509.OID.name2oid(r);if(n==="")throw"DERObjectIdentifier oidName undefined: "+r;this.setValueOidString(n)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?i.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(i):this.setValueName(i):i.oid!==void 0?this.setValueOidString(i.oid):i.hex!==void 0?this.setValueHex(i.hex):i.name!==void 0&&this.setValueName(i.name))},D.lang.extend(u.asn1.DERObjectIdentifier,u.asn1.ASN1Object),u.asn1.DEREnumerated=function(i){u.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},D.lang.extend(u.asn1.DEREnumerated,u.asn1.ASN1Object),u.asn1.DERUTF8String=function(i){u.asn1.DERUTF8String.superclass.constructor.call(this,i),this.hT="0c"},D.lang.extend(u.asn1.DERUTF8String,u.asn1.DERAbstractString),u.asn1.DERNumericString=function(i){u.asn1.DERNumericString.superclass.constructor.call(this,i),this.hT="12"},D.lang.extend(u.asn1.DERNumericString,u.asn1.DERAbstractString),u.asn1.DERPrintableString=function(i){u.asn1.DERPrintableString.superclass.constructor.call(this,i),this.hT="13"},D.lang.extend(u.asn1.DERPrintableString,u.asn1.DERAbstractString),u.asn1.DERTeletexString=function(i){u.asn1.DERTeletexString.superclass.constructor.call(this,i),this.hT="14"},D.lang.extend(u.asn1.DERTeletexString,u.asn1.DERAbstractString),u.asn1.DERIA5String=function(i){u.asn1.DERIA5String.superclass.constructor.call(this,i),this.hT="16"},D.lang.extend(u.asn1.DERIA5String,u.asn1.DERAbstractString),u.asn1.DERUTCTime=function(i){u.asn1.DERUTCTime.superclass.constructor.call(this,i),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{12}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date))},D.lang.extend(u.asn1.DERUTCTime,u.asn1.DERAbstractTime),u.asn1.DERGeneralizedTime=function(i){u.asn1.DERGeneralizedTime.superclass.constructor.call(this,i),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{14}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date),i.millis===!0&&(this.withMillis=!0))},D.lang.extend(u.asn1.DERGeneralizedTime,u.asn1.DERAbstractTime),u.asn1.DERSequence=function(i){u.asn1.DERSequence.superclass.constructor.call(this,i),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},D.lang.extend(u.asn1.DERSequence,u.asn1.DERAbstractStructured),u.asn1.DERSet=function(i){u.asn1.DERSet.superclass.constructor.call(this,i),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return this.sortFlag==1&&t.sort(),this.hV=t.join(""),this.hV},i!==void 0&&i.sortflag!==void 0&&i.sortflag==0&&(this.sortFlag=!1)},D.lang.extend(u.asn1.DERSet,u.asn1.DERAbstractStructured),u.asn1.DERTaggedObject=function(i){u.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.tag!==void 0&&(this.hT=i.tag),i.explicit!==void 0&&(this.isExplicit=i.explicit),i.obj!==void 0&&(this.asn1Object=i.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},D.lang.extend(u.asn1.DERTaggedObject,u.asn1.ASN1Object);var Nt,te=(Nt=function(i,t){return(Nt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])})(i,t)},function(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=i}Nt(i,t),i.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}),Ut=function(i){function t(e){var r=i.call(this)||this;return e&&(typeof e=="string"?r.parseKey(e):(t.hasPrivateKeyProperty(e)||t.hasPublicKeyProperty(e))&&r.parsePropertiesFrom(e)),r}return te(t,i),t.prototype.parseKey=function(e){try{var r=0,n=0,s=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?function(b){var y;if(U===void 0){var E="0123456789ABCDEF",F=` \f
\r	 \u2028\u2029`;for(U={},y=0;y<16;++y)U[E.charAt(y)]=y;for(E=E.toLowerCase(),y=10;y<16;++y)U[E.charAt(y)]=y;for(y=0;y<F.length;++y)U[F.charAt(y)]=-1}var A=[],I=0,at=0;for(y=0;y<b.length;++y){var f=b.charAt(y);if(f=="=")break;if((f=U[f])!=-1){if(f===void 0)throw new Error("Illegal character at offset "+y);I|=f,++at>=2?(A[A.length]=I,I=0,at=0):I<<=4}}if(at)throw new Error("Hex encoding incomplete: 4 bits missing");return A}(e):x.unarmor(e),o=X.decode(s);if(o.sub.length===3&&(o=o.sub[2].sub[0]),o.sub.length===9){r=o.sub[1].getHexStringValue(),this.n=w(r,16),n=o.sub[2].getHexStringValue(),this.e=parseInt(n,16);var h=o.sub[3].getHexStringValue();this.d=w(h,16);var a=o.sub[4].getHexStringValue();this.p=w(a,16);var c=o.sub[5].getHexStringValue();this.q=w(c,16);var l=o.sub[6].getHexStringValue();this.dmp1=w(l,16);var v=o.sub[7].getHexStringValue();this.dmq1=w(v,16);var g=o.sub[8].getHexStringValue();this.coeff=w(g,16)}else{if(o.sub.length!==2)return!1;var S=o.sub[1].sub[0];r=S.sub[0].getHexStringValue(),this.n=w(r,16),n=S.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new u.asn1.DERInteger({int:0}),new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e}),new u.asn1.DERInteger({bigint:this.d}),new u.asn1.DERInteger({bigint:this.p}),new u.asn1.DERInteger({bigint:this.q}),new u.asn1.DERInteger({bigint:this.dmp1}),new u.asn1.DERInteger({bigint:this.dmq1}),new u.asn1.DERInteger({bigint:this.coeff})]};return new u.asn1.DERSequence(e).getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return ot(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new u.asn1.DERSequence({array:[new u.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new u.asn1.DERNull]}),r=new u.asn1.DERSequence({array:[new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e})]}),n=new u.asn1.DERBitString({hex:"00"+r.getEncodedHex()});return new u.asn1.DERSequence({array:[e,n]}).getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return ot(this.getPublicBaseKey())},t.wordwrap=function(e,r){if(!e)return e;var n="(.{1,"+(r=r||64)+`})( +|$
?)|(.{1,`+r+"})";return e.match(RegExp(n,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var e=`-----BEGIN RSA PRIVATE KEY-----
`;return(e+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`)+"-----END RSA PRIVATE KEY-----"},t.prototype.getPublicKey=function(){var e=`-----BEGIN PUBLIC KEY-----
`;return(e+=t.wordwrap(this.getPublicBaseKeyB64())+`
`)+"-----END PUBLIC KEY-----"},t.hasPublicKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}(Xt);const ee=function(){function i(t){t===void 0&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return i.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Ut(t)},i.prototype.setPrivateKey=function(t){this.setKey(t)},i.prototype.setPublicKey=function(t){this.setKey(t)},i.prototype.decrypt=function(t){try{return this.getKey().decrypt(yt(t))}catch{return!1}},i.prototype.encrypt=function(t){try{return ot(this.getKey().encrypt(t))}catch{return!1}},i.prototype.sign=function(t,e,r){try{return ot(this.getKey().sign(t,e,r))}catch{return!1}},i.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,yt(e),r)}catch{return!1}},i.prototype.getKey=function(t){if(!this.key){if(this.key=new Ut,t&&{}.toString.call(t)==="[object Function]")return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},i.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},i.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},i.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},i.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},i.version="3.2.1",i}()}],N={d:(K,V)=>{for(var T in V)N.o(V,T)&&!N.o(K,T)&&Object.defineProperty(K,T,{enumerable:!0,get:V[T]})},o:(K,V)=>Object.prototype.hasOwnProperty.call(K,V)},vt={};return mt[1](0,vt,N),vt.default})()})}(Lt)),Lt.exports}var De=we();const Qt=ie(De),xe="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALj0zjON+EVdBsnMcR4Uj+jOYgp5ZipftQZ1utW8KvVioz+RSaotF1JHt59q9SC/mZcWWpbpcEqQ3WyyyCC33msCAwEAAQ==",Be="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAuPTOM434RV0GycxxHhSP6M5iCnlmKl+1BnW61bwq9WKjP5FJqi0XUke3n2r1IL+ZlxZalulwSpDdbLLIILfeawIDAQABAkB5PYAtq1KjpWddwPYlkbUEFsWNuCaQgExZ/7KJiN9gGjo/UfUZ3W39Orb8PITIYf1NbasReqgddAcsfJNyoDWBAiEA7K89DyTmbjNSmekdD3rejRDdMzzXYtcbo69ZjHoowMUCIQDIDN8eg6PcWk4kiRcRYcNEfriUJR7Fg07ellSPv821bwIhAJA5TEyxIJUgQwI0cZfgOELfdtrlBR5ek6IPlNKsEa89AiBbMVroexPQWC41A3VLjChKagXUKpO7b98dIqRLnyCz6wIgP3qpvnO4IOxY7f5XarfCVyIHZJAMt/R1f16P5OkKv+A=";function Ae(it){const k=new Qt;return k.setPublicKey(xe),k.encrypt(it)}function Re(it){const k=new Qt;return k.setPrivateKey(Be),k.decrypt(it)}const Ve={class:"login-wrap"},Oe={class:"login"},Ie={class:"title"},Ne={style:{padding:"0 25px 5px 25px"}},Pe={key:0},Me={key:1},qe={class:"el-login-footer"},_e=["innerHTML"],Le=le({name:"login"}),je=Object.assign(Le,{setup(it){var k="";const mt=se(()=>import("https://openfpcdn.io/fingerprintjs/v3"),[]).then(m=>m.load()),N=oe(),vt=fe(),K=pe(),{proxy:V}=ge(),T=ut({username:"",password:"",rememberMe:!1,code:"",uuid:""});Mt(()=>{T.value.username="admin",T.value.password="123456",ft()});const q={username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},z=he({get:()=>N.loginType,set:m=>{N.loginType=m}}),nt=ut(""),W=ut(!1),st=ut("");ut(!1);const lt=ut();lt.value=K.query.redirect,mt.then(m=>m.get()).then(m=>{k=m.visitorId,N.setClientId(k)}),Ft(K,m=>{lt.value=m.query&&m.query.redirect},{immediate:!0});function ft(){V.$refs.loginRef.validate(m=>{m&&(W.value=!0,T.value.rememberMe?(Z.set("username",T.value.username,{expires:7}),Z.set("password",Ae(T.value.password),{expires:7}),Z.set("rememberMe",T.value.rememberMe,{expires:7})):(Z.remove("username"),Z.remove("password"),Z.remove("rememberMe")),N.login(T.value).then(()=>{V.$modal.msgSuccess(V.$t("login.loginSuccess"));const x=K.query,O=Object.keys(x).reduce((j,$)=>($!=="redirect"&&(j[$]=x[$]),j),{});vt.push({path:lt.value||"/",query:O})}).catch(x=>{console.error(x),V.$modal.msgError(x.msg),W.value=!1,st.value&&U()}))})}function U(){ae().then(m=>{nt.value="data:image/gif;base64,"+m.data.img,T.value.uuid=m.data.uuid,st.value=m.data.captchaOff})}function Q(){const m=Z.get("username"),x=Z.get("password"),O=Z.get("rememberMe");T.value={username:m===void 0?T.value.username:m,password:x===void 0?T.value.password:Re(x),rememberMe:O===void 0?!1:!!O}}function ot(m){N.loginType==3&&Mt(()=>{V.$refs.qrLoginRef.clearQr()})}Ft(()=>N.loginType,m=>{m==3&&yt()},{immediate:!0});function yt(){Mt(()=>{V.$refs.qrLoginRef.generateCode()})}return U(),Q(),(m,x)=>{const O=et("el-tab-pane"),j=et("el-tabs"),$=et("svg-icon"),bt=et("el-input"),C=et("el-form-item"),H=et("el-button"),pt=et("el-form");return rt(),qt(ce,null,[M(be),ct("div",Ve,[ct("div",Oe,[ct("h3",Ie,Zt(R(_t).title),1),M(de,{title:"多语言设置",class:"langSet"}),ct("div",Ne,[M(j,{modelValue:R(z),"onUpdate:modelValue":x[0]||(x[0]=X=>ue(z)?z.value=X:null),onTabClick:ot},{default:Y(()=>[M(O,{label:m.$t("login.loginway1"),name:1},null,8,["label"])]),_:1},8,["modelValue"])]),R(z)==1?(rt(),At(pt,{key:0,ref:"loginRef",model:R(T),rules:q,class:"login-form"},{default:Y(()=>[M(C,{prop:"username"},{default:Y(()=>[M(bt,{modelValue:R(T).username,"onUpdate:modelValue":x[1]||(x[1]=X=>R(T).username=X),type:"text","auto-complete":"off",placeholder:m.$t("login.account")},{prefix:Y(()=>[M($,{name:"user",class:"input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),M(C,{prop:"password"},{default:Y(()=>[M(bt,{modelValue:R(T).password,"onUpdate:modelValue":x[2]||(x[2]=X=>R(T).password=X),"show-password":"",type:"password","auto-complete":"off",placeholder:m.$t("login.password"),onKeyup:me(ft,["enter"])},{prefix:Y(()=>[M($,{name:"password",class:"input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),M(C,{style:ye([{width:"100%"},{"margin-top":R(st)=="off"?"40px":""}])},{default:Y(()=>[M(H,{loading:R(W),size:"default",round:"",type:"primary",style:{width:"100%"},onClick:ve(ft,["prevent"])},{default:Y(()=>[R(W)?(rt(),qt("span",Me,"登 录 中...")):(rt(),qt("span",Pe,Zt(m.$t("login.btnLogin")),1))]),_:1},8,["loading"])]),_:1},8,["style"])]),_:1},8,["model"])):Rt("",!0),R(z)==3?(rt(),At(Ee,{key:1,ref:"qrLoginRef"},null,512)):Rt("",!0),R(z)==2?(rt(),At(Se,{key:2})):Rt("",!0),R(_t).showOtherLogin?(rt(),At(Te,{key:3})):Rt("",!0)]),ct("div",qe,[ct("div",{innerHTML:R(_t).copyright},null,8,_e)])])],64)}}}),Ze=ne(je,[["__scopeId","data-v-ed49b14d"]]);export{Ze as default};
