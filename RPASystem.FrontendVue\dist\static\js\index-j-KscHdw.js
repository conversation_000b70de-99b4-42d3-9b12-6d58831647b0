import{a as ce,p as fe,d as be,b as ge,s as _e}from"./gen-BRdzRWqq.js";import{u as ve,a as we,r as c,E as he,a5 as ye,w as Ne,b4 as Ce,x as d,N as B,o as s,c as y,i as t,l as o,k as i,D as r,O as b,p as h,t as E,m as $e,F as ke,K as Se,j as P,s as Ve,v as Te}from"./index-CX4J5aM5.js";import Ie from"./importTable-B1WkisRD.js";import{H as xe}from"./index-CUndBEIL.js";const De={class:"app-container"},Re=["innerHTML"],Le=Ve({name:"gen"}),He=Object.assign(Le,{setup(Be){const U=ve(),z=we(),{proxy:a}=Te(),V=c([]),k=c(!0),T=c([]);c(!0);const I=c(!0),N=c(0),F=c([]),x=c(!1),C=c({}),H=he({queryParams:{pageNum:1,pageSize:10,tableName:void 0,t:0},preview:{open:!1,title:"代码预览",data:{},activeName:"0"}}),{queryParams:u,preview:g}=ye(H);Ne(U,l=>{l&&_()},{immediate:!0});function _(){k.value=!0,ce(a.addDateRange(u.value,F.value)).then(l=>{V.value=l.data.result,N.value=l.data.totalNum,k.value=!1})}function D(){u.value.pageNum=1,_()}function j(l){if(C.value=l,!C.value)return a.$modal.msgError("请先选择要生成代码的数据表"),!1;a.$refs.codeform.validate(e=>{if(e)a.$modal.loading("正在生成代码..."),ge({tableId:C.value.tableId,tableName:C.value.name,VueVersion:3}).then(async f=>{const{data:$}=f;x.value=!1,l.genType==="1"?a.$modal.msgSuccess("成功生成到自定义路径"):(a.$modal.msgSuccess("恭喜你，代码生成完成！"),await a.downFile("/common/downloadFile",{path:$.path}))}).finally(()=>{a.$modal.closeLoading()});else return!1})}function q(l){const e=l.tableName;a.$confirm('确认要强制同步"'+e+'"表结构吗？').then(function(){return _e(l.tableId,{tableName:e,dbName:l.dbName})}).then(()=>{a.$modal.msgSuccess("同步成功")}).catch(()=>{})}function G(){a.$refs.importRef.show()}function O(l){a.$refs.codeform.validate(e=>{if(!e){a.$modal.msgError("请先完成表格内容");return}a.$modal.loading("请稍后..."),fe(l.tableId,{VueVersion:3}).then(f=>{f.code===200&&(x.value=!1,g.value.open=!0,g.value.data=f.data)}).finally(()=>{a.$modal.closeLoading()})})}function M(l){T.value=l.map(e=>e.tableId),I.value=!l.length}function A(l){u.value.tableName=l.tableName,_(),z.push({path:"/gen/editTable",query:{tableId:l.tableId}})}function R(l){const e=l.tableId||T.value;a.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{be(e.toString()).then(f=>{f.code==200&&(a.$modal.msgSuccess("删除成功"),D())})}).catch(()=>{a.$message({type:"info",message:"已取消删除"})})}function J(l){return xe.highlightAuto(l||"").value||"&nbsp;"}const{copy:K,isSupported:Q}=Ce();function W(l){Q?(K(l),a.$modal.msgSuccess("复制成功！")):a.$modal.msgError("当前浏览器不支持")}function X(l,e){switch(l){case"generate":j(e);break;case"delete":R(e);break;case"sync":q(e);break}}function Y(){a.resetForm("codeform"),D()}return _(),(l,e)=>{const f=d("el-input"),$=d("el-form-item"),m=d("el-button"),Z=d("el-form"),L=d("el-col"),ee=d("el-row"),p=d("el-table-column"),te=d("arrow-down"),oe=d("el-icon"),S=d("el-dropdown-item"),le=d("el-dropdown-menu"),ne=d("el-dropdown"),ae=d("el-button-group"),de=d("el-table"),ie=d("pagination"),re=d("el-link"),se=d("el-tab-pane"),ue=d("el-tabs"),me=d("zr-dialog"),v=B("hasPermi"),pe=B("loading");return s(),y("div",De,[t(Z,{ref:"codeform",inline:!0,model:i(u)},{default:o(()=>[t($,{label:"表名",prop:"tableName"},{default:o(()=>[t(f,{modelValue:i(u).tableName,"onUpdate:modelValue":e[0]||(e[0]=n=>i(u).tableName=n),style:{width:"200px"},clearable:"",placeholder:"输入要查询的表名"},null,8,["modelValue"])]),_:1}),t($,null,{default:o(()=>[t(m,{type:"primary",icon:"search",onClick:e[1]||(e[1]=n=>_())},{default:o(()=>e[8]||(e[8]=[r("查询")])),_:1}),t(m,{icon:"refresh",onClick:e[2]||(e[2]=n=>Y())},{default:o(()=>e[9]||(e[9]=[r("重置")])),_:1})]),_:1})]),_:1},8,["model"]),t(ee,{gutter:10,class:"mb10"},{default:o(()=>[t(L,{span:1.5},{default:o(()=>[b((s(),h(m,{type:"info",plain:"",icon:"upload",onClick:G},{default:o(()=>e[10]||(e[10]=[r("导入数据表")])),_:1})),[[v,["tool:gen:import"]]])]),_:1}),t(L,{span:1.5},{default:o(()=>[b((s(),h(m,{type:"danger",disabled:i(I),plain:"",icon:"delete",onClick:R},{default:o(()=>e[11]||(e[11]=[r(" 删除")])),_:1},8,["disabled"])),[[v,["tool:gen:remove"]]])]),_:1})]),_:1}),b((s(),h(de,{ref:"gridtable",data:i(V),border:"",onSelectionChange:M,"highlight-current-row":"",height:"400px"},{default:o(()=>[t(p,{type:"selection",align:"center",width:"55"}),t(p,{prop:"tableId",label:"tableId",width:"80",sortable:""}),t(p,{prop:"dbName",label:"数据库名",width:"90","show-overflow-tooltip":!0}),t(p,{prop:"tplCategory",label:"生成模板",width:"90",sortable:""}),t(p,{prop:"tableName",label:"表名",width:"120","show-overflow-tooltip":!0}),t(p,{prop:"tableComment",label:"表描述","show-overflow-tooltip":!0,width:"120"}),t(p,{prop:"className",label:"实体","show-overflow-tooltip":!0}),t(p,{prop:"createTime",label:"创建时间",sortable:""}),t(p,{prop:"updateTime",label:"更新时间",sortable:""}),t(p,{label:"操作",align:"center",width:"200"},{default:o(n=>[t(ae,null,{default:o(()=>[b((s(),h(m,{text:"",icon:"view",onClick:w=>O(n.row)},{default:o(()=>e[12]||(e[12]=[r(" 预览 ")])),_:2},1032,["onClick"])),[[v,["tool:gen:preview"]]]),b((s(),h(m,{text:"",icon:"edit",onClick:w=>A(n.row)},{default:o(()=>e[13]||(e[13]=[r(" 编辑 ")])),_:2},1032,["onClick"])),[[v,["tool:gen:edit"]]]),t(ne,{onCommand:w=>X(w,n.row)},{dropdown:o(()=>[t(le,null,{default:o(()=>[b((s(),y("div",null,[t(S,{command:"generate"},{default:o(()=>[t(m,{icon:"download",link:""},{default:o(()=>e[14]||(e[14]=[r("生成代码")])),_:1})]),_:1})])),[[v,["tool:gen:code"]]]),b((s(),y("div",null,[t(S,{command:"sync"},{default:o(()=>[t(m,{icon:"refresh",link:""},{default:o(()=>e[15]||(e[15]=[r(" 同步 ")])),_:1})]),_:1})])),[[v,["tool:gen:edit"]]]),b((s(),y("div",null,[t(S,{command:"delete"},{default:o(()=>[t(m,{icon:"delete",type:"danger",link:""},{default:o(()=>e[16]||(e[16]=[r(" 删除 ")])),_:1})]),_:1})])),[[v,["tool:gen:remove"]]])]),_:1})]),default:o(()=>[t(m,{text:""},{default:o(()=>[r(E(l.$t("btn.more"))+" ",1),t(oe,{class:"el-icon--right"},{default:o(()=>[t(te)]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[pe,i(k)]]),t(ie,{page:i(u).pageNum,"onUpdate:page":e[3]||(e[3]=n=>i(u).pageNum=n),limit:i(u).pageSize,"onUpdate:limit":e[4]||(e[4]=n=>i(u).pageSize=n),total:i(N),"onUpdate:total":e[5]||(e[5]=n=>$e(N)?N.value=n:null),onPagination:_},null,8,["page","limit","total"]),t(me,{modelValue:i(g).open,"onUpdate:modelValue":e[7]||(e[7]=n=>i(g).open=n),width:"80%",top:"5vh","append-to-body":""},{default:o(()=>[t(ue,{modelValue:i(g).activeName,"onUpdate:modelValue":e[6]||(e[6]=n=>i(g).activeName=n)},{default:o(()=>[(s(!0),y(ke,null,Se(i(g).data,(n,w)=>(s(),h(se,{label:n.title,id:w,name:w.toString(),key:w},{default:o(()=>[r(E(n.path)+" ",1),t(re,{underline:!1,icon:"DocumentCopy",onClick:Ee=>W(n.content),class:"btn-copy"},{default:o(()=>e[17]||(e[17]=[r("复制 ")])),_:2},1032,["onClick"]),P("pre",null,[P("code",{class:"hljs",innerHTML:J(n.content)},null,8,Re)])]),_:2},1032,["label","id","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),t(i(Ie),{ref:"importRef",onOk:_},null,512)])}}});export{He as default};
