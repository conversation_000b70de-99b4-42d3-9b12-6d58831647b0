﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5236E9D4-4632-40DD-A4FB-4A5F65653775}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>RPASystem.Client</RootNamespace>
    <AssemblyName>RPASystem.ClientWin</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Upload|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Z:\VV35\RPASystem.ClientWinV3-Publish\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Common\c.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Full|AnyCPU'">
    <OutputPath>bin\Full\</OutputPath>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DllFileOrDirOp">
      <HintPath>..\Lib\DllFileOrDirOp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\EnvParUtils.cs" />
    <Compile Include="Common\IniHelper.cs" />
    <Compile Include="Common\PreventLockDisplayUtils.cs" />
    <Compile Include="Common\RetryH.cs" />
    <Compile Include="Common\ShortcutUtils.cs" />
    <Compile Include="Common\ZipRarUtils.cs" />
    <Compile Include="Connected\ConnectionManager.cs" />
    <Compile Include="Connected\MachineMan.cs" />
    <Compile Include="Connected\ResourceReporter.cs" />
    <Compile Include="Core\TaskMan.cs" />
    <Compile Include="Core\PackagesMan.cs" />
    <Compile Include="EXE\EXEStarter.cs" />
    <Compile Include="ISRPA\ISRPALog.cs" />
    <Compile Include="ISRPA\ISRPAStarter.cs" />
    <Compile Include="ISRPA\ISRPAUtils.cs" />
    <Compile Include="ISRPA\RPACaseUtils.cs" />
    <Compile Include="ISRPA\RuntimeEnv\RPARuntimeEnv.cs" />
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\JobInfoModel.cs" />
    <Compile Include="Models\JobModel.cs" />
    <Compile Include="Models\ResourceInfoDto.cs" />
    <Compile Include="Models\RunResult.cs" />
    <Compile Include="Models\TaskCompletedDto.cs" />
    <Compile Include="Models\TaskHistoryManager.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\Remote\KeyboardController.cs" />
    <Compile Include="Services\Remote\MouseController.cs" />
    <Compile Include="Services\ScreenCaptureService.cs" />
    <Compile Include="Services\VideoRecorderService.cs" />
    <Compile Include="Services\VOpenDir\VOpenDir.cs" />
    <Compile Include="View\MsgTips.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\MsgTips.Designer.cs">
      <DependentUpon>MsgTips.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="View\MsgTips.resx">
      <DependentUpon>MsgTips.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="ISRPA\RuntimeEnv\Plugin\1.0.1.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="ISRPA\RuntimeEnv\PublicComponents2022\1.0.1.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="nlog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Version.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.2 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autoupdater.NET.Official">
      <Version>1.9.0</Version>
    </PackageReference>
    <PackageReference Include="EPPlus">
      <Version>6.2.19</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client">
      <Version>8.0.8</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>5.3.4</Version>
    </PackageReference>
    <PackageReference Include="Polly">
      <Version>5.9.0</Version>
    </PackageReference>
    <PackageReference Include="SunnyUI">
      <Version>3.6.8.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Common\c.ico" />
    <None Include="Common\c_running.ico" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>