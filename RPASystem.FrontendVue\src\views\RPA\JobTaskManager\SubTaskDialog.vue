<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'子任务列表 - ' + parentTaskName"
    width="90%"
    destroy-on-close
  >
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="资源机">
        <el-input 
          v-model="searchForm.resourceMachine" 
          placeholder="请输入资源机" 
          clearable 
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select 
          v-model="searchForm.status" 
          multiple 
          placeholder="请选择状态" 
          clearable 
          style="width: 300px;"
        >
          <el-option label="待运行" value="Pending" />
          <el-option label="运行中" value="Running" />
          <el-option label="成功" value="Success" />
          <el-option label="失败" value="Failed" />
          <el-option label="已取消" value="Cancelled" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="jobTaskId" label="任务ID" width="70" />
      <el-table-column prop="jobTaskName" label="任务名称" min-width="170" show-overflow-tooltip />
      <el-table-column prop="exeProgramName" label="程序名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="priority" label="优先级" width="70" align="center" />
      <!-- <el-table-column prop="createdAt" label="创建时间" width="85" show-overflow-tooltip /> -->
      <el-table-column prop="startTime" label="开始时间" width="160" show-overflow-tooltip />
      <el-table-column label="运行时长" width="90" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip 
            v-if="scope.row.endTime"
            :content="`结束时间: ${scope.row.endTime}`"
            placement="top"
            effect="light"
          >
            <span>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
          </el-tooltip>
          <span v-else>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="resourceSelection" label="资源选择" width="100" show-overflow-tooltip />
      <el-table-column prop="assignedResourceMachine" label="资源机" width="120" show-overflow-tooltip />
      <el-table-column prop="inputParameters" label="输入参数" width="100" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip 
            v-if="hasInputFile(scope.row.inputParameters)"
            :content="scope.row.inputParameters"
            placement="top"
            effect="light"
          >
            <span 
              class="clickable-text"
              @click="handleInputFileDownload(scope.row.inputParameters)"
            >
              下载输入件
            </span>
          </el-tooltip>
          <span v-else>{{ scope.row.inputParameters }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="outputResults" label="输出结果" width="80" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip 
            v-if="hasReturnResult(scope.row.outputResults)"
            :content="scope.row.outputResults"
            placement="top"
            effect="light"
          >
            <span 
              class="clickable-text"
              @click="copyReturnResult(scope.row.outputResults)"
            >
              复制结果
            </span>
          </el-tooltip>
          <span v-else>{{ scope.row.outputResults }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="outputFile" label="输出文件" width="100" show-overflow-tooltip>
        <template #default="scope">
          <span
            v-if="scope.row.outputFile && isNetworkSharePath(scope.row.outputFile)"
            class="clickable-text"
            @click="openDirectory(scope.row.outputFile)"
            :title="scope.row.outputFile"
          >
            打开目录
          </span>
          <span v-else-if="scope.row.outputFile" :title="scope.row.outputFile">
            {{ scope.row.outputFile }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="retryCount" label="重试数" width="80" align="center">
        <template #default="scope">
          <span>{{ scope.row.retryCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="110">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="notes" label="备注" min-width="100" show-overflow-tooltip />
      <el-table-column label="操作" width="80" fixed="right">
        <template #default="scope">
          <el-tooltip content="重试" placement="top" effect="light">
            <el-button 
              v-if="scope.row.status !== 'Pending' && scope.row.status !== 'Running'" 
              type="primary" 
              :icon="RefreshRight" 
              circle 
              size="small" 
              @click="retryJobTask(scope.row.jobTaskId)" 
            />
          </el-tooltip>
          <el-tooltip content="停止" placement="top" effect="light">
            <el-button 
              v-if="scope.row.status === 'Running' || scope.row.status === 'Pending'" 
              type="warning" 
              :icon="VideoPause" 
              circle 
              size="small" 
              @click="stopJobTask(scope.row.jobTaskId)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.pageNumber"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RefreshRight, VideoPause } from '@element-plus/icons-vue'
import { copyToClip } from '@/utils/clipboard'
import { retrySubTask, stopSubTask, downloadInputFile } from '@/api/RPA/SubTaskManager'
import axios from 'axios'

// 接收父组件传递的属性
const props = defineProps({
  parentTaskId: {
    type: Number,
    required: true
  },
  parentTaskName: {
    type: String,
    required: true
  },
  visible: {
    type: Boolean,
    required: true
  }
})

// 向父组件发送事件
const emit = defineEmits(['update:visible'])

// 对话框可见性计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 搜索表单
const searchForm = ref({
  resourceMachine: '',
  status: []
})

// 分页配置
const pagination = ref({
  pageNumber: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const tableData = ref([])

// 获取子任务数据
const fetchSubTasks = async () => {
  try {
    // 使用URLSearchParams正确处理数组参数
    const params = new URLSearchParams()
    params.append('parentTaskId', props.parentTaskId)
    params.append('resourceMachine', searchForm.value.resourceMachine)
    // 正确处理数组参数
    searchForm.value.status.forEach(s => {
      params.append('status', s)
    })
    params.append('pageNumber', pagination.value.pageNumber)
    params.append('pageSize', pagination.value.pageSize)

    const response = await axios.get('/api/jobtask/subtasks', { params })
    tableData.value = response.data.items
    pagination.value.total = response.data.totalCount
  } catch (error) {
    console.error('获取子任务列表失败:', error)
    ElMessage.error('获取子任务列表失败')
  }
}

// 搜索和重置
const handleSearch = () => {
  pagination.value.pageNumber = 1
  fetchSubTasks()
}

const resetSearch = () => {
  searchForm.value = {
    resourceMachine: '',
    status: []
  }
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.pageNumber = 1
  fetchSubTasks()
}

const handlePageChange = (page) => {
  pagination.value.pageNumber = page
  fetchSubTasks()
}

// 任务操作
const retryJobTask = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要重试该任务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await retrySubTask(id)
    ElMessage.success('已开始重试任务')
    await fetchSubTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试任务失败:', error)
      ElMessage.error('重试任务失败')
    }
  }
}

const stopJobTask = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要停止该任务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await stopSubTask(id)
    ElMessage.success('已发送停止指令')
    await fetchSubTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止任务失败:', error)
      ElMessage.error('停止任务失败')
    }
  }
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    'Pending': 'info',
    'Running': 'primary',
    'Success': 'success',
    'Failed': 'danger',
    'Cancelled': 'warning'
  }
  return statusMap[status] || 'info'
}

const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return ''

  const start = new Date(startTime)
  const end = new Date(endTime)
  const diffInSeconds = Math.floor((end - start) / 1000)
  
  if (diffInSeconds < 0) return ''
  
  const days = Math.floor(diffInSeconds / (24 * 3600))
  const hours = Math.floor((diffInSeconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((diffInSeconds % 3600) / 60)
  const seconds = diffInSeconds % 60
  
  if (days === 0 && hours === 0 && minutes === 0) {
    return `${seconds}秒`
  }
  
  if (days === 0 && hours === 0) {
    return minutes === 0 ? `${seconds}秒` : `${minutes}分${seconds}秒`
  }
  
  if (days === 0) {
    return `${hours}时${minutes}分${seconds}秒`
  }
  
  return `${days}天${hours}时${minutes}分${seconds}秒`
}

const hasInputFile = (inputParameters) => {
  try {
    const params = JSON.parse(inputParameters || '{}')
    return params.InputFile && params.InputFile !== ''
  } catch {
    return false
  }
}

const handleInputFileDownload = async (inputParameters) => {
  try {
    const params = JSON.parse(inputParameters || '{}')
    if (params.InputFile) {
      const fileId = params.InputFile
      
      const response = await downloadInputFile(fileId)
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      
      const now = new Date()
      const fileName = now.getFullYear() +
        String(now.getMonth() + 1).padStart(2, '0') +
        String(now.getDate()).padStart(2, '0') +
        String(now.getHours()).padStart(2, '0') +
        String(now.getMinutes()).padStart(2, '0') +
        String(now.getSeconds()).padStart(2, '0') +
        '.xlsx'
      
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败，请重试')
  }
}

const hasReturnResult = (outputResults) => {
  try {
    const result = JSON.parse(outputResults || '{}')
    return result.ReturnResult !== undefined
  } catch {
    return false
  }
}

const copyReturnResult = (outputResults) => {
  try {
    const result = JSON.parse(outputResults || '{}')
    if (result.ReturnResult !== undefined) {
      copyToClip(result.ReturnResult)
    }
  } catch (error) {
    console.error('解析输出结果失败:', error)
    ElMessage.error('解析输出结果失败')
  }
}

// 检查是否是网络共享路径
const isNetworkSharePath = (path) => {
  if (!path) return false

  // 转换为小写以便不区分大小写比较
  const lowerPath = path.toLowerCase()

  // 检查常见的网络共享路径格式:
  // - Windows UNC路径 (\\server\share)
  // - 网络协议 (smb://, ftp://, http://)
  // - 网络路径 (//server/share)
  // - 映射的网络驱动器 (z:\folder 其中z不是c,d,e等本地驱动器)

  if (lowerPath.startsWith('\\\\') || lowerPath.startsWith('//')) {
    return true
  }

  if (lowerPath.includes('://')) {
    return true
  }

  // 检查是否是网络驱动器映射 (假设C-F是本地驱动器)
  const driveLetterMatch = lowerPath.match(/^([a-z]):\\/i)
  if (driveLetterMatch) {
    const driveLetter = driveLetterMatch[1].toLowerCase()
    // 假设g-z是网络驱动器
    if (driveLetter >= 'g' && driveLetter <= 'z') {
      return true
    }
  }

  return false
}

// 打开目录处理函数
const openDirectory = (path) => {
  if (!path) return

  try {
    // 格式化路径成自定义协议
    const protocolPath = 'opendir:' + path
    console.log('Opening directory:', protocolPath)
    window.location.href = protocolPath
  } catch (error) {
    console.error('打开目录失败:', error)
    ElMessage.error('打开目录失败')
  }
}

// 初始化
onMounted(() => {
  fetchSubTasks()
})
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.clickable-text {
  color: #409EFF;
  cursor: pointer;
}

.clickable-text:hover {
  text-decoration: underline;
}

:deep(.el-table .cell) {
  display: flex;
  align-items: center;
}

:deep(.el-tag) {
  min-width: 80px;
  text-align: center;
}
</style>
