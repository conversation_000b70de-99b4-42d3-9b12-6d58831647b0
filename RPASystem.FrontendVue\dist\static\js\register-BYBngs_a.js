import{r as f,E as V,e as x,c as h,i as e,j as n,l as r,k as t,F as M,s as E,a as H,y as k,h as K,x as d,o as g,t as C,z as w,p as L,q as N,A as O,D as S,G as j,H as D,v as I}from"./index-CX4J5aM5.js";import $ from"./starBackground-BMTLJT3n.js";import A from"./oauthLogin-Bj3K6qfP.js";const G={class:"login-wrap"},J={class:"login"},Q={class:"title"},W={class:"register-code ml10"},X=["src"],Y={key:0},Z={key:1},ee={style:{"text-align":"center"}},te={class:"el-register-footer"},oe=["innerHTML"],se=E({name:"register"}),de=Object.assign(se,{setup(re){const{proxy:b}=I(),q=H(),y=f(""),s=V({username:"",password:"",confirmPassword:"",code:"",uuid:""}),P=f(null),c=f(!1),v=f(!0),R=V({username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:5,max:20,message:"用户账号长度必须介于 5 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 6 和 20 之间",trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,o,a)=>{s.password!==o?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]}),z=x(()=>k.copyright),B=x(()=>k.title);function _(){K().then(l=>{y.value="data:image/gif;base64,"+l.data.img,s.uuid=l.data.uuid})}function m(){b.$refs.registerFormRef.validate(l=>{l&&(c.value=!0,j(s).then(o=>{if(o.code==200){const a=s.username;D.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{q.push("/login")}).catch(()=>{})}}).catch(()=>{c.value=!1,v.value&&_()}))})}return _(),(l,o)=>{const a=d("svg-icon"),p=d("el-input"),u=d("el-form-item"),F=d("el-button"),T=d("router-link"),U=d("el-form");return g(),h(M,null,[e($),n("div",G,[n("div",J,[e(U,{ref_key:"registerFormRef",ref:P,model:t(s),rules:t(R),class:"login-form"},{default:r(()=>[n("h3",Q,C(t(B)),1),e(u,{prop:"username"},{default:r(()=>[e(p,{modelValue:t(s).username,"onUpdate:modelValue":o[0]||(o[0]=i=>t(s).username=i),type:"text",size:"default","auto-complete":"off",placeholder:"账号"},{prefix:r(()=>[e(a,{name:"user"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"password"},{default:r(()=>[e(p,{modelValue:t(s).password,"onUpdate:modelValue":o[1]||(o[1]=i=>t(s).password=i),type:"password",size:"default","auto-complete":"off",placeholder:"密码",onKeyup:w(m,["enter"])},{prefix:r(()=>[e(a,{name:"password"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"confirmPassword"},{default:r(()=>[e(p,{modelValue:t(s).confirmPassword,"onUpdate:modelValue":o[2]||(o[2]=i=>t(s).confirmPassword=i),type:"password",size:"default","auto-complete":"off",placeholder:"确认密码",onKeyup:w(m,["enter"])},{prefix:r(()=>[e(a,{name:"password"})]),_:1},8,["modelValue"])]),_:1}),t(v)?(g(),L(u,{key:0,prop:"code"},{default:r(()=>[e(p,{modelValue:t(s).code,"onUpdate:modelValue":o[3]||(o[3]=i=>t(s).code=i),"auto-complete":"off",size:"default",placeholder:"验证码",style:{width:"63%"},onKeyup:w(m,["enter"])},{prefix:r(()=>[e(a,{name:"validCode"})]),_:1},8,["modelValue"]),n("div",W,[n("img",{src:t(y),onClick:_,class:"register-code-img"},null,8,X)])]),_:1})):N("",!0),e(u,{style:{width:"100%"}},{default:r(()=>[e(F,{loading:t(c),type:"primary",size:"default",round:"",style:{width:"100%"},onClick:O(m,["prevent"])},{default:r(()=>[t(c)?(g(),h("span",Z,"注 册 中...")):(g(),h("span",Y,C(l.$t("login.register")),1))]),_:1},8,["loading"])]),_:1}),n("div",ee,[e(T,{class:"link-type",to:"/login"},{default:r(()=>o[4]||(o[4]=[S("使用已有账户登录")])),_:1})])]),_:1},8,["model","rules"]),e(A)]),n("div",te,[n("div",{innerHTML:t(z)},null,8,oe)])])],64)}}});export{de as default};
