import{i as a}from"./index-BtiuLXK9.js";import{_ as e,o as i,c as r,V as n,B as o}from"./index-CX4J5aM5.js";const s=3e3,l={props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},data(){return{chart:null}},mounted(){this.$nextTick(()=>{this.initChart()})},beforeDestroy(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart(){this.chart=a(this.$el),this.chart.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},radar:{radius:"66%",center:["50%","42%"],splitNumber:8,splitArea:{areaStyle:{color:"rgba(127,95,132,.3)",opacity:1,shadowBlur:45,shadowColor:"rgba(0,0,0,.5)",shadowOffsetX:0,shadowOffsetY:15}},indicator:[{name:"Sales",max:1e4},{name:"Administration",max:2e4},{name:"Information Techology",max:2e4},{name:"Customer Support",max:2e4},{name:"Development",max:2e4},{name:"Marketing",max:2e4}]},legend:{left:"center",bottom:"10",data:["Allocated Budget","Expected Spending","Actual Spending"]},series:[{type:"radar",symbolSize:0,areaStyle:{shadowBlur:13,shadowColor:"rgba(0,0,0,.2)",shadowOffsetX:0,shadowOffsetY:10,opacity:1},data:[{value:[5e3,7e3,12e3,11e3,15e3,14e3],name:"Allocated Budget"},{value:[4e3,9e3,15e3,15e3,13e3,11e3],name:"Expected Spending"},{value:[5500,11e3,12e3,15e3,12e3,12e3],name:"Actual Spending"}],animationDuration:s}]})}}};function d(h,c,t,m,u,p){return i(),r("div",{class:n(t.className),style:o({height:t.height,width:t.width})},null,6)}const x=e(l,[["render",d]]);export{x as default};
