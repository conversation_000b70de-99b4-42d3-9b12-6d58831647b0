import{i as n}from"./index-BtiuLXK9.js";import{_ as r,r as s,M as l,o as c,c as f}from"./index-CX4J5aM5.js";const p={__name:"lineEcharts",setup(d){const a=s(null),i={title:{text:"在线人数统计",textStyle:{color:"#00e4ff"}},grid:{top:"10%",left:"3%",right:"4%",bottom:"10%",containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},yAxis:{axisLabel:{color:"#bbdaff",margin:20},splitLine:{lineStyle:{color:"#fff"}}},xAxis:{splitLine:{show:!1},axisLine:{lineStyle:{color:"orange"}},type:"category",data:["周一","周二","周三","周四","周五","周六","周日"],axisLabel:{color:"#bbdaff",margin:20},boundaryGap:!1,axisTick:{show:!1}},series:[{name:"在线人数",itemStyle:{color:"orange",lineStyle:{color:"#FF005A",width:2}},symbol:"circle",markLine:{silent:!0},type:"line",data:[154,230,224,218,135,147,260],animationDuration:2800,animationEasing:"cubicInOut"}]};let e=null;const o=()=>{const t=n(a.value);return t.setOption(i),t};return l(()=>{e=o(),window.addEventListener("resize",function(){e&&e.resize()})}),(t,u)=>(c(),f("div",{class:"echarts",ref_key:"chartsRef",ref:a},null,512))}},_=r(p,[["__scopeId","data-v-f517d76e"]]);export{_ as default};
