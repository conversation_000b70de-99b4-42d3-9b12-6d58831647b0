import{_ as u,r as n,M as f,o as a,c as e,F as i,K as m,k as p,s as g,v as h}from"./index-CX4J5aM5.js";const $={class:"stars"},k=g({name:"starBackground"}),x=Object.assign(k,{setup(M){const o=n(600),c=n(800),{proxy:d}=h();f(()=>{_()});function _(){d.$refs.star.forEach(r=>{let s=.2+Math.random()*1,t=c.value+Math.random()*300;r.style.transformOrigin=`0 0 ${t}px`,r.style.transform=`translate3d(0, 0, -${t}px) rotateY(${Math.random()*360}deg) rotateX(${Math.random()*-50}deg) scale(${s}, ${s})`})}return(l,r)=>(a(),e("div",$,[(a(!0),e(i,null,m(p(o),(s,t)=>(a(),e("div",{class:"star",key:t,ref_for:!0,ref:"star"}))),128))]))}}),y=u(x,[["__scopeId","data-v-991326b0"]]);export{y as default};
