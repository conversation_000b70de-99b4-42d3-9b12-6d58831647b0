import{Q as g,_ as M,r as f,E as O,M as P,x as c,N as q,o as U,c as A,j as h,i as a,l as s,D as v,O as Q,p as H,s as J,P as r,H as B}from"./index-CX4J5aM5.js";function G(n){return g.get("/api/filestorage/list",{params:n})}function K(n){return g.post("/api/filestorage/upload",n)}function W(n){return g.get(`/api/filestorage/${n}`,{responseType:"blob",headers:{Accept:"application/octet-stream"}})}function X(n,m){return g.put(`/api/filestorage/${n}/remark`,m,{headers:{"Content-Type":"application/json"}})}function Y(n){return g.delete(`/api/filestorage/${n}`)}function Z(n){return g.post("/api/filestorage/batchdelete",n)}const ee={class:"file-management"},te={class:"upload-area"},ae={class:"operation-area"},le={class:"search-area"},oe={class:"batch-operation"},ne=J({name:"filemanagement"}),ie=Object.assign(ne,{setup(n){const m=f(!1),k=f([]),_=f(""),y=f(0),x=f([]),V=f(!0),o=O({pageIndex:1,pageSize:10,fileName:"",fileExtension:""});function p(){m.value=!0,G({pageIndex:o.pageIndex,pageSize:o.pageSize,searchFileName:o.fileName,searchFileExtension:o.fileExtension}).then(t=>{k.value=t.data.items,y.value=t.data.total,m.value=!1}).catch(t=>{console.error("加载文件列表失败:",t),r.error("加载文件列表失败,请重试"),m.value=!1})}function C(){o.pageIndex=1,p()}function E(t){const e=new FormData;e.append("file",t.file),e.append("remark",_.value),K(e).then(i=>{t.onSuccess(i.data)}).catch(i=>{t.onError(i)})}function N(){p(),_.value="",r.success("上传成功")}function D(){r.error("文件上传失败,请重试")}function S(t){W(t.id).then(e=>{const i=window.URL.createObjectURL(new Blob([e.data])),u=document.createElement("a");u.href=i,u.setAttribute("download",`${t.fileName}${t.fileExtension}`),document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(i)}).catch(e=>{console.error("下载文件失败:",e),r.error("下载文件失败,请重试")})}function F(t){X(t.id,JSON.stringify(t.remark)).then(()=>{p()}).catch(e=>{console.error("更新备注失败:",e),r.error("更新备注失败,请重试")})}function T(t){x.value=t.map(e=>e.id),V.value=!t.length}function $(t){B.confirm("确定要删除这个文件吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Y(t.id).then(()=>{p(),r.success("删除成功")}).catch(e=>{console.error("删除文件失败:",e),r.error("删除文件失败，请重试")})}).catch(()=>{r.info("已取消删除")})}function z(){x.value.length&&B.confirm(`确定要删除选中的 ${x.value.length} 个文件吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z(x.value).then(()=>{p(),r.success("批量删除成功")}).catch(t=>{console.error("批量删除文件失败:",t),r.error("批量删除文件失败，请重试")})}).catch(()=>{r.info("已取消删除")})}return P(()=>{p()}),(t,e)=>{const i=c("el-button"),u=c("el-upload"),w=c("el-input"),I=c("el-divider"),d=c("el-table-column"),R=c("el-table"),L=c("pagination"),j=q("loading");return U(),A("div",ee,[h("div",te,[a(u,{class:"upload-demo","http-request":E,"on-success":N,"on-error":D,style:{"margin-right":"20px"}},{tip:s(()=>e[7]||(e[7]=[h("div",{class:"el-upload__tip",style:{display:"inline-block","margin-left":"10px"}},"上传文件建议不要超过30MB",-1)])),default:s(()=>[a(i,{size:"small",type:"primary"},{default:s(()=>e[6]||(e[6]=[v("点击上传")])),_:1})]),_:1}),a(w,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=l=>_.value=l),placeholder:"上传文件备注（可选）",style:{width:"300px"}},null,8,["modelValue"])]),a(I),h("div",ae,[h("div",le,[a(w,{modelValue:o.fileName,"onUpdate:modelValue":e[1]||(e[1]=l=>o.fileName=l),placeholder:"搜索文件名",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),a(w,{modelValue:o.fileExtension,"onUpdate:modelValue":e[2]||(e[2]=l=>o.fileExtension=l),placeholder:"搜索扩展名",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),a(i,{type:"primary",onClick:C},{default:s(()=>e[8]||(e[8]=[v("搜索")])),_:1})]),h("div",oe,[a(i,{type:"danger",disabled:V.value,onClick:z},{default:s(()=>e[9]||(e[9]=[v(" 批量删除 ")])),_:1},8,["disabled"])])]),Q((U(),H(R,{data:k.value,style:{width:"100%","margin-top":"20px"},onSelectionChange:T},{default:s(()=>[a(d,{type:"selection",width:"55"}),a(d,{prop:"id",label:"ID",width:"80"}),a(d,{prop:"fileName",label:"文件名",width:"180","show-overflow-tooltip":""}),a(d,{prop:"fileExtension",label:"扩展名",width:"100"}),a(d,{prop:"uploadTime",label:"上传时间",width:"180"}),a(d,{prop:"modifyTime",label:"修改时间",width:"180"}),a(d,{prop:"remark",label:"备注","min-width":"200"},{default:s(l=>[a(w,{modelValue:l.row.remark,"onUpdate:modelValue":b=>l.row.remark=b,onBlur:b=>F(l.row)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1}),a(d,{label:"操作",width:"180"},{default:s(l=>[a(i,{size:"small",onClick:b=>S(l.row)},{default:s(()=>e[10]||(e[10]=[v("下载")])),_:2},1032,["onClick"]),a(i,{size:"small",type:"danger",onClick:b=>$(l.row)},{default:s(()=>e[11]||(e[11]=[v(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[j,m.value]]),a(L,{total:y.value,"onUpdate:total":e[3]||(e[3]=l=>y.value=l),page:o.pageIndex,"onUpdate:page":e[4]||(e[4]=l=>o.pageIndex=l),limit:o.pageSize,"onUpdate:limit":e[5]||(e[5]=l=>o.pageSize=l),onPagination:p},null,8,["total","page","limit"])])}}}),se=M(ie,[["__scopeId","data-v-d70a59b3"]]);export{se as default};
