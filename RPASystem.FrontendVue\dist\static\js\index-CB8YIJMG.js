import{ai as G,_ as L,aN as S,r as k,w as T,x as p,o as a,c as i,i as n,l as d,k as s,p as z,j as t,D as v,t as o,q as r,F,K as V,s as $,v as E}from"./index-CX4J5aM5.js";function j(){return G({url:"/monitor/server",method:"get"})}const D={class:"app-container"},U={class:"card-header"},q={class:"col-item"},K={class:"content"},O={key:0,class:"footer"},H={class:"col-item"},J={style:{"font-size":"12px"}},Q={style:{padding:"3px"}},W={style:{padding:"3px"}},X={style:{padding:"3px"}},Y={class:"content"},Z={class:"footer"},tt={class:"col-item"},et={class:"content"},lt={key:0,class:"footer"},st={class:"title"},ot={class:"content"},nt={style:{"font-size":"12px"}},dt={style:{padding:"3px"}},at={style:{padding:"3px"}},it={style:{padding:"3px"}},rt={class:"content"},pt={class:"footer"},ut={class:"el-table el-table--enable-row-hover el-table--medium"},ct={key:0,cellspacing:"0",style:{width:"100%"}},vt={class:"cell"},_t={class:"cell"},mt={class:"cell"},gt={class:"cell"},yt={class:"cell"},ft={class:"el-table el-table--enable-row-hover el-table--medium"},bt={cellspacing:"0",style:{width:"100%"}},xt={key:0},ht={key:0},kt={key:0},Rt={key:0},wt={key:0},Nt={key:0},Bt={key:0},Ct={key:0},Pt=$({name:"server"}),Mt=Object.assign(Pt,{setup(At){S(u=>{C()});const R=k(0),l=k([]),_=k(null),{proxy:f}=E();function b(){j().then(u=>{l.value=u.data,f.$modal.closeLoading()}).catch(u=>{f.$modal.closeLoading()})}function N(){f.$modal.loading("拼命读取中")}function B(){_.value==null&&(_.value=setInterval(()=>{b()},1e4))}function C(){clearInterval(_.value),_.value=null}return T(()=>l.value,u=>{if(u&&u.app){const e=u.app.appRAM.replace(" MB",""),x=u.cpu.totalRAM.replace("GB","")*1024,h=e/x;R.value=h.toFixed(2)}},{immediate:!0}),b(),N(),B(),(u,e)=>{const x=p("Cpu"),h=p("el-button"),m=p("el-progress"),w=p("el-tooltip"),g=p("el-card"),y=p("el-col"),P=p("Files"),M=p("Monitor"),A=p("Platform"),I=p("el-row");return a(),i("div",D,[n(I,null,{default:d(()=>[s(l).cpu?(a(),z(y,{key:0,lg:24,class:"card-box"},{default:d(()=>[n(g,{class:"box-card"},{header:d(()=>[t("div",U,[t("div",null,[n(x,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[1]||(e[1]=v()),e[2]||(e[2]=t("span",{style:{"vertical-align":"middle"}},"CPU/内存",-1))]),n(h,{text:"",onClick:e[0]||(e[0]=c=>b())},{default:d(()=>e[3]||(e[3]=[v("刷新")])),_:1})])]),default:d(()=>[t("div",q,[e[4]||(e[4]=t("div",{class:"title"},"CPU使用率",-1)),t("div",K,[n(m,{type:"dashboard",percentage:parseFloat(s(l).cpu.cpuRate)},null,8,["percentage"])]),s(l).sys?(a(),i("div",O,o(s(l).sys.cpuNum)+" 核心",1)):r("",!0)]),t("div",H,[e[5]||(e[5]=t("div",{class:"title"},"内存使用率",-1)),n(w,{placement:"top-end"},{content:d(()=>[t("div",J,[t("div",Q,"总量："+o(s(l).cpu.totalRAM),1),t("div",W,"已使用："+o(s(l).cpu.usedRam),1),t("div",X,"空闲："+o(s(l).cpu.freeRam),1)])]),default:d(()=>[t("div",Y,[n(m,{type:"dashboard",percentage:parseFloat(s(l).cpu.ramRate)},null,8,["percentage"])])]),_:1}),t("div",Z,o(s(l).cpu.usedRam)+" / "+o(s(l).cpu.totalRAM),1)]),t("div",tt,[e[6]||(e[6]=t("div",{class:"title"},".NETCore服务",-1)),t("div",et,[n(m,{type:"dashboard",percentage:parseFloat(s(R))},null,8,["percentage"])]),s(l).app?(a(),i("div",lt,o(s(l).app.appRAM),1)):r("",!0)])]),_:1})]),_:1})):r("",!0),n(y,{lg:24,class:"card-box"},{default:d(()=>[n(g,null,{header:d(()=>[n(P,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[7]||(e[7]=v()),e[8]||(e[8]=t("span",{style:{"vertical-align":"middle"}},"磁盘状态",-1))]),default:d(()=>[(a(!0),i(F,null,V(s(l).disk,c=>(a(),i("div",{class:"col-item",key:c.diskName},[t("div",st,o(c.diskName)+"盘使用率",1),t("div",ot,[n(w,{placement:"top-end"},{content:d(()=>[t("div",nt,[t("div",dt,"总量："+o(c.totalSize)+"GB",1),t("div",at,"空闲："+o(c.availableFreeSpace)+"GB",1),t("div",it,"已用："+o(c.used)+"GB",1)])]),default:d(()=>[t("div",rt,[n(m,{type:"dashboard",percentage:parseFloat(c.availablePercent)},null,8,["percentage"])])]),_:2},1024)]),t("div",pt,o(c.availableFreeSpace)+"GB可用，共"+o(c.totalSize)+"GB",1)]))),128))]),_:1})]),_:1}),n(y,{lg:24,class:"card-box"},{default:d(()=>[n(g,null,{header:d(()=>[n(M,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[9]||(e[9]=v()),e[10]||(e[10]=t("span",{style:{"vertical-align":"middle"}},"服务器信息",-1))]),default:d(()=>[t("div",ut,[s(l).sys?(a(),i("table",ct,[t("tbody",null,[t("tr",null,[e[11]||(e[11]=t("td",null,[t("div",{class:"cell"},"服务器名称")],-1)),t("td",null,[t("div",vt,o(s(l).sys.computerName),1)]),e[12]||(e[12]=t("td",null,[t("div",{class:"cell"},"操作系统")],-1)),t("td",null,[t("div",_t,o(s(l).sys.osName),1)])]),t("tr",null,[e[13]||(e[13]=t("td",null,[t("div",{class:"cell"},"服务器IP")],-1)),t("td",null,[t("div",mt,o(s(l).sys.serverIP),1)]),e[14]||(e[14]=t("td",null,[t("div",{class:"cell"},"系统架构")],-1)),t("td",null,[t("div",gt,o(s(l).sys.osArch),1)])]),t("tr",null,[e[15]||(e[15]=t("td",null,[t("div",{class:"cell"},"系统运行时长")],-1)),t("td",null,[t("div",yt,o(s(l).sys.runTime),1)]),e[16]||(e[16]=t("td",null,[t("div",{class:"cell"})],-1)),e[17]||(e[17]=t("td",null,[t("div",{class:"cell"})],-1))])])])):r("",!0)])]),_:1})]),_:1}),n(y,{lg:24,class:"card-box"},{default:d(()=>[n(g,null,{header:d(()=>[n(A,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[18]||(e[18]=v()),e[19]||(e[19]=t("span",{style:{"vertical-align":"middle"}},".NET信息",-1))]),default:d(()=>[t("div",ft,[t("table",bt,[t("tbody",null,[t("tr",null,[e[20]||(e[20]=t("td",null,[t("div",{class:"cell"},"环境变量")],-1)),t("td",null,[s(l).app?(a(),i("div",xt,o(s(l).app.name),1)):r("",!0)]),e[21]||(e[21]=t("td",null,[t("div",{class:"cell"},".Net版本")],-1)),t("td",null,[s(l).app?(a(),i("div",ht,o(s(l).app.version),1)):r("",!0)])]),t("tr",null,[e[22]||(e[22]=t("td",null,[t("div",{class:"cell"},"启动时间")],-1)),t("td",null,[s(l).app?(a(),i("div",kt,o(s(l).app.startTime),1)):r("",!0)]),e[23]||(e[23]=t("td",null,[t("div",{class:"cell"},"运行时长")],-1)),t("td",null,[s(l).app?(a(),i("div",Rt,o(s(l).app.runTime),1)):r("",!0)])]),t("tr",null,[e[24]||(e[24]=t("td",null,[t("div",{class:"cell"},"占用内存")],-1)),t("td",null,[s(l).app?(a(),i("div",wt,o(s(l).app.appRAM),1)):r("",!0)]),e[25]||(e[25]=t("td",null,[t("div",{class:"cell"},"启动地址")],-1)),t("td",null,[s(l).app?(a(),i("div",Nt,o(s(l).app.host),1)):r("",!0)])]),t("tr",null,[e[26]||(e[26]=t("td",null,[t("div",{class:"cell"},"ContentRootPath")],-1)),t("td",null,[s(l).app?(a(),i("div",Bt,o(s(l).app.rootPath),1)):r("",!0)]),e[27]||(e[27]=t("td",null,[t("div",{class:"cell"},"webPath")],-1)),t("td",null,[s(l).app?(a(),i("div",Ct,o(s(l).app.webRootPath),1)):r("",!0)])])])])])]),_:1})]),_:1})]),_:1})])}}}),Gt=L(Mt,[["__scopeId","data-v-3486fb5a"]]);export{Gt as default};
